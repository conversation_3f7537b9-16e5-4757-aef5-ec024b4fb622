from flask import Flask, send_from_directory, redirect, url_for
from flask_cors import CORS
from app.api.chat import chat_bp
from app.api.auth import auth_bp
from app.api.profile import profile_bp
from app.api.intent import intent_bp
from app.api.doctor import doctor_bp
import os
import sys

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.dirname(os.path.dirname(__file__))))

from app.configs.config import get_config
from app.configs.logging_config import setup_logging
from app.models.database import init_db
from app.commands import register_commands

def create_app():
    """Initialize the Flask application."""
    config = get_config()
    
    app = Flask(__name__, static_folder='frontend')
    app.config.from_object(config)
    
    # Set up logging
    setup_logging(app)
    
    # Initialize database
    init_db(app)
    
    # Register CLI commands
    register_commands(app)
    
    # Enable CORS
    CORS(app)
    
    # Register blueprints
    app.register_blueprint(chat_bp)
    app.register_blueprint(auth_bp)
    app.register_blueprint(profile_bp)
    app.register_blueprint(intent_bp)
    app.register_blueprint(doctor_bp)
    
    # Serve frontend
    @app.route('/', defaults={'path': ''})
    def index(path):
        return send_from_directory(app.static_folder, 'index.html')
        
    @app.route('/chat')
    def chat():
        return send_from_directory(app.static_folder, 'chat.html')
        
    @app.route('/doctor-dashboard')
    def doctor_dashboard():
        return send_from_directory(app.static_folder + '/components/doctor', 'dashboard.html')
        
    @app.route('/components/<path:path>')
    def serve_components(path):
        component_path = os.path.join('components', path)
        if os.path.exists(os.path.join(app.static_folder, component_path)):
            directory, filename = os.path.split(component_path)
            return send_from_directory(os.path.join(app.static_folder, directory), filename)
        else:
            return redirect('/')
        
    @app.route('/<path:path>')
    def serve(path):
        if os.path.exists(os.path.join(app.static_folder, path)):
            return send_from_directory(app.static_folder, path)
        else:
            return redirect('/')
    
    # Health check endpoint
    @app.route('/health')
    def health_check():
        return {
            'status': 'ok',
            'app_name': app.config.get('APP_NAME')
        }
    
    return app

if __name__ == '__main__':
    app = create_app()
    app.run(debug=app.config.get('DEBUG', True), port=5000) 