/* 聊天组件样式 */

/* 加载指示器 */
.loading-indicator {
    display: flex;
    align-self: flex-start;
    margin-left: 3.25rem;
    margin-bottom: 1rem;
    padding: 0.75rem 1rem;
    border-radius: var(--radius-lg);
    border-bottom-left-radius: var(--radius-sm);
    background: var(--white);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
}

.dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--primary-color);
    margin: 0 3px;
    animation: bounce 1.4s infinite ease-in-out both;
}

.dot:nth-child(1) {
    animation-delay: -0.32s;
}

.dot:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes bounce {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1.2);
        opacity: 1;
    }
}

/* 空状态消息 */
.empty-state-message {
    text-align: center;
    color: var(--gray-500);
    font-size: 1rem;
    margin: 2rem 0;
    padding: 2rem;
    background: var(--white);
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-200);
} 