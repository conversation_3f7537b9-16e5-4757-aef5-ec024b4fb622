/* 聊天布局样式 */

/* 聊天容器 */
.chat-container {
    flex: 1;
    width: 100%;
    max-width: 900px;
    margin: 0 auto;
    background: transparent;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
}

/* 聊天消息区域 */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    scroll-behavior: smooth;
    position: relative;
}

/* 自定义滚动条 */
.chat-messages::-webkit-scrollbar {
    width: 5px;
}

.chat-messages::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: var(--radius-full);
}

.chat-messages::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: var(--radius-full);
    transition: background var(--duration-normal) ease;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
} 