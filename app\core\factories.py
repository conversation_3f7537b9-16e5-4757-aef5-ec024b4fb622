from typing import List, Dict, Any

from langchain_core.tools import BaseTool
from langchain_openai import ChatOpenAI

from app.configs.settings import api_settings, config as app_config
from app.core.tools.weather_tool import get_current_weather

EXTERNAL_TOOL_REGISTRY: Dict[str, BaseTool] = {
    "get_current_weather": get_current_weather,
}


def create_llm_instance(llm_config: Dict[str, Any] = None) -> ChatOpenAI:
    # llm_config is now optional and defaults to None
    user_config = llm_config or {}
    config = {
        "model": app_config.llm.name,
        "temperature": app_config.llm.temperature,
        **user_config,
    }
    return ChatOpenAI(
        model=config["model"],
        temperature=config["temperature"],
        api_key=api_settings.OPENAI_API_KEY,
        base_url=api_settings.OPENAI_BASE_URL
    )


def get_tools(tool_names: List[str]) -> List[BaseTool]:
    if tool_names:
        return [
            EXTERNAL_TOOL_REGISTRY[name]
            for name in tool_names
            if name in EXTERNAL_TOOL_REGISTRY
        ]
    else:
        return []
