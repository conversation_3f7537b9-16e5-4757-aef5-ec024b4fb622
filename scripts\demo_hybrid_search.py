"""混合搜索功能演示脚本

演示SRH-001混合搜索核心功能，展示如何处理包含专有名词的复杂查询
"""

import sys
import os
from typing import List, Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from langchain_core.documents import Document
from app.core.retrievers.ensemble_retriever import create_ensemble_retriever
from app.core.fusion.rrf_fusion import fuse_search_results
from app.core.search_engines.simple_search_engine import SimpleSearchEngine
from app.configs.settings import config


def setup_demo_data():
    """设置演示数据"""
    print("🔧 设置演示数据...")
    
    # 创建搜索引擎
    search_engine = SimpleSearchEngine({
        'index_name': 'medical_demo',
        'persist_directory': 'demo_data'
    })
    
    # 医学知识演示数据
    medical_documents = [
        {
            'content': '高血压是一种常见的心血管疾病，收缩压持续≥140mmHg或舒张压≥90mmHg。主要症状包括头痛、头晕、心悸等。',
            'chunk_id': 'hypertension_1',
            'source': '心血管疾病指南',
            'keywords': ['高血压', '心血管疾病', '收缩压', '舒张压', '头痛', '头晕']
        },
        {
            'content': '糖尿病是一组以高血糖为特征的代谢性疾病。1型糖尿病多见于青少年，2型糖尿病多见于成年人。',
            'chunk_id': 'diabetes_1',
            'source': '内分泌疾病手册',
            'keywords': ['糖尿病', '高血糖', '代谢性疾病', '1型糖尿病', '2型糖尿病']
        },
        {
            'content': '阿司匹林（Aspirin）是一种非甾体抗炎药（NSAID），具有解热、镇痛、抗炎和抗血小板聚集作用。',
            'chunk_id': 'aspirin_1',
            'source': '药物治疗学',
            'keywords': ['阿司匹林', 'Aspirin', 'NSAID', '抗炎药', '抗血小板']
        },
        {
            'content': '心肌梗死（MI）是冠状动脉急性闭塞导致心肌缺血性坏死。典型症状为持续性胸痛，可放射至左臂、颈部。',
            'chunk_id': 'mi_1',
            'source': '急诊医学',
            'keywords': ['心肌梗死', 'MI', '冠状动脉', '心肌坏死', '胸痛']
        },
        {
            'content': '心电图（ECG/EKG）是记录心脏电活动的检查方法，可诊断心律失常、心肌梗死等心脏疾病。',
            'chunk_id': 'ecg_1',
            'source': '心电图诊断学',
            'keywords': ['心电图', 'ECG', 'EKG', '心律失常', '心脏疾病']
        },
        {
            'content': '抗生素是用于治疗细菌感染的药物。青霉素是第一个被发现的抗生素，对革兰氏阳性菌有效。',
            'chunk_id': 'antibiotics_1',
            'source': '抗感染治疗',
            'keywords': ['抗生素', '细菌感染', '青霉素', '革兰氏阳性菌']
        },
        {
            'content': '冠心病是冠状动脉粥样硬化性心脏病的简称，主要表现为心绞痛和心肌梗死。',
            'chunk_id': 'chd_1',
            'source': '心血管疾病指南',
            'keywords': ['冠心病', '冠状动脉', '粥样硬化', '心绞痛', '心肌梗死']
        }
    ]
    
    # 添加文档到搜索引擎
    success = search_engine.add_documents(medical_documents)
    if success:
        print(f"✅ 成功添加 {len(medical_documents)} 个医学文档")
    else:
        print("❌ 添加文档失败")
    
    return search_engine


def demo_rrf_fusion():
    """演示RRF融合算法"""
    print("\n🔬 演示RRF融合算法")
    print("-" * 40)
    
    # 模拟向量搜索结果（按相似度排序）
    vector_results = [
        Document(page_content="高血压是一种常见的心血管疾病", metadata={"chunk_id": "hypertension_1", "vector_score": 0.95}),
        Document(page_content="冠心病是冠状动脉粥样硬化性心脏病", metadata={"chunk_id": "chd_1", "vector_score": 0.85}),
        Document(page_content="心肌梗死是冠状动脉急性闭塞导致心肌坏死", metadata={"chunk_id": "mi_1", "vector_score": 0.75})
    ]
    
    # 模拟关键词搜索结果（按匹配度排序）
    keyword_results = [
        Document(page_content="心肌梗死是冠状动脉急性闭塞导致心肌坏死", metadata={"chunk_id": "mi_1", "keyword_score": 3.2}),
        Document(page_content="高血压是一种常见的心血管疾病", metadata={"chunk_id": "hypertension_1", "keyword_score": 2.8}),
        Document(page_content="心电图可诊断心律失常、心肌梗死等心脏疾病", metadata={"chunk_id": "ecg_1", "keyword_score": 2.1})
    ]
    
    print("向量搜索结果:")
    for i, doc in enumerate(vector_results, 1):
        print(f"  {i}. {doc.page_content[:30]}... (分数: {doc.metadata.get('vector_score', 0)})")
    
    print("\n关键词搜索结果:")
    for i, doc in enumerate(keyword_results, 1):
        print(f"  {i}. {doc.page_content[:30]}... (分数: {doc.metadata.get('keyword_score', 0)})")
    
    # 使用RRF融合
    search_results = {
        'vector': vector_results,
        'keyword': keyword_results
    }
    
    weights = {'vector': 0.6, 'keyword': 0.4}
    fused_results = fuse_search_results(search_results, weights, k=60)
    
    print(f"\nRRF融合结果 (权重: 向量={weights['vector']}, 关键词={weights['keyword']}):")
    for i, doc in enumerate(fused_results, 1):
        rrf_score = doc.metadata.get('rrf_score', 0)
        sources = doc.metadata.get('fusion_sources', [])
        print(f"  {i}. {doc.page_content[:40]}...")
        print(f"     RRF分数: {rrf_score:.4f}, 来源: {', '.join(sources)}")


def demo_ensemble_retriever(search_engine):
    """演示混合检索器"""
    print("\n🔍 演示混合检索器")
    print("-" * 40)
    
    # 创建混合检索器
    retriever = create_ensemble_retriever(
        vector_weight=0.6,
        keyword_weight=0.4,
        rrf_k=60,
        top_k=5,
        search_engine_config={
            'type': 'simple',
            'simple': {'index_name': 'medical_demo'}
        }
    )
    
    # 测试查询
    test_queries = [
        "高血压的症状和治疗",
        "心肌梗死MI的诊断",
        "阿司匹林的作用机制",
        "ECG心电图检查方法"
    ]
    
    for query in test_queries:
        print(f"\n查询: '{query}'")
        try:
            # 注意：这里只演示关键词搜索部分，因为向量搜索需要实际的向量数据库
            keyword_results = search_engine.search(query, size=3)
            
            print(f"关键词搜索找到 {len(keyword_results)} 个结果:")
            for i, result in enumerate(keyword_results, 1):
                content = result.get('content', '')
                score = result.get('_score', 0)
                print(f"  {i}. {content[:50]}... (分数: {score})")
                
        except Exception as e:
            print(f"  ⚠️  搜索出错: {e}")


def demo_complex_medical_queries(search_engine):
    """演示复杂医学查询处理"""
    print("\n🏥 演示复杂医学查询处理")
    print("-" * 40)
    
    # 包含专有名词的复杂查询
    complex_queries = [
        {
            'query': '心肌梗死的ECG表现',
            'description': '包含医学缩写和专业术语'
        },
        {
            'query': 'NSAID类药物阿司匹林的副作用',
            'description': '包含药物分类和具体药名'
        },
        {
            'query': '高血压患者的收缩压和舒张压标准',
            'description': '包含具体医学指标'
        },
        {
            'query': '1型和2型糖尿病的区别',
            'description': '包含疾病分型'
        }
    ]
    
    for query_info in complex_queries:
        query = query_info['query']
        description = query_info['description']
        
        print(f"\n查询: '{query}'")
        print(f"特点: {description}")
        
        try:
            results = search_engine.search(query, size=3)
            print(f"找到 {len(results)} 个相关结果:")
            
            for i, result in enumerate(results, 1):
                content = result.get('content', '')
                score = result.get('_score', 0)
                source = result.get('source', '未知来源')
                print(f"  {i}. 来源: {source}")
                print(f"     内容: {content[:80]}...")
                print(f"     匹配分数: {score}")
                
        except Exception as e:
            print(f"  ⚠️  查询处理出错: {e}")


def demo_configuration():
    """演示配置系统"""
    print("\n⚙️  演示配置系统")
    print("-" * 40)
    
    print("当前混合搜索配置:")
    print(f"  启用状态: {config.hybrid_search.enabled}")
    print(f"  向量搜索权重: {config.hybrid_search.weights.vector}")
    print(f"  关键词搜索权重: {config.hybrid_search.weights.keyword}")
    print(f"  RRF参数k: {config.hybrid_search.rrf.k}")
    print(f"  返回文档数: {config.hybrid_search.results.top_k}")
    print(f"  搜索引擎类型: {config.hybrid_search.search_engine.type}")
    
    if config.hybrid_search.search_engine.type == 'simple':
        simple_config = config.hybrid_search.search_engine.simple
        print(f"  简单搜索引擎配置:")
        print(f"    索引名称: {simple_config.index_name}")
        print(f"    存储目录: {simple_config.persist_directory}")


def main():
    """主演示函数"""
    print("🚀 混合搜索功能演示")
    print("=" * 50)
    print("实现SRH-001混合搜索核心功能")
    print("支持语义搜索和关键词搜索的RRF融合")
    print("=" * 50)
    
    try:
        # 设置演示数据
        search_engine = setup_demo_data()
        
        # 演示配置系统
        demo_configuration()
        
        # 演示RRF融合算法
        demo_rrf_fusion()
        
        # 演示混合检索器
        demo_ensemble_retriever(search_engine)
        
        # 演示复杂医学查询
        demo_complex_medical_queries(search_engine)
        
        print("\n" + "=" * 50)
        print("🎉 混合搜索功能演示完成！")
        print("\n主要特性:")
        print("✅ 同时支持语义搜索和关键词搜索")
        print("✅ 使用RRF算法智能融合搜索结果")
        print("✅ 优化处理包含专有名词的复杂查询")
        print("✅ 支持灵活的权重配置")
        print("✅ 提供完整的配置管理系统")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
