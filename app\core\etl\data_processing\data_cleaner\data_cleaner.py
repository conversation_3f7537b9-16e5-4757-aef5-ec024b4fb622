"""数据清洗器主类

提供可配置的Pandas数据清洗流水线功能
"""

from typing import Dict, Any, List, Optional
import pandas as pd
from datetime import datetime

from .cleaning_steps import (
    CleaningStep,
    RegexCleaningStep,
    DropNaCleaningStep,
    FillNaCleaningStep,
    DropDuplicatesCleaningStep,
    ApplyFunctionCleaningStep
)


class DataCleaner:
    """数据清洗器
    
    提供可配置的数据清洗流水线，支持多种清洗步骤的组合使用
    """
    
    def __init__(self, cleaning_config: List[Dict[str, Any]] = None):
        """初始化数据清洗器
        
        Args:
            cleaning_config: 清洗步骤配置列表
        """
        self.cleaning_config = cleaning_config or []
        self.cleaning_steps = []
        self.cleaning_log = []
        
        # 清洗步骤类型映射
        self.step_type_mapping = {
            'regex': RegexCleaningStep,
            'dropna': DropNaCleaningStep,
            'fillna': FillNaCleaningStep,
            'drop_duplicates': DropDuplicatesCleaningStep,
            'apply': ApplyFunctionCleaningStep
        }
        
        # 构建清洗步骤
        self._build_cleaning_steps()
    
    def _build_cleaning_steps(self) -> None:
        """根据配置构建清洗步骤"""
        self.cleaning_steps = []
        
        for step_config in self.cleaning_config:
            step_type = step_config.get('type')
            
            if step_type not in self.step_type_mapping:
                print(f"警告: 不支持的清洗步骤类型: {step_type}")
                continue
            
            try:
                step_class = self.step_type_mapping[step_type]
                step = step_class(step_config)
                self.cleaning_steps.append(step)
            except Exception as e:
                print(f"警告: 创建清洗步骤 {step_type} 失败: {e}")
    
    def clean(self, df: pd.DataFrame, source_name: str = "Unknown") -> pd.DataFrame:
        """执行数据清洗流水线
        
        Args:
            df: 要清洗的DataFrame
            source_name: 数据源名称，用于日志记录
            
        Returns:
            清洗后的DataFrame
        """
        if df.empty:
            print(f"数据源 {source_name}: 输入DataFrame为空，跳过清洗")
            return df
        
        if not self.cleaning_steps:
            print(f"数据源 {source_name}: 没有配置清洗步骤，跳过清洗")
            return df
        
        print(f"\n开始清洗数据源: {source_name}")
        print(f"初始数据量: {len(df)} 条记录")
        
        # 重置清洗日志
        self.cleaning_log = []
        current_df = df.copy()
        
        # 逐步执行清洗步骤
        for i, step in enumerate(self.cleaning_steps, 1):
            step_start_time = datetime.now()
            initial_count = len(current_df)
            
            try:
                print(f"  步骤 {i}: {step.description}")
                print(f"    处理前: {initial_count} 条记录")
                
                # 执行清洗步骤
                cleaned_df = step.apply(current_df)
                
                final_count = len(cleaned_df)
                step_end_time = datetime.now()
                processing_time = (step_end_time - step_start_time).total_seconds()
                
                # 记录清洗结果
                step_log = {
                    'step_number': i,
                    'step_type': step.step_type,
                    'description': step.description,
                    'initial_count': initial_count,
                    'final_count': final_count,
                    'records_removed': initial_count - final_count,
                    'processing_time': processing_time,
                    'success': True,
                    'error': None
                }
                
                print(f"    处理后: {final_count} 条记录")
                if initial_count != final_count:
                    print(f"    变化: {initial_count - final_count:+d} 条记录")
                print(f"    耗时: {processing_time:.3f} 秒")
                
                current_df = cleaned_df
                
            except Exception as e:
                step_end_time = datetime.now()
                processing_time = (step_end_time - step_start_time).total_seconds()
                
                # 记录错误
                step_log = {
                    'step_number': i,
                    'step_type': step.step_type,
                    'description': step.description,
                    'initial_count': initial_count,
                    'final_count': initial_count,  # 保持不变
                    'records_removed': 0,
                    'processing_time': processing_time,
                    'success': False,
                    'error': str(e)
                }
                
                print(f"    ❌ 步骤执行失败: {e}")
                print(f"    继续执行后续步骤...")
            
            self.cleaning_log.append(step_log)
        
        # 输出清洗总结
        total_removed = len(df) - len(current_df)
        print(f"\n清洗完成:")
        print(f"  最终数据量: {len(current_df)} 条记录")
        print(f"  总计移除: {total_removed} 条记录")
        print(f"  清洗步骤: {len(self.cleaning_steps)} 个")
        
        return current_df
    
    def get_cleaning_summary(self) -> Dict[str, Any]:
        """获取清洗过程摘要
        
        Returns:
            包含清洗过程统计信息的字典
        """
        if not self.cleaning_log:
            return {}
        
        total_steps = len(self.cleaning_log)
        successful_steps = sum(1 for log in self.cleaning_log if log['success'])
        failed_steps = total_steps - successful_steps
        
        initial_count = self.cleaning_log[0]['initial_count'] if self.cleaning_log else 0
        final_count = self.cleaning_log[-1]['final_count'] if self.cleaning_log else 0
        total_removed = initial_count - final_count
        
        total_time = sum(log['processing_time'] for log in self.cleaning_log)
        
        return {
            'total_steps': total_steps,
            'successful_steps': successful_steps,
            'failed_steps': failed_steps,
            'initial_record_count': initial_count,
            'final_record_count': final_count,
            'total_records_removed': total_removed,
            'total_processing_time': total_time,
            'step_details': self.cleaning_log
        }
    
    def add_cleaning_step(self, step_config: Dict[str, Any]) -> None:
        """动态添加清洗步骤
        
        Args:
            step_config: 清洗步骤配置
        """
        self.cleaning_config.append(step_config)
        self._build_cleaning_steps()
    
    def clear_cleaning_steps(self) -> None:
        """清空所有清洗步骤"""
        self.cleaning_config = []
        self.cleaning_steps = []
        self.cleaning_log = []
    
    @classmethod
    def from_source_config(cls, source_config: Dict[str, Any]) -> 'DataCleaner':
        """从数据源配置创建数据清洗器
        
        Args:
            source_config: 数据源配置，应包含 'cleaning_steps' 键
            
        Returns:
            配置好的数据清洗器实例
        """
        cleaning_steps = source_config.get('cleaning_steps', [])
        return cls(cleaning_steps)
