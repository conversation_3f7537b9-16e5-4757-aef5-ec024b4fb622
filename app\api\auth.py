from flask import Blueprint, request, jsonify
import time
import logging
import secrets
import json
from ..models.user import User
from ..models.token import Token
from ..models.database import db
from datetime import datetime

# 设置日志记录器
logger = logging.getLogger(__name__)

# 创建蓝图
auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/api/auth/register', methods=['POST'])
def register():
    """
    用户注册API
    
    请求体:
    {
        "username": "用户名",
        "password": "密码",
        "role": "patient|doctor",
        "license_number": "医生执照号"（仅医生角色需要）
    }
    
    响应:
    {
        "success": true|false,
        "message": "成功/错误信息",
        "token": "身份验证令牌"（仅在成功时）
    }
    """
    try:
        data = request.json
        username = data.get('username')
        password = data.get('password')
        role = data.get('role', 'patient')  # 默认为患者
        license_number = data.get('license_number', '')  # 医生执照号
        
        # 日志输出请求数据，但不记录密码
        logger.info(f"注册请求: username={username}, role={role}")
        
        # 验证请求数据
        if not username or not password:
            logger.warning(f"注册请求缺少必要参数: username={username}")
            return jsonify({
                'success': False,
                'message': '用户名和密码不能为空'
            }), 400
        
        # 检查用户名是否已存在
        existing_user = User.query.filter_by(username=username).first()
        if existing_user:
            logger.warning(f"注册失败，用户名已存在: {username}")
            return jsonify({
                'success': False,
                'message': '用户名已存在'
            }), 400
        
        # 如果是医生角色，验证执照号
        if role == 'doctor' and not license_number:
            logger.warning(f"医生注册缺少执照号: username={username}")
            return jsonify({
                'success': False,
                'message': '医生注册需要提供执照号'
            }), 400
            
        # 创建用户
        try:
            new_user = User(
                username=username,
                password=password,
                role=role,
                license_number=license_number if role == 'doctor' else None
            )
            
            logger.info(f"准备添加新用户到数据库: {username}")
            db.session.add(new_user)
            db.session.commit()
            logger.info(f"新用户已添加到数据库: {username}, ID={new_user.id}")
        except Exception as db_err:
            logger.error(f"数据库保存用户错误: {str(db_err)}")
            db.session.rollback()
            return jsonify({
                'success': False,
                'message': f'创建用户时出错: {str(db_err)}'
            }), 500
        
        # 生成令牌
        try:
            token = Token.generate_for_user(new_user.id)
            logger.info(f"已为用户 {username} 生成令牌")
        except Exception as token_err:
            logger.error(f"生成令牌错误: {str(token_err)}")
            return jsonify({
                'success': False,
                'message': f'生成令牌时出错: {str(token_err)}'
            }), 500
        
        logger.info(f"{role}注册成功: {username}")
        
        return jsonify({
            'success': True,
            'message': '注册成功',
            'token': token,
            'username': username,
            'role': role
        })
        
    except Exception as e:
        # 详细记录错误信息
        import traceback
        error_trace = traceback.format_exc()
        logger.error(f"注册过程中发生错误: {str(e)}")
        logger.error(f"错误详情: {error_trace}")
        
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': '服务器错误，请稍后再试',
            'error': str(e)  # 在开发环境返回错误信息，生产环境应移除
        }), 500

@auth_bp.route('/api/auth/login', methods=['POST'])
def login():
    """
    用户登录API
    
    请求体:
    {
        "username": "用户名",
        "password": "密码"
    }
    
    响应:
    {
        "success": true|false,
        "message": "成功/错误信息",
        "token": "身份验证令牌"（仅在成功时）
        "role": "角色"（仅在成功时）
    }
    """
    try:
        data = request.json
        username = data.get('username')
        password = data.get('password')
        
        # 验证请求数据
        if not username or not password:
            return jsonify({
                'success': False,
                'message': '用户名和密码不能为空'
            }), 400
        
        # 查询用户并验证密码
        user = User.query.filter_by(username=username).first()
        if not user or not user.check_password(password):
            return jsonify({
                'success': False,
                'message': '用户名或密码不正确'
            }), 401
        
        # 更新最后登录时间 - 不再调用用户模型的方法
        try:
            # 使用参数化查询，避免SQL注入
            current_time = datetime.utcnow().replace(microsecond=0)
            db.session.execute(
                "UPDATE users SET last_login = :login_time WHERE id = :user_id",
                {"login_time": current_time, "user_id": user.id}
            )
            db.session.commit()
            logger.info(f"已更新用户 {username} 的最后登录时间")
        except Exception as e:
            db.session.rollback()
            logger.error(f"更新登录时间失败: {str(e)}")
            # 继续处理，不阻止登录
        
        # 生成令牌
        try:
            token = Token.generate_for_user(user.id)
            
            logger.info(f"{user.role}登录成功: {username}")
            
            return jsonify({
                'success': True,
                'message': '登录成功',
                'token': token,
                'username': username,
                'role': user.role
            })
        except Exception as token_err:
            logger.error(f"生成令牌错误: {str(token_err)}")
            return jsonify({
                'success': False,
                'message': '登录处理失败，请稍后再试'
            }), 500
        
    except Exception as e:
        logger.error(f"登录错误: {str(e)}")
        # 打印完整堆栈跟踪以便调试
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return jsonify({
            'success': False,
            'message': '服务器错误，请稍后再试'
        }), 500

@auth_bp.route('/api/auth/verify', methods=['GET'])
def verify_token():
    """
    验证令牌API
    
    请求头:
    Authorization: Bearer <token>
    
    响应:
    {
        "success": true|false,
        "message": "成功/错误信息",
        "username": "用户名"（仅在成功时）
        "role": "角色"（仅在成功时）
    }
    """
    try:
        # 从请求头获取令牌
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'success': False,
                'message': '未提供有效的身份验证令牌'
            }), 401
        
        token_str = auth_header.split(' ')[1]
        
        # 验证令牌
        user_id = Token.get_user_id_by_token(token_str)
        if not user_id:
            return jsonify({
                'success': False,
                'message': '无效的身份验证令牌或令牌已过期'
            }), 401
        
        # 获取用户信息
        user = User.query.get(user_id)
        if not user:
            return jsonify({
                'success': False,
                'message': '用户不存在'
            }), 401
        
        return jsonify({
            'success': True,
            'message': '令牌有效',
            'username': user.username,
            'role': user.role
        })
        
    except Exception as e:
        logger.error(f"令牌验证错误: {str(e)}")
        return jsonify({
            'success': False,
            'message': '服务器错误，请稍后再试'
        }), 500

@auth_bp.route('/api/auth/logout', methods=['POST'])
def logout():
    """
    用户注销API
    
    请求头:
    Authorization: Bearer <token>
    
    响应:
    {
        "success": true|false,
        "message": "成功/错误信息"
    }
    """
    try:
        # 从请求头获取令牌
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'success': False,
                'message': '未提供有效的身份验证令牌'
            }), 401
        
        token_str = auth_header.split(' ')[1]
        
        # 验证令牌
        user_id = Token.get_user_id_by_token(token_str)
        if user_id:
            # 删除令牌
            Token.query.filter_by(token=token_str).delete()
            db.session.commit()
            
            # 记录用户注销
            user = User.query.get(user_id)
            if user:
                logger.info(f"用户注销成功: {user.username}")
        
        return jsonify({
            'success': True,
            'message': '注销成功'
        })
        
    except Exception as e:
        logger.error(f"注销错误: {str(e)}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': '服务器错误，请稍后再试'
        }), 500 