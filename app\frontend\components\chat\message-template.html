<div class="chat-messages">
    <div v-for="message in messages" :key="message.id" :class="message.role + '-message'">
        <div class="message-avatar">
            <img :src="message.avatar" :alt="message.role" />
        </div>
        <div class="message-content">
            <div class="message-text">{{ message.content }}</div>
            <div class="message-meta">
                <span v-if="message.role === 'doctor'" class="doctor-name">{{ message.doctor_name || '医生' }}</span>
                <span class="message-time">{{ formatMessageTime(message.timestamp) }}</span>
            </div>
        </div>
    </div>
    
    <div v-if="isTyping" class="bot-message">
        <div class="message-avatar">
            <img src="./assets/bot-avatar.svg" alt="AI助手" />
        </div>
        <div class="message-content">
            <div class="loading-indicator">
                <div class="dot"></div>
                <div class="dot"></div>
                <div class="dot"></div>
            </div>
        </div>
    </div>
</div> 