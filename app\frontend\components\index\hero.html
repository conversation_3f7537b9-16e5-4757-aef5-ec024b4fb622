<!-- Hero区域 - 全屏设计 -->
<section id="home" class="hero-section">
    <div class="hero-background">
        <div class="hero-overlay"></div>
    </div>
    <div class="hero-container">
        <div class="hero-content">
            <div class="hero-badge">
                <span class="badge-icon">🏥</span>
                <span>专业医疗AI诊断平台</span>
            </div>
            <h1 class="hero-title">
                智能医疗问诊
                <span class="highlight">重新定义</span>
                健康咨询体验
            </h1>
            <p class="hero-subtitle">
                结合人工智能与专业医生，为您提供24/7全天候智能问诊服务。
                准确、快速、专业的医疗咨询，让健康触手可及。
            </p>
            <div class="hero-actions">
                <button class="btn hero-primary-btn" id="startConsultationBtn">
                    <span class="btn-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="20" height="20">
                            <path d="M19 8h-2v3h-3v2h3v3h2v-3h3v-2h-3V8zM4 6h9v2H4zm0 4h9v2H4zm0 4h9v2H4z"/>
                        </svg>
                    </span>
                    开始智能问诊
                </button>
                <button class="btn hero-secondary-btn" id="learnMoreBtn">
                    <span class="btn-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="20" height="20">
                            <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                        </svg>
                    </span>
                    了解更多
                </button>
            </div>
            <div class="hero-stats">
                <div class="stat-item">
                    <div class="stat-number">10万+</div>
                    <div class="stat-label">服务用户</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">500+</div>
                    <div class="stat-label">专业医生</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">98%</div>
                    <div class="stat-label">满意度</div>
                </div>
            </div>
        </div>
        <div class="hero-visual">
            <div class="visual-card main-card">
                <div class="card-header">
                    <div class="avatar-group">
                        <img src="./assets/user-avatar.svg" alt="用户" class="avatar">
                        <img src="./assets/bot-avatar.svg" alt="AI" class="avatar">
                    </div>
                    <span class="online-status">在线问诊</span>
                </div>
                <div class="chat-preview">
                    <div class="message user-msg">头痛持续3天了</div>
                    <div class="message ai-msg">根据您的症状，建议进行详细检查...</div>
                </div>
            </div>
            <div class="visual-card floating-card-1">
                <div class="mini-chart">
                    <div class="chart-bar" style="height: 60%"></div>
                    <div class="chart-bar" style="height: 80%"></div>
                    <div class="chart-bar" style="height: 100%"></div>
                    <div class="chart-bar" style="height: 75%"></div>
                </div>
                <span class="card-label">诊断准确率 95%</span>
            </div>
            <div class="visual-card floating-card-2">
                <div class="notification-dot"></div>
                <span class="card-label">新消息提醒</span>
            </div>
        </div>
    </div>
</section> 