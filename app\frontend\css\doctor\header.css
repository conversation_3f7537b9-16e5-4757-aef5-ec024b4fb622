/* 顶部导航样式 */
.top-nav {
    padding: 1.5rem 2rem;
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.current-patient {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.current-patient h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
}

.patient-status {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.375rem 0.75rem;
    background: var(--success);
    color: var(--white);
    border-radius: var(--radius-full);
    font-size: 0.75rem;
    font-weight: 500;
}

.nav-actions {
    display: flex;
    gap: 0.75rem;
}