/* 响应式设计 */

/* 1024px及以下设备 */
@media (max-width: 1024px) {
    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 3rem;
        padding: 2rem 1.5rem;
    }
    
    .hero-content {
        order: 1;
    }
    
    .hero-visual {
        order: 2;
    }
    
    .hero-title {
        font-size: clamp(2rem, 4vw, 2.75rem);
    }
    
    .hero-subtitle {
        font-size: 1.125rem;
        max-width: 100%;
    }
    
    .hero-actions {
        justify-content: center;
        gap: 1rem;
    }
    
    .hero-stats {
        justify-content: center;
        gap: 1.5rem;
    }
    
    .main-card {
        width: 280px;
    }
    
    .floating-card-1,
    .floating-card-2 {
        display: none;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .large-card {
        grid-row: span 1;
    }
    
    .process-steps {
        flex-direction: column;
        gap: 3rem;
    }
    
    .step-connector {
        width: 2px;
        height: 60px;
        transform: rotate(90deg);
    }
    
    .step-connector::after {
        transform: rotate(-90deg);
        top: 50px;
        right: -7px;
    }
}

/* 768px及以下设备 */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 70px;
        left: 0;
        right: 0;
        background: var(--white);
        flex-direction: column;
        padding: 2rem;
        box-shadow: var(--shadow-lg);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all var(--duration-normal) ease;
    }
    
    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
    
    .hero-section {
        min-height: 90vh;
        padding: 2rem 0;
    }
    
    .hero-container {
        padding: 1rem;
        gap: 2rem;
    }
    
    .hero-badge {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }
    
    .hero-title {
        font-size: clamp(1.75rem, 6vw, 2.5rem);
        margin-bottom: 1rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .hero-actions {
        flex-direction: column;
        gap: 0.75rem;
        margin-bottom: 2rem;
    }
    
    .hero-primary-btn,
    .hero-secondary-btn {
        width: 100%;
        padding: 0.875rem 1.25rem;
        justify-content: center;
        font-size: 0.95rem;
    }
    
    .hero-primary-btn .btn-icon,
    .hero-secondary-btn .btn-icon {
        font-size: 1.125rem;
    }
    
    .cta-primary-btn .btn-icon,
    .cta-secondary-btn .btn-icon {
        font-size: 1.25rem;
    }
    
    .hero-stats {
        gap: 1rem;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .stat-item {
        min-width: 80px;
    }
    
    .stat-number {
        font-size: 1.25rem;
        color: var(--gray-900) !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }
    
    .stat-label {
        font-size: 0.8rem;
        color: var(--gray-600) !important;
        text-shadow: none;
    }
    
    .main-card {
        width: 260px;
        padding: 1.25rem;
    }
    
    .avatar {
        width: 32px;
        height: 32px;
    }
    
    .message {
        padding: 0.75rem 1rem;
        font-size: 0.8rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .stat-card {
        padding: 2rem 1.5rem;
        gap: 1.25rem;
    }
    
    .stat-icon {
        width: 65px;
        height: 65px;
    }
    
    .stat-icon svg {
        width: 28px;
        height: 28px;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .team-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }
    
    .footer-bottom {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
}

/* 480px及以下设备 */
@media (max-width: 480px) {
    .container {
        padding: 0 1rem;
    }
    
    .nav-container {
        padding: 0 1rem;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .section-title {
        font-size: 1.75rem;
    }
    
    .stat-card {
        padding: 1.75rem 1.25rem;
        gap: 1rem;
    }
    
    .stat-icon {
        width: 60px;
        height: 60px;
    }
    
    .stat-icon svg {
        width: 26px;
        height: 26px;
    }
    
    .stat-number {
        font-size: 1.875rem;
    }
    
    .stat-description {
        font-size: 0.875rem;
    }
    
    .hero-stats .stat-number {
        font-size: 1.125rem;
        color: var(--gray-900) !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }
    
    .hero-stats .stat-label {
        font-size: 0.75rem;
        color: var(--gray-600) !important;
        text-shadow: none;
    }
    
    .feature-card,
    .doctor-card {
        padding: 1.5rem;
    }
    
    .floating-card-1,
    .floating-card-2 {
        display: none;
    }
} 