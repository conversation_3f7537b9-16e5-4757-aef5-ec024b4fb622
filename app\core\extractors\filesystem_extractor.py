"""文件系统数据抽取器"""

import os
import glob
from pathlib import Path
from typing import Dict, Any, List
import pandas as pd
import chardet

# 文档处理库
try:
    import PyPDF2
except ImportError:
    PyPDF2 = None
    
try:
    from docx import Document as DocxDocument
except ImportError:
    DocxDocument = None
    
try:
    import markdown
except ImportError:
    markdown = None

from .base_extractor import BaseExtractor


class FilesystemExtractor(BaseExtractor):
    """文件系统数据抽取器
    
    支持的文件格式：
    - PDF (.pdf)
    - Word文档 (.docx)
    - Markdown (.md)
    - 纯文本 (.txt)
    """
    
    def extract(self) -> pd.DataFrame:
        """从文件系统抽取数据"""
        self.validate_config(['path'])
        
        path = self.config['path']
        file_types = self.config.get('file_types', ['*.txt'])
        recursive = self.config.get('recursive', True)
        encoding = self.config.get('encoding', 'utf-8')
        
        if not os.path.exists(path):
            raise ValueError(f"路径不存在: {path}")
        
        data = []
        
        # 收集所有匹配的文件
        all_files = []
        for file_type in file_types:
            if recursive:
                pattern = os.path.join(path, '**', file_type)
                files = glob.glob(pattern, recursive=True)
            else:
                pattern = os.path.join(path, file_type)
                files = glob.glob(pattern)
            all_files.extend(files)
        
        print(f"找到 {len(all_files)} 个文件待处理")
        
        # 处理每个文件
        for file_path in all_files:
            try:
                content = self._extract_file_content(file_path, encoding)
                if content:
                    file_metadata = {
                        'file_path': file_path,
                        'file_name': os.path.basename(file_path),
                        'file_extension': os.path.splitext(file_path)[1],
                        'file_size': os.path.getsize(file_path),
                        'relative_path': os.path.relpath(file_path, path)
                    }
                    
                    data.append({
                        'content': self._clean_content(content),
                        'metadata': file_metadata
                    })
                    
            except Exception as e:
                print(f"处理文件 {file_path} 时出错: {str(e)}")
                continue
        
        print(f"成功处理 {len(data)} 个文件")
        return self._create_standardized_dataframe(data)
    
    def _extract_file_content(self, file_path: str, encoding: str) -> str:
        """根据文件类型抽取内容"""
        file_ext = os.path.splitext(file_path)[1].lower()
        
        if file_ext == '.pdf':
            return self._extract_pdf_content(file_path)
        elif file_ext == '.docx':
            return self._extract_docx_content(file_path)
        elif file_ext == '.md':
            return self._extract_markdown_content(file_path, encoding)
        elif file_ext in ['.txt', '.text']:
            return self._extract_text_content(file_path, encoding)
        else:
            # 尝试作为文本文件处理
            return self._extract_text_content(file_path, encoding)
    
    def _extract_pdf_content(self, file_path: str) -> str:
        """抽取PDF文件内容"""
        if PyPDF2 is None:
            raise ImportError("需要安装 PyPDF2 来处理PDF文件")
        
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                content = []
                
                for page in pdf_reader.pages:
                    text = page.extract_text()
                    if text:
                        content.append(text)
                
                return '\n'.join(content)
        except Exception as e:
            print(f"PDF文件 {file_path} 处理失败: {str(e)}")
            return ""
    
    def _extract_docx_content(self, file_path: str) -> str:
        """抽取Word文档内容"""
        if DocxDocument is None:
            raise ImportError("需要安装 python-docx 来处理DOCX文件")
        
        try:
            doc = DocxDocument(file_path)
            content = []
            
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    content.append(paragraph.text)
                    
            return '\n'.join(content)
        except Exception as e:
            print(f"DOCX文件 {file_path} 处理失败: {str(e)}")
            return ""
    
    def _extract_markdown_content(self, file_path: str, encoding: str) -> str:
        """抽取Markdown文件内容"""
        try:
            # 首先尝试读取原始内容
            content = self._extract_text_content(file_path, encoding)
            
            # 如果安装了markdown库，可以转换为HTML再提取纯文本
            if markdown and content:
                try:
                    html = markdown.markdown(content)
                    # 这里可以进一步处理HTML，提取纯文本
                    # 为简单起见，直接返回原始markdown内容
                    return content
                except:
                    return content
            
            return content
        except Exception as e:
            print(f"Markdown文件 {file_path} 处理失败: {str(e)}")
            return ""
    
    def _extract_text_content(self, file_path: str, encoding: str) -> str:
        """抽取纯文本文件内容"""
        try:
            # 如果指定的编码失败，尝试检测编码
            try:
                with open(file_path, 'r', encoding=encoding) as file:
                    return file.read()
            except UnicodeDecodeError:
                # 检测文件编码
                with open(file_path, 'rb') as file:
                    raw_data = file.read()
                    detected = chardet.detect(raw_data)
                    detected_encoding = detected.get('encoding', 'utf-8')
                
                with open(file_path, 'r', encoding=detected_encoding) as file:
                    return file.read()
                    
        except Exception as e:
            print(f"文本文件 {file_path} 处理失败: {str(e)}")
            return "" 