"""结构化数据行转文本转换器

将结构化数据DataFrame的每一行通过可配置的模板转换为自然语言描述
"""

import re
from typing import Dict, Any, List, Optional, Union
import pandas as pd
from datetime import datetime


class RowToTextConverter:
    """结构化数据行转文本转换器
    
    支持通过f-string模板将DataFrame的每一行转换为自然语言描述
    """
    
    def __init__(self, template_config: Dict[str, Any] = None):
        """初始化转换器
        
        Args:
            template_config: 模板配置，包含template、enabled等字段
        """
        self.template_config = template_config or {}
        self.template = self.template_config.get('template', '')
        self.enabled = self.template_config.get('enabled', False)
        self.output_column = self.template_config.get('output_column', 'natural_language_description')
        self.fallback_template = self.template_config.get('fallback_template', '')
        self.preprocessing = self.template_config.get('preprocessing', {})
        
        # 验证模板
        if self.enabled and not self.template:
            raise ValueError("启用行转文本功能时必须提供template配置")
    
    def convert(self, df: pd.DataFrame, source_name: str = "Unknown") -> pd.DataFrame:
        """将DataFrame的每一行转换为自然语言描述
        
        Args:
            df: 输入的结构化数据DataFrame
            source_name: 数据源名称，用于日志记录
            
        Returns:
            包含自然语言描述列的DataFrame
        """
        if not self.enabled:
            print(f"数据源 {source_name}: 行转文本功能未启用，跳过转换")
            return df
        
        if df.empty:
            print(f"数据源 {source_name}: 输入DataFrame为空，跳过转换")
            return df
        
        print(f"\n开始行转文本转换: {source_name}")
        print(f"输入数据量: {len(df)} 条记录")
        print(f"使用模板: {self.template}")
        
        df_copy = df.copy()
        
        # 应用预处理
        if self.preprocessing:
            df_copy = self._apply_preprocessing(df_copy)
        
        # 生成自然语言描述
        descriptions = []
        successful_conversions = 0
        failed_conversions = 0
        
        for index, row in df_copy.iterrows():
            try:
                # 准备模板变量
                template_vars = self._prepare_template_variables(row, df_copy.columns)
                
                # 应用主模板
                description = self._apply_template(self.template, template_vars)
                
                # 如果主模板失败且有备用模板，尝试备用模板
                if not description and self.fallback_template:
                    description = self._apply_template(self.fallback_template, template_vars)
                
                # 如果仍然失败，生成默认描述
                if not description:
                    description = self._generate_default_description(row, df_copy.columns)
                
                descriptions.append(description)
                successful_conversions += 1
                
            except Exception as e:
                # 转换失败时的处理
                print(f"  警告: 第 {index+1} 行转换失败: {e}")
                
                # 尝试备用模板
                try:
                    if self.fallback_template:
                        template_vars = self._prepare_template_variables(row, df_copy.columns)
                        description = self._apply_template(self.fallback_template, template_vars)
                    else:
                        description = self._generate_default_description(row, df_copy.columns)
                    descriptions.append(description)
                except:
                    # 最后的备用方案
                    descriptions.append(f"记录 {index+1}: 数据转换失败")
                
                failed_conversions += 1
        
        # 添加自然语言描述列
        df_copy[self.output_column] = descriptions
        
        # 输出转换结果
        print(f"转换完成:")
        print(f"  成功转换: {successful_conversions} 条记录")
        print(f"  转换失败: {failed_conversions} 条记录")
        print(f"  输出列名: {self.output_column}")
        
        # 显示转换示例
        if successful_conversions > 0:
            print(f"  转换示例:")
            for i, desc in enumerate(descriptions[:3]):
                print(f"    {i+1}: {desc}")
        
        return df_copy
    
    def _apply_preprocessing(self, df: pd.DataFrame) -> pd.DataFrame:
        """应用预处理步骤
        
        Args:
            df: 输入DataFrame
            
        Returns:
            预处理后的DataFrame
        """
        df_copy = df.copy()
        
        # 数据类型转换
        type_conversions = self.preprocessing.get('type_conversions', {})
        for column, target_type in type_conversions.items():
            if column in df_copy.columns:
                try:
                    if target_type == 'str':
                        df_copy[column] = df_copy[column].astype(str)
                    elif target_type == 'int':
                        df_copy[column] = pd.to_numeric(df_copy[column], errors='coerce').astype('Int64')
                    elif target_type == 'float':
                        df_copy[column] = pd.to_numeric(df_copy[column], errors='coerce')
                    elif target_type == 'datetime':
                        df_copy[column] = pd.to_datetime(df_copy[column], errors='coerce')
                except Exception as e:
                    print(f"  警告: 列 {column} 类型转换失败: {e}")
        
        # 值映射
        value_mappings = self.preprocessing.get('value_mappings', {})
        for column, mapping in value_mappings.items():
            if column in df_copy.columns:
                df_copy[column] = df_copy[column].map(mapping).fillna(df_copy[column])
        
        # 空值填充
        null_replacements = self.preprocessing.get('null_replacements', {})
        for column, replacement in null_replacements.items():
            if column in df_copy.columns:
                df_copy[column] = df_copy[column].fillna(replacement)
        
        return df_copy
    
    def _prepare_template_variables(self, row: pd.Series, columns: List[str]) -> Dict[str, Any]:
        """准备模板变量
        
        Args:
            row: DataFrame的一行数据
            columns: 列名列表
            
        Returns:
            模板变量字典
        """
        template_vars = {}
        
        # 添加所有列的值
        for col in columns:
            # 处理列名，使其适合作为变量名
            var_name = self._sanitize_variable_name(col)
            value = row[col]
            
            # 处理不同类型的值
            if pd.isna(value):
                template_vars[var_name] = "未知"
            elif isinstance(value, (int, float)):
                template_vars[var_name] = value
            else:
                template_vars[var_name] = str(value)
        
        # 添加一些有用的元变量
        template_vars['_row_index'] = row.name if row.name is not None else 0
        template_vars['_timestamp'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        return template_vars
    
    def _sanitize_variable_name(self, name: str) -> str:
        """清理变量名，使其符合Python变量命名规范
        
        Args:
            name: 原始名称
            
        Returns:
            清理后的变量名
        """
        # 移除特殊字符，替换为下划线
        sanitized = re.sub(r'[^\w\u4e00-\u9fff]', '_', str(name))
        
        # 如果以数字开头，添加前缀
        if sanitized and sanitized[0].isdigit():
            sanitized = 'col_' + sanitized
        
        # 如果为空，使用默认名称
        if not sanitized:
            sanitized = 'unknown_column'
        
        return sanitized
    
    def _apply_template(self, template: str, template_vars: Dict[str, Any]) -> str:
        """应用模板生成描述
        
        Args:
            template: f-string模板
            template_vars: 模板变量
            
        Returns:
            生成的描述文本
        """
        try:
            # 使用format方法应用模板
            description = template.format(**template_vars)
            return description.strip()
        except KeyError as e:
            raise ValueError(f"模板中引用了不存在的变量: {e}")
        except Exception as e:
            raise ValueError(f"模板应用失败: {e}")
    
    def _generate_default_description(self, row: pd.Series, columns: List[str]) -> str:
        """生成默认的描述文本
        
        Args:
            row: DataFrame的一行数据
            columns: 列名列表
            
        Returns:
            默认描述文本
        """
        parts = []
        for col in columns:
            value = row[col]
            if pd.notna(value) and str(value).strip():
                parts.append(f"{col}: {value}")
        
        if parts:
            return " | ".join(parts)
        else:
            return f"记录 {row.name + 1 if row.name is not None else 1}: 无有效数据"
    
    def get_conversion_summary(self) -> Dict[str, Any]:
        """获取转换过程摘要
        
        Returns:
            包含转换统计信息的字典
        """
        return {
            'enabled': self.enabled,
            'template': self.template,
            'output_column': self.output_column,
            'has_fallback_template': bool(self.fallback_template),
            'has_preprocessing': bool(self.preprocessing)
        }
    
    @classmethod
    def from_source_config(cls, source_config: Dict[str, Any]) -> 'RowToTextConverter':
        """从数据源配置创建转换器
        
        Args:
            source_config: 数据源配置，应包含 'row_to_text' 键
            
        Returns:
            配置好的转换器实例
        """
        row_to_text_config = source_config.get('row_to_text', {})
        return cls(row_to_text_config)
