/* 医生工作台现代化样式 - 简约高级版 */
:root {
    /* 简约高级配色 */
    --primary-color: #2c5282;
    --secondary-color: #4c6ef5;
    --accent-color: #5a67d8;
    --success-color: #38a169;
    --warning-color: #dd6b20;
    --danger-color: #e53e3e;
    
    /* 简约渐变 */
    --primary-gradient: linear-gradient(to right, #2c5282, #3182ce);
    --secondary-gradient: linear-gradient(to right, #4c6ef5, #6b8cff);
    --success-gradient: linear-gradient(to right, #38a169, #48bb78);
    
    /* 中性色 */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* 阴影系统 */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* 边框半径 */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* 动画时长 */
    --duration-fast: 0.15s;
    --duration-normal: 0.3s;
    --duration-slow: 0.5s;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    background: var(--hero-gradient);
    color: var(--gray-800);
    line-height: 1.6;
    min-height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 添加背景装饰 */
body::before {
    content: '';
    position: fixed;
    top: -50%;
    right: -20%;
    width: 40%;
    height: 120%;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.03) 0%, rgba(147, 51, 234, 0.03) 100%);
    border-radius: 50%;
    filter: blur(60px);
    z-index: 1;
    pointer-events: none;
}

/* 医生工作台布局 */
.dashboard {
    display: flex;
    min-height: 100vh;
    position: relative;
    z-index: 2;
    padding: 1rem;
    gap: 1rem;
}

/* 侧边栏 - 玻璃态效果 */
.sidebar {
    width: 320px;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-2xl);
    display: flex;
    flex-direction: column;
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    transition: all var(--duration-normal) var(--ease-out);
}

.sidebar:hover {
    transform: translateY(-2px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
}

.sidebar-header {
    padding: 2rem 1.5rem 1.5rem;
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
    color: var(--white);
    position: relative;
    overflow: hidden;
}

.sidebar-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(180deg); }
}

.sidebar-title {
    font-size: 1.5rem;
    font-weight: 700;
    letter-spacing: -0.025em;
    position: relative;
    z-index: 1;
}

.doctor-info {
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border-bottom: 1px solid var(--gray-200);
    background: var(--white);
}

.doctor-avatar {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-full);
    object-fit: cover;
    box-shadow: var(--shadow-md);
    border: 3px solid var(--white);
    transition: all var(--duration-normal) var(--ease-out);
}

.doctor-avatar:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-lg);
}

.doctor-details h3 {
    color: var(--gray-900);
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.doctor-details p {
    color: var(--gray-500);
    font-size: 0.875rem;
}

.patient-list {
    flex: 1;
    overflow-y: auto;
    padding: 0.5rem;
}

.patient-item {
    padding: 1rem;
    margin-bottom: 0.5rem;
    background: var(--white);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--duration-normal) var(--ease-out);
    border: 1px solid transparent;
    position: relative;
    overflow: hidden;
}

.patient-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    transition: left var(--duration-slow) var(--ease-out);
}

.patient-item:hover::before {
    left: 100%;
}

.patient-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-light);
}

.patient-item.active {
    background: var(--primary-gradient);
    color: var(--white);
    box-shadow: var(--shadow-lg);
}

.patient-item.active .patient-name,
.patient-item.active .patient-preview {
    color: var(--white);
}

.patient-name {
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 0.25rem;
    font-size: 0.95rem;
}

.patient-preview {
    color: var(--gray-500);
    font-size: 0.875rem;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 主内容区域 */
.content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
}

/* 顶部导航 */
.top-nav {
    padding: 1.5rem 2rem;
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.current-patient {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.current-patient h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
}

.patient-status {
    padding: 0.25rem 0.75rem;
    background: var(--secondary-gradient);
    color: var(--white);
    border-radius: var(--radius-full);
    font-size: 0.75rem;
    font-weight: 500;
}

.nav-actions {
    display: flex;
    gap: 0.75rem;
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--duration-normal) var(--ease-out);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transition: all var(--duration-normal) var(--ease-out);
    transform: translate(-50%, -50%);
}

.btn:hover::before {
    width: 300px;
    height: 300px;
}

.btn-primary {
    background: var(--primary-gradient);
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--white);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.btn-secondary:hover {
    background: var(--gray-50);
    border-color: var(--gray-400);
    transform: translateY(-1px);
}

/* 主内容区域 */
.main-content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
    background: var(--gray-50);
}

/* 聊天消息区域 */
.chat-messages {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--shadow-sm);
    min-height: 400px;
    max-height: 500px;
    overflow-y: auto;
    border: 1px solid var(--gray-200);
}

.message {
    display: flex;
    margin-bottom: 1.5rem;
    animation: slideInUp 0.3s var(--ease-out);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message.user-message {
    justify-content: flex-end;
}

.message-content {
    max-width: 70%;
    padding: 1rem 1.25rem;
    border-radius: var(--radius-lg);
    position: relative;
    word-wrap: break-word;
    box-shadow: var(--shadow-sm);
}

.user-message .message-content {
    background: var(--gray-100);
    color: var(--gray-900);
    border-bottom-right-radius: var(--radius-sm);
}

.doctor-message .message-content {
    background: var(--primary-gradient);
    color: var(--white);
    border-bottom-left-radius: var(--radius-sm);
}

.message-text {
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 0.5rem;
}

.message-time {
    font-size: 0.75rem;
    opacity: 0.7;
}

.empty-state-message {
    text-align: center;
    color: var(--gray-500);
    font-size: 1rem;
    padding: 3rem 1rem;
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    border: 2px dashed var(--gray-300);
}

/* AI诊断和处方区域 */
.ai-diagnosis,
.prescription {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    transition: all var(--duration-normal) var(--ease-out);
}

.ai-diagnosis:hover,
.prescription:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--gray-200);
}

.section-header h2 {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--gray-900);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .dashboard {
        padding: 0.5rem;
        gap: 0.5rem;
    }
    
    .sidebar {
        width: 280px;
    }
    
    .main-content {
        padding: 1.5rem;
    }
}

@media (max-width: 768px) {
    .dashboard {
        flex-direction: column;
        padding: 0;
        gap: 0;
    }
    
    .sidebar {
        width: 100%;
        border-radius: 0;
        margin-bottom: 0;
    }
    
    .content {
        border-radius: 0;
    }
    
    .main-content {
        padding: 1rem;
    }
    
    .top-nav {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .nav-actions {
        justify-content: center;
    }
} 

/* 加入会话模态框样式 */
.join-session-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    width: 90%;
    max-width: 500px;
    max-height: 90%;
    overflow-y: auto;
    animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--gray-500);
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.close-modal:hover {
    color: var(--gray-900);
}

.modal-body {
    padding: 1.5rem;
}

.modal-body p {
    margin-bottom: 1rem;
    color: var(--gray-700);
}

.join-message-options {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.join-message-option {
    padding: 1rem;
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    background-color: var(--gray-50);
    cursor: pointer;
    transition: all var(--duration-fast) ease;
}

.join-message-option:hover {
    border-color: var(--primary-color);
    background-color: var(--gray-100);
}

.join-message-option.selected {
    border-color: var(--primary-color);
    background-color: rgba(44, 82, 130, 0.1);
    font-weight: 500;
}

#customJoinMessage {
    width: 100%;
    padding: 1rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    resize: vertical;
    min-height: 100px;
    font-family: inherit;
    font-size: 0.95rem;
    color: var(--gray-800);
}

#customJoinMessage:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(44, 82, 130, 0.1);
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--gray-200);
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
}

/* 医生已加入标记 */
.doctor-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--success-color);
    background-color: rgba(56, 161, 105, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-full);
    margin-left: 0.5rem;
}

/* 高亮医学症状关键词 */
.highlight-symptom {
    background-color: rgba(237, 137, 54, 0.2);
    padding: 0 0.25rem;
    border-radius: 2px;
    font-weight: 500;
}

/* 处方消息特殊样式 */
.prescription-message .message-content {
    background-color: rgba(49, 130, 206, 0.1);
    border: 1px solid rgba(49, 130, 206, 0.3);
}

/* 消息发送者和时间 */
.message-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.message-sender {
    font-weight: 600;
    font-size: 0.95rem;
    color: var(--gray-800);
}

.message-time {
    font-size: 0.85rem;
    color: var(--gray-500);
}

/* 响应式设计调整 */
@media (max-width: 768px) {
    .join-message-options {
        gap: 0.5rem;
    }
    
    .modal-content {
        width: 95%;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 1rem;
    }
}

/* 消息输入区域 */
.message-input-area {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: 1rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    margin-top: 1rem;
}

.input-container {
    display: flex;
    align-items: flex-end;
    gap: 0.75rem;
}

.doctor-message-input {
    flex: 1;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-lg);
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    line-height: 1.4;
    resize: none;
    min-height: 44px;
    max-height: 120px;
    font-family: inherit;
    transition: border-color 0.2s var(--ease-out);
}

.doctor-message-input:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px var(--primary-100);
}

.send-message-btn {
    background: var(--primary-500);
    color: var(--white);
    border: none;
    border-radius: var(--radius-lg);
    padding: 0.75rem;
    cursor: pointer;
    transition: all 0.2s var(--ease-out);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    height: 44px;
}

.send-message-btn:hover:not(:disabled) {
    background: var(--primary-600);
    transform: translateY(-1px);
}

.send-message-btn:disabled {
    background: var(--gray-300);
    cursor: not-allowed;
    transform: none;
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 1000;
    animation: slideIn 0.3s ease;
}

.notification.success {
    background: #10b981;
}

.notification.error {
    background: #ef4444;
}

.notification.info {
    background: #3b82f6;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 加入消息选项样式 */
.join-message-option {
    padding: 12px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: 8px;
}

.join-message-option:hover {
    border-color: #3b82f6;
    background: #f8fafc;
}

.join-message-option.selected {
    border-color: #3b82f6;
    background: #eff6ff;
}
