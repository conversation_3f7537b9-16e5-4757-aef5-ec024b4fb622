from fastapi import FastAPI

from app.api.endpoints import kb_chat, chat

app = FastAPI(
    title="LLM Application Scaffold - Separated Endpoints",
    description="An API with distinct, stateful endpoints for Knowledge Base (RAG) chat and Agent (Tool-calling) chat.",
    version="2.2.0",
)

app.include_router(kb_chat.router, prefix="/api/kb_chat", tags=["Knowledge Base Chat"])
app.include_router(chat.router, prefix="/api/chat", tags=["Agent / Tool-calling Chat"])


@app.get("/", tags=["Health Check"])
def read_root():
    return {"status": "ok", "message": "Welcome to the LLM Scaffold API!"}
