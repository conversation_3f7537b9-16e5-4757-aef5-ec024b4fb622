from typing import List
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_core.documents import Document
from app.core.loaders import load_documents
from app.core.vector_store import get_vector_store
from app.configs.settings import config
from app.services.data_extraction_service import DataExtractionService
import pandas as pd


def load_and_ingest(data_path: str):
    """
    传统方式：从路径加载文档，拆分并摄取到向量存储中。
    保留此方法以维持向后兼容性。
    """
    # 1. Load documents
    docs = load_documents(data_path)

    # 2. Split documents
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=config.text_splitter.chunk_size,
        chunk_overlap=config.text_splitter.chunk_overlap
    )
    splits = text_splitter.split_documents(docs)

    if not splits:
        print("No documents to ingest.")
        return

    print(f"Split {len(docs)} documents into {len(splits)} chunks.")

    # 3. Ingest into vector store
    vector_store = get_vector_store()
    print("Ingesting chunks into vector store...")
    vector_store.add_documents(documents=splits)
    print("Ingestion complete.")


def extract_and_ingest_all_sources():
    """
    新方式：从sources.yaml配置的所有数据源抽取数据，
    标准化处理后摄取到向量存储中。
    """
    print("开始从配置的数据源抽取并摄取数据...")
    
    # 1. 初始化数据抽取服务
    extraction_service = DataExtractionService()
    
    # 2. 抽取所有启用的数据源
    df = extraction_service.extract_all_sources()
    
    if df.empty:
        print("没有抽取到任何数据，摄取操作取消。")
        return
    
    # 3. 将DataFrame转换为LangChain文档格式
    docs = _dataframe_to_documents(df)
    
    # 4. 拆分文档
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=config.text_splitter.chunk_size,
        chunk_overlap=config.text_splitter.chunk_overlap
    )
    splits = text_splitter.split_documents(docs)
    
    if not splits:
        print("文档拆分后没有内容，摄取操作取消。")
        return
    
    print(f"将 {len(docs)} 个文档拆分为 {len(splits)} 个块。")
    
    # 5. 摄取到向量存储
    vector_store = get_vector_store()
    print("正在将数据块摄取到向量存储...")
    vector_store.add_documents(documents=splits)
    print("数据摄取完成。")
    
    # 6. 输出统计信息
    stats = extraction_service.get_extraction_statistics(df)
    _print_ingestion_statistics(stats)


def extract_and_ingest_by_source_type(source_type: str):
    """
    按数据源类型抽取并摄取数据
    
    Args:
        source_type: 数据源类型 (filesystem, csv_excel, web_url, confluence, git)
    """
    print(f"开始抽取数据源类型: {source_type}")
    
    extraction_service = DataExtractionService()
    df = extraction_service.extract_by_source_type(source_type)
    
    if df.empty:
        print(f"数据源类型 {source_type} 没有抽取到任何数据。")
        return
    
    # 转换并摄取
    docs = _dataframe_to_documents(df)
    _ingest_documents(docs)
    
    # 输出统计
    stats = extraction_service.get_extraction_statistics(df)
    _print_ingestion_statistics(stats)


def extract_and_ingest_by_source_name(source_name: str):
    """
    按数据源名称抽取并摄取数据
    
    Args:
        source_name: 数据源名称
    """
    print(f"开始抽取数据源: {source_name}")
    
    extraction_service = DataExtractionService()
    df = extraction_service.extract_by_source_name(source_name)
    
    if df.empty:
        print(f"数据源 {source_name} 没有抽取到任何数据。")
        return
    
    # 转换并摄取
    docs = _dataframe_to_documents(df)
    _ingest_documents(docs)
    
    # 输出统计
    stats = extraction_service.get_extraction_statistics(df)
    _print_ingestion_statistics(stats)


def _dataframe_to_documents(df: pd.DataFrame) -> List[Document]:
    """
    将标准化的DataFrame转换为LangChain文档格式

    Args:
        df: 包含content和metadata列的标准化DataFrame

    Returns:
        LangChain Document对象列表
    """
    documents = []

    for _, row in df.iterrows():
        content = row['content']
        metadata = row['metadata']

        # 确保metadata是字典格式
        if not isinstance(metadata, dict):
            metadata = {'raw_metadata': str(metadata)}

        # 过滤复杂的元数据，只保留基本类型
        filtered_metadata = {}
        for key, value in metadata.items():
            if isinstance(value, (str, int, float, bool)) or value is None:
                filtered_metadata[key] = value
            elif isinstance(value, dict):
                # 将嵌套字典转换为字符串
                filtered_metadata[f"{key}_json"] = str(value)
            elif isinstance(value, list):
                # 将列表转换为字符串
                filtered_metadata[f"{key}_list"] = str(value)
            else:
                # 其他类型转换为字符串
                filtered_metadata[f"{key}_str"] = str(value)

        # 创建LangChain文档
        doc = Document(
            page_content=content,
            metadata=filtered_metadata
        )
        documents.append(doc)

    return documents


def _ingest_documents(docs: List[Document]):
    """
    将文档拆分并摄取到向量存储
    
    Args:
        docs: LangChain文档列表
    """
    if not docs:
        print("没有文档需要摄取。")
        return
    
    # 拆分文档
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=config.text_splitter.chunk_size,
        chunk_overlap=config.text_splitter.chunk_overlap
    )
    splits = text_splitter.split_documents(docs)
    
    if not splits:
        print("文档拆分后没有内容。")
        return
    
    print(f"将 {len(docs)} 个文档拆分为 {len(splits)} 个块。")
    
    # 摄取到向量存储
    vector_store = get_vector_store()
    print("正在摄取数据块到向量存储...")
    vector_store.add_documents(documents=splits)
    print("摄取完成。")


def _print_ingestion_statistics(stats: dict):
    """
    打印摄取统计信息
    
    Args:
        stats: 统计信息字典
    """
    print("\n=== 数据摄取统计 ===")
    print(f"总记录数: {stats.get('total_records', 0)}")
    
    content_stats = stats.get('content_length_stats', {})
    if content_stats:
        print(f"内容长度统计:")
        print(f"  - 最小: {content_stats.get('min', 0)} 字符")
        print(f"  - 最大: {content_stats.get('max', 0)} 字符")
        print(f"  - 平均: {content_stats.get('mean', 0):.1f} 字符")
    
    source_dist = stats.get('source_name_distribution', {})
    if source_dist:
        print(f"数据源分布:")
        for source, count in source_dist.items():
            print(f"  - {source}: {count} 条记录")
    
    category_dist = stats.get('category_distribution', {})
    if category_dist:
        print(f"类别分布:")
        for category, count in category_dist.items():
            print(f"  - {category}: {count} 条记录")
    
    print("==================\n")


def get_available_data_sources():
    """
    获取所有可用的数据源信息
    
    Returns:
        数据源信息字典
    """
    extraction_service = DataExtractionService()
    return extraction_service.get_available_sources()


def test_data_source_connection(source_name: str):
    """
    测试指定数据源的连接
    
    Args:
        source_name: 数据源名称
        
    Returns:
        测试结果字典
    """
    extraction_service = DataExtractionService()
    return extraction_service.test_source_connection(source_name)