"""Elasticsearch搜索引擎实现

基于Elasticsearch的关键词搜索引擎
"""

from typing import List, Dict, Any, Optional
import logging
from datetime import datetime

try:
    from elasticsearch import Elasticsearch
    from elasticsearch.exceptions import NotFoundError, RequestError
    ELASTICSEARCH_AVAILABLE = True
except ImportError:
    ELASTICSEARCH_AVAILABLE = False
    print("警告: elasticsearch 未安装，将使用简化版本的搜索引擎")

from .base_search_engine import BaseSearchEngine


class ElasticsearchEngine(BaseSearchEngine):
    """Elasticsearch搜索引擎"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """初始化Elasticsearch引擎
        
        Args:
            config: Elasticsearch配置
        """
        super().__init__(config)
        
        if not ELASTICSEARCH_AVAILABLE:
            raise ImportError("elasticsearch 包未安装，请运行: pip install elasticsearch")
        
        # Elasticsearch连接配置
        self.host = self.config.get('host', 'localhost')
        self.port = self.config.get('port', 9200)
        self.username = self.config.get('username')
        self.password = self.config.get('password')
        self.use_ssl = self.config.get('use_ssl', False)
        
        # 初始化Elasticsearch客户端
        self._init_client()
        
        # 默认映射配置
        self.default_mapping = {
            "mappings": {
                "properties": {
                    "content": {
                        "type": "text",
                        "analyzer": "standard",
                        "search_analyzer": "standard"
                    },
                    "chunk_id": {"type": "keyword"},
                    "document_id": {"type": "keyword"},
                    "source_type": {"type": "keyword"},
                    "source_location": {"type": "keyword"},
                    "source_name": {"type": "keyword"},
                    "page_number": {"type": "integer"},
                    "chunk_index": {"type": "integer"},
                    "chunk_length": {"type": "integer"},
                    "splitter_type": {"type": "keyword"},
                    "clean_status": {"type": "keyword"},
                    "extracted_at": {"type": "date"},
                    "indexed_at": {"type": "date"},
                    "category": {"type": "keyword"},
                    "file_name": {"type": "keyword"},
                    "file_extension": {"type": "keyword"}
                }
            },
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0,
                "analysis": {
                    "analyzer": {
                        "medical_analyzer": {
                            "type": "standard",
                            "stopwords": "_none_"
                        }
                    }
                }
            }
        }
    
    def _init_client(self):
        """初始化Elasticsearch客户端"""
        try:
            # 构建连接参数
            es_config = {
                'hosts': [{'host': self.host, 'port': self.port}],
                'timeout': 30,
                'max_retries': 3,
                'retry_on_timeout': True
            }
            
            # 添加认证信息
            if self.username and self.password:
                es_config['basic_auth'] = (self.username, self.password)
            
            # SSL配置
            if self.use_ssl:
                es_config['use_ssl'] = True
                es_config['verify_certs'] = False
            
            self.client = Elasticsearch(**es_config)
            
            # 测试连接
            if not self.client.ping():
                print(f"警告: 无法连接到Elasticsearch ({self.host}:{self.port})")
                self.client = None
            else:
                print(f"✅ 成功连接到Elasticsearch ({self.host}:{self.port})")
                
        except Exception as e:
            print(f"警告: Elasticsearch连接失败: {e}")
            self.client = None
    
    def create_index(self, index_name: str = None, mapping: Dict[str, Any] = None) -> bool:
        """创建索引"""
        if not self.client:
            return False
        
        index_name = index_name or self.index_name
        mapping = mapping or self.default_mapping
        
        try:
            if self.client.indices.exists(index=index_name):
                print(f"索引 {index_name} 已存在")
                return True
            
            response = self.client.indices.create(index=index_name, body=mapping)
            print(f"✅ 成功创建索引: {index_name}")
            return response.get('acknowledged', False)
            
        except Exception as e:
            print(f"❌ 创建索引失败: {e}")
            return False
    
    def delete_index(self, index_name: str = None) -> bool:
        """删除索引"""
        if not self.client:
            return False
        
        index_name = index_name or self.index_name
        
        try:
            if not self.client.indices.exists(index=index_name):
                print(f"索引 {index_name} 不存在")
                return True
            
            response = self.client.indices.delete(index=index_name)
            print(f"✅ 成功删除索引: {index_name}")
            return response.get('acknowledged', False)
            
        except Exception as e:
            print(f"❌ 删除索引失败: {e}")
            return False
    
    def index_exists(self, index_name: str = None) -> bool:
        """检查索引是否存在"""
        if not self.client:
            return False
        
        index_name = index_name or self.index_name
        
        try:
            return self.client.indices.exists(index=index_name)
        except Exception as e:
            print(f"❌ 检查索引存在性失败: {e}")
            return False
    
    def add_documents(self, documents: List[Dict[str, Any]], index_name: str = None) -> bool:
        """批量添加文档"""
        if not self.client or not documents:
            return False
        
        index_name = index_name or self.index_name
        
        # 确保索引存在
        if not self.index_exists(index_name):
            self.create_index(index_name)
        
        try:
            # 准备批量操作
            actions = []
            for doc in documents:
                # 添加索引时间戳
                doc_copy = doc.copy()
                doc_copy['indexed_at'] = datetime.now().isoformat()
                
                # 使用chunk_id作为文档ID（如果存在）
                doc_id = doc_copy.get('chunk_id')
                
                action = {
                    "_index": index_name,
                    "_source": doc_copy
                }
                
                if doc_id:
                    action["_id"] = doc_id
                
                actions.append(action)
            
            # 执行批量索引
            from elasticsearch.helpers import bulk
            success_count, failed_items = bulk(
                self.client,
                actions,
                index=index_name,
                refresh=True
            )
            
            print(f"✅ 成功索引 {success_count} 个文档到 {index_name}")
            if failed_items:
                print(f"⚠️  {len(failed_items)} 个文档索引失败")
            
            return success_count > 0
            
        except Exception as e:
            print(f"❌ 批量添加文档失败: {e}")
            return False
    
    def update_document(self, doc_id: str, document: Dict[str, Any], index_name: str = None) -> bool:
        """更新文档"""
        if not self.client:
            return False
        
        index_name = index_name or self.index_name
        
        try:
            document_copy = document.copy()
            document_copy['indexed_at'] = datetime.now().isoformat()
            
            response = self.client.update(
                index=index_name,
                id=doc_id,
                body={"doc": document_copy}
            )
            
            return response.get('result') in ['updated', 'noop']
            
        except Exception as e:
            print(f"❌ 更新文档失败: {e}")
            return False
    
    def delete_document(self, doc_id: str, index_name: str = None) -> bool:
        """删除文档"""
        if not self.client:
            return False
        
        index_name = index_name or self.index_name
        
        try:
            response = self.client.delete(index=index_name, id=doc_id)
            return response.get('result') == 'deleted'
            
        except NotFoundError:
            print(f"文档 {doc_id} 不存在")
            return True
        except Exception as e:
            print(f"❌ 删除文档失败: {e}")
            return False
    
    def search(self, query: str, filters: Dict[str, Any] = None, 
               size: int = 10, index_name: str = None) -> List[Dict[str, Any]]:
        """搜索文档"""
        if not self.client:
            return []
        
        index_name = index_name or self.index_name
        
        try:
            # 构建搜索查询
            search_body = {
                "query": {
                    "bool": {
                        "must": [
                            {
                                "multi_match": {
                                    "query": query,
                                    "fields": ["content^2", "source_name", "file_name"],
                                    "type": "best_fields"
                                }
                            }
                        ]
                    }
                },
                "size": size,
                "sort": [{"_score": {"order": "desc"}}]
            }
            
            # 添加过滤条件
            if filters:
                filter_clauses = []
                for key, value in filters.items():
                    if isinstance(value, list):
                        filter_clauses.append({"terms": {key: value}})
                    else:
                        filter_clauses.append({"term": {key: value}})
                
                if filter_clauses:
                    search_body["query"]["bool"]["filter"] = filter_clauses
            
            # 执行搜索
            response = self.client.search(index=index_name, body=search_body)
            
            # 处理结果
            results = []
            for hit in response['hits']['hits']:
                result = hit['_source'].copy()
                result['_score'] = hit['_score']
                result['_id'] = hit['_id']
                results.append(result)
            
            return results
            
        except Exception as e:
            print(f"❌ 搜索失败: {e}")
            return []
    
    def get_document_count(self, index_name: str = None) -> int:
        """获取文档数量"""
        if not self.client:
            return 0
        
        index_name = index_name or self.index_name
        
        try:
            if not self.index_exists(index_name):
                return 0
            
            response = self.client.count(index=index_name)
            return response.get('count', 0)
            
        except Exception as e:
            print(f"❌ 获取文档数量失败: {e}")
            return 0
