from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
import logging

# 设置日志记录器
logger = logging.getLogger(__name__)

# 创建ORM对象
db = SQLAlchemy()
migrate = Migrate()

def init_db(app):
    """初始化数据库连接"""
    try:
        # 数据库配置已在Config中设置，这里直接初始化
        db.init_app(app)
        migrate.init_app(app, db)
        
        # 导入所有模型以确保它们已定义
        from .user import User, UserProfile
        from .token import Token
        from .chat_session import ChatSession, ChatMessage
        
        # 在应用上下文中创建所有表
        with app.app_context():
            db.create_all()
            logger.info("数据库初始化完成")
    except Exception as e:
        logger.error(f"数据库初始化失败: {str(e)}")
        logger.error("请确保数据库已创建: CREATE DATABASE medical_chatbot CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;")
        raise 