"""混合搜索功能测试

测试SRH-001混合搜索核心功能，特别是对包含专有名词的复杂查询的处理
"""

import sys
import os
from typing import List, Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from langchain_core.documents import Document
from app.core.retrievers.ensemble_retriever import EnsembleRetriever, create_ensemble_retriever
from app.core.fusion.rrf_fusion import RRFFusion, fuse_search_results
from app.core.chains import get_rag_chain, get_hybrid_rag_chain
from app.core.search_engines.simple_search_engine import SimpleSearchEngine


class TestRRFFusion:
    """测试RRF融合算法"""
    
    def test_rrf_basic_fusion(self):
        """测试基本的RRF融合功能"""
        # 创建测试文档
        vector_docs = [
            Document(page_content="高血压是一种常见的心血管疾病", metadata={"chunk_id": "1", "source": "medical_book"}),
            Document(page_content="糖尿病需要控制血糖水平", metadata={"chunk_id": "2", "source": "medical_book"}),
            Document(page_content="冠心病的症状包括胸痛", metadata={"chunk_id": "3", "source": "medical_book"})
        ]
        
        keyword_docs = [
            Document(page_content="糖尿病需要控制血糖水平", metadata={"chunk_id": "2", "source": "medical_book"}),
            Document(page_content="高血压是一种常见的心血管疾病", metadata={"chunk_id": "1", "source": "medical_book"}),
            Document(page_content="心脏病的预防很重要", metadata={"chunk_id": "4", "source": "medical_book"})
        ]
        
        # 测试RRF融合
        search_results = {
            'vector': vector_docs,
            'keyword': keyword_docs
        }
        
        weights = {'vector': 0.6, 'keyword': 0.4}
        
        fused_docs = fuse_search_results(search_results, weights, k=60)
        
        # 验证结果
        assert len(fused_docs) > 0
        assert all(hasattr(doc, 'metadata') for doc in fused_docs)
        assert all('rrf_score' in doc.metadata for doc in fused_docs)
        assert all('fusion_sources' in doc.metadata for doc in fused_docs)
        
        # 验证排序（分数应该是降序）
        scores = [doc.metadata['rrf_score'] for doc in fused_docs]
        assert scores == sorted(scores, reverse=True)
        
        print(f"✅ RRF融合测试通过，融合了 {len(fused_docs)} 个文档")
    
    def test_rrf_with_different_k_values(self):
        """测试不同k值对RRF结果的影响"""
        docs1 = [Document(page_content="文档1", metadata={"chunk_id": "1"})]
        docs2 = [Document(page_content="文档2", metadata={"chunk_id": "2"})]
        
        search_results = {'source1': docs1, 'source2': docs2}
        
        # 测试不同的k值
        for k in [10, 60, 100]:
            fused_docs = fuse_search_results(search_results, k=k)
            assert len(fused_docs) == 2
            print(f"✅ k={k} 测试通过")


class TestEnsembleRetriever:
    """测试混合检索器"""
    
    def setup_method(self):
        """设置测试环境"""
        # 创建简单的搜索引擎用于测试
        self.search_engine = SimpleSearchEngine({
            'index_name': 'test_index',
            'persist_directory': 'test_data'
        })
        
        # 添加测试文档到搜索引擎
        test_docs = [
            {
                'content': '高血压是一种常见的心血管疾病，需要长期管理',
                'chunk_id': 'test_1',
                'source': 'medical_test',
                'keywords': ['高血压', '心血管', '疾病']
            },
            {
                'content': '糖尿病患者需要控制血糖水平，定期监测',
                'chunk_id': 'test_2', 
                'source': 'medical_test',
                'keywords': ['糖尿病', '血糖', '监测']
            },
            {
                'content': '阿司匹林是常用的抗血小板药物',
                'chunk_id': 'test_3',
                'source': 'medical_test',
                'keywords': ['阿司匹林', '抗血小板', '药物']
            }
        ]
        
        self.search_engine.add_documents(test_docs)
    
    def test_ensemble_retriever_creation(self):
        """测试混合检索器的创建"""
        retriever = create_ensemble_retriever(
            vector_weight=0.6,
            keyword_weight=0.4,
            rrf_k=60,
            top_k=5
        )
        
        assert isinstance(retriever, EnsembleRetriever)
        assert retriever.vector_weight == 0.6
        assert retriever.keyword_weight == 0.4
        assert retriever.rrf_k == 60
        assert retriever.top_k == 5
        
        print("✅ 混合检索器创建测试通过")
    
    def test_keyword_search_with_medical_terms(self):
        """测试包含医学专有名词的关键词搜索"""
        # 测试专有名词搜索
        test_queries = [
            "高血压",
            "糖尿病血糖",
            "阿司匹林药物",
            "心血管疾病管理"
        ]
        
        for query in test_queries:
            results = self.search_engine.search(query, size=5)
            print(f"查询 '{query}' 返回 {len(results)} 个结果")
            
            # 验证结果包含相关内容
            if results:
                assert any(query.split()[0] in result.get('content', '') for result in results)
        
        print("✅ 医学专有名词搜索测试通过")


class TestHybridSearchIntegration:
    """测试混合搜索集成功能"""
    
    def test_hybrid_search_config_loading(self):
        """测试混合搜索配置加载"""
        from app.configs.settings import config
        
        # 验证配置加载
        assert hasattr(config, 'hybrid_search')
        assert hasattr(config.hybrid_search, 'enabled')
        assert hasattr(config.hybrid_search, 'weights')
        assert hasattr(config.hybrid_search, 'rrf')
        assert hasattr(config.hybrid_search, 'results')
        assert hasattr(config.hybrid_search, 'search_engine')
        
        print("✅ 混合搜索配置加载测试通过")
        print(f"   - 启用状态: {config.hybrid_search.enabled}")
        print(f"   - 向量权重: {config.hybrid_search.weights.vector}")
        print(f"   - 关键词权重: {config.hybrid_search.weights.keyword}")
        print(f"   - RRF参数k: {config.hybrid_search.rrf.k}")
    
    def test_rag_chain_with_hybrid_search(self):
        """测试RAG链的混合搜索功能"""
        try:
            # 测试创建混合搜索RAG链
            rag_chain = get_hybrid_rag_chain()
            assert rag_chain is not None
            print("✅ 混合搜索RAG链创建测试通过")
            
            # 测试创建传统向量搜索RAG链
            vector_rag_chain = get_rag_chain(use_hybrid_search=False)
            assert vector_rag_chain is not None
            print("✅ 传统向量搜索RAG链创建测试通过")
            
        except Exception as e:
            print(f"⚠️  RAG链创建测试跳过（可能缺少向量数据库）: {e}")


class TestComplexMedicalQueries:
    """测试复杂医学查询的处理"""
    
    def test_medical_terminology_queries(self):
        """测试医学术语查询"""
        # 创建包含医学术语的测试文档
        medical_docs = [
            {
                'content': '心肌梗死（MI）是冠状动脉急性闭塞导致的心肌坏死',
                'chunk_id': 'med_1',
                'source': 'cardiology',
                'keywords': ['心肌梗死', 'MI', '冠状动脉', '心肌坏死']
            },
            {
                'content': '急性心肌梗死的典型症状包括胸痛、呼吸困难',
                'chunk_id': 'med_2',
                'source': 'cardiology', 
                'keywords': ['急性心肌梗死', '胸痛', '呼吸困难']
            },
            {
                'content': '心电图（ECG）可以诊断心肌梗死',
                'chunk_id': 'med_3',
                'source': 'diagnostics',
                'keywords': ['心电图', 'ECG', '诊断', '心肌梗死']
            }
        ]
        
        # 创建搜索引擎并添加文档
        search_engine = SimpleSearchEngine({'index_name': 'medical_test'})
        search_engine.add_documents(medical_docs)
        
        # 测试复杂查询
        complex_queries = [
            "心肌梗死的症状",
            "MI诊断方法",
            "急性心肌梗死ECG表现",
            "冠状动脉闭塞导致的心肌坏死"
        ]
        
        for query in complex_queries:
            results = search_engine.search(query, size=3)
            print(f"复杂查询 '{query}' 返回 {len(results)} 个结果")
            
            # 验证结果相关性
            if results:
                # 检查是否包含相关医学术语
                relevant_terms = ['心肌梗死', 'MI', '心电图', 'ECG', '冠状动脉']
                for result in results:
                    content = result.get('content', '')
                    has_relevant_term = any(term in content for term in relevant_terms)
                    if has_relevant_term:
                        print(f"   ✅ 找到相关结果: {content[:50]}...")
                        break
        
        print("✅ 复杂医学查询测试通过")


def run_hybrid_search_tests():
    """运行所有混合搜索测试"""
    print("🧪 开始混合搜索功能测试")
    print("=" * 50)
    
    # 测试RRF融合
    print("\n📋 测试RRF融合算法")
    rrf_test = TestRRFFusion()
    rrf_test.test_rrf_basic_fusion()
    rrf_test.test_rrf_with_different_k_values()
    
    # 测试混合检索器
    print("\n📋 测试混合检索器")
    ensemble_test = TestEnsembleRetriever()
    ensemble_test.setup_method()
    ensemble_test.test_ensemble_retriever_creation()
    ensemble_test.test_keyword_search_with_medical_terms()
    
    # 测试集成功能
    print("\n📋 测试混合搜索集成")
    integration_test = TestHybridSearchIntegration()
    integration_test.test_hybrid_search_config_loading()
    integration_test.test_rag_chain_with_hybrid_search()
    
    # 测试复杂查询
    print("\n📋 测试复杂医学查询")
    complex_test = TestComplexMedicalQueries()
    complex_test.test_medical_terminology_queries()
    
    print("\n" + "=" * 50)
    print("🎉 混合搜索功能测试完成！")


if __name__ == "__main__":
    run_hybrid_search_tests()
