/* CSS变量定义 */
:root {
    /* 简约高级配色 */
    --primary: #3b82f6;
    --primary-light: #60a5fa;
    --primary-dark: #1d4ed8;
    --primary-color: #2c5282;
    --secondary: #10b981;
    --secondary-color: #4c6ef5;
    --accent-color: #5a67d8;
    --success-color: #38a169;
    --warning-color: #dd6b20;
    --danger-color: #e53e3e;
    
    /* 简约渐变 */
    --primary-gradient: linear-gradient(135deg, #2c5282 0%, #3182ce 100%);
    --secondary-gradient: linear-gradient(135deg, #4c6ef5 0%, #6b8cff 100%);
    --success-gradient: linear-gradient(135deg, #38a169 0%, #48bb78 100%);
    --hero-gradient: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
    
    /* 中性色 */
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* 阴影系统 */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* 边框半径 */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;
    
    /* 动画时长 */
    --duration-fast: 0.15s;
    --duration-normal: 0.3s;
    --duration-slow: 0.5s;
} 