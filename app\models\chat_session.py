import time
from datetime import datetime
from .database import db

class ChatSession(db.Model):
    """聊天会话模型，用于存储会话相关信息"""
    __tablename__ = 'chat_sessions'
    
    id = db.Column(db.String(50), primary_key=True)  # 使用字符串类型的ID，与前端会话ID保持一致
    user_id = db.Column(db.<PERSON><PERSON><PERSON>, db.ForeignKey('users.id', ondelete='CASCADE'), nullable=False)  # 关联用户，必须指定
    title = db.Column(db.String(255), nullable=True)  # 会话标题，可以根据首条消息自动生成
    created_at = db.Column(db.DateTime, default=datetime.now)
    last_activity = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)  # 最后活动时间
    status = db.Column(db.String(20), default='active')  # 状态：active, archived, deleted
    doctor_involved = db.Column(db.<PERSON><PERSON><PERSON>, default=False)  # 是否有医生参与
    priority = db.Column(db.String(20), default='normal')  # 优先级：normal, high
    
    # 关系：一个会话有多条消息
    messages = db.relationship('ChatMessage', back_populates='session', cascade='all, delete-orphan')
    
    def __init__(self, session_id, user_id, title=None):
        if user_id is None:
            raise ValueError("user_id is required for ChatSession")
        self.id = session_id
        self.user_id = user_id
        self.title = title
        self.created_at = datetime.now()
        self.last_activity = datetime.now()
    
    def update_activity(self):
        """更新最后活动时间"""
        self.last_activity = datetime.now()
        
    def to_dict(self):
        """将会话信息转换为字典"""
        return {
            'session_id': self.id,
            'title': self.title,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_activity': self.last_activity.isoformat() if self.last_activity else None,
            'status': self.status,
            'doctor_involved': self.doctor_involved,
            'priority': self.priority
        }

class ChatMessage(db.Model):
    """聊天消息模型，用于存储会话中的消息"""
    __tablename__ = 'chat_messages'
    
    id = db.Column(db.Integer, primary_key=True)
    session_id = db.Column(db.String(50), db.ForeignKey('chat_sessions.id', ondelete='CASCADE'), nullable=False)
    role = db.Column(db.String(20), nullable=False)  # 角色：user, bot, doctor
    content = db.Column(db.Text, nullable=False)  # 消息内容
    timestamp = db.Column(db.DateTime, default=datetime.now)  # 发送时间
    doctor_name = db.Column(db.String(50), nullable=True)  # 医生名称（仅当role为doctor时使用）
    
    # 关系：多条消息属于一个会话
    session = db.relationship('ChatSession', back_populates='messages')
    
    def __init__(self, session_id, role, content, doctor_name=None):
        self.session_id = session_id
        self.role = role
        self.content = content
        self.timestamp = datetime.now()
        self.doctor_name = doctor_name
    
    def to_dict(self):
        """将消息转换为字典"""
        message_dict = {
            'role': self.role,
            'content': self.content,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None
        }
        
        # 仅当是医生消息时添加医生名称
        if self.role == 'doctor' and self.doctor_name:
            message_dict['doctor_name'] = self.doctor_name
            
        return message_dict 