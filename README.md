# 慧问医答——智能医疗问答系统

[![Python](https://img.shields.io/badge/Python-3.10+-blue.svg)](https://python.org)
[![Flask](https://img.shields.io/badge/Flask-2.3+-green.svg)](https://flask.palletsprojects.com/)
[![LangChain](https://img.shields.io/badge/LangChain-0.3+-orange.svg)](https://langchain.com/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

基于LangChain和Flask的智能医疗问答系统，集成多数据源抽取、向量检索、用户认证等功能，为用户提供专业的医疗咨询服务。

## ✨ 项目特色

"慧问医答"是一个企业级的智能医疗问答平台，具备以下核心能力：

- 🤖 **智能对话**：基于LangChain的RAG（检索增强生成）架构，提供准确的医疗问答
- 📚 **多源数据**：支持PDF、Word、CSV、网页、Git仓库等多种数据源的自动抽取和处理
- 👥 **多角色系统**：支持患者和医生两种角色，提供差异化服务
- 🔐 **安全认证**：完整的用户注册、登录、权限管理系统
- 💾 **会话管理**：持久化聊天记录，支持多轮对话上下文
- 🎨 **响应式界面**：现代化的Web界面，支持桌面和移动设备

## 🏗️ 系统架构

```
MedicalBot/
├── app/                          # 应用核心目录
│   ├── api/                      # API接口层
│   │   ├── main.py              # FastAPI主入口
│   │   ├── chat.py              # Flask聊天API
│   │   ├── auth.py              # 用户认证API
│   │   ├── profile.py           # 用户资料API
│   │   └── endpoints/           # FastAPI端点
│   │       ├── kb_chat.py       # 知识库问答
│   │       └── chat.py          # 智能对话
│   ├── core/                    # 核心功能模块
│   │   ├── chains/              # LangChain链
│   │   ├── extractors/          # 数据抽取器
│   │   ├── etl/                 # 数据处理管道
│   │   └── memory/              # 对话记忆管理
│   ├── services/                # 业务服务层
│   │   ├── llm_service.py       # LLM服务
│   │   ├── knowledge_service.py  # 知识库服务
│   │   └── data_extraction_service.py # 数据抽取服务
│   ├── models/                  # 数据模型
│   │   ├── user.py              # 用户模型
│   │   ├── chat_session.py      # 会话模型
│   │   └── database.py          # 数据库配置
│   ├── frontend/                # 前端界面
│   │   ├── components/          # 页面组件
│   │   ├── css/                 # 样式文件
│   │   ├── js/                  # JavaScript脚本
│   │   └── assets/              # 静态资源
│   └── configs/                 # 配置文件
├── scripts/                     # 工具脚本
│   ├── extract_and_ingest.py    # 数据抽取脚本
│   └── ingest_data.py           # 数据摄取脚本
├── data/                        # 数据目录
├── migrations/                  # 数据库迁移
└── chroma_db/                   # 向量数据库
```

## 🚀 快速开始

### 环境要求

- Python 3.10+
- pip 或 poetry
- MySQL 数据库（可选，默认使用SQLite）

### 1. 克隆项目

```bash
git clone <repository-url>
cd MedicalBot
```

### 2. 安装依赖

使用pip：
```bash
pip install -r requirements.txt
```

或使用poetry：
```bash
poetry install
```

### 3. 环境配置

创建 `.env` 文件并配置必要参数：
```env
# 数据库配置
DATABASE_URL=sqlite:///medical_chatbot.db
# 或使用MySQL: mysql+pymysql://user:password@localhost/medical_chatbot

# LLM配置
LLM_API_URL=your_llm_api_url
LLM_API_KEY=your_api_key

# 应用配置
SECRET_KEY=your_secret_key
DEBUG=True
```

### 4. 初始化数据库

```bash
# 使用Flask CLI
python run.py db init
python run.py db migrate
python run.py db upgrade

# 或直接运行应用（会自动创建表）
python run.py
```

### 5. 数据摄取（可选）

导入医疗知识数据：
```bash
# 从配置的所有数据源抽取数据
python scripts/extract_and_ingest.py --all

# 或按类型抽取
python scripts/extract_and_ingest.py --type filesystem
```

### 6. 启动应用

Flask应用：
```bash
python run.py
```

FastAPI应用：
```bash
poetry run poe start
# 或
uvicorn app.api.main:app --host 0.0.0.0 --port 12345 --reload
```

### 7. 访问应用

- **Web界面**: http://localhost:5000
- **聊天页面**: http://localhost:5000/chat
- **医生仪表板**: http://localhost:5000/doctor-dashboard
- **API文档**: http://localhost:12345/docs (FastAPI)

## 🎯 核心功能

### 智能问答系统
- **RAG检索增强生成**：结合向量检索和大语言模型
- **多轮对话**：支持上下文记忆的连续对话
- **专业医疗知识**：基于医疗文档和数据库的专业回答

### 多数据源支持
- **文档处理**：PDF、Word、Markdown、纯文本
- **结构化数据**：CSV、Excel文件
- **网络数据**：网页抓取、Confluence、Git仓库
- **自动化ETL**：数据抽取、清洗、标准化、向量化

### 用户管理系统
- **多角色支持**：患者和医生角色
- **安全认证**：JWT令牌认证
- **权限控制**：基于角色的访问控制
- **用户资料**：个人信息和偏好管理

### 会话管理
- **持久化存储**：聊天记录数据库存储
- **会话恢复**：支持中断后继续对话
- **历史查询**：用户可查看历史对话记录

## 🛠️ 技术栈

### 后端技术
- **Web框架**: Flask 2.3+ / FastAPI 0.111+
- **AI框架**: LangChain 0.3+, LangChain Community
- **数据库**: SQLAlchemy, Flask-Migrate, MySQL/SQLite
- **向量数据库**: ChromaDB 0.5+
- **文档处理**: PyPDF2, python-docx, unstructured
- **机器学习**: sentence-transformers, torch

### 前端技术
- **核心**: HTML5, CSS3, JavaScript ES6+
- **UI框架**: 响应式设计，现代化界面
- **通信**: RESTful API, 实时WebSocket支持

### 开发工具
- **包管理**: Poetry, pip
- **数据处理**: pandas, numpy
- **网络爬虫**: BeautifulSoup4, Selenium
- **版本控制**: GitPython

## 📖 使用指南

### API接口

#### 聊天接口
```bash
# Flask聊天API
POST /api/chat
{
    "message": "我头疼怎么办？",
    "session_id": "optional_session_id",
    "username": "optional_username"
}

# FastAPI知识库问答
POST /api/kb_chat/
{
    "query": "高血压的症状有哪些？",
    "conversation_id": "optional_conversation_id"
}
```

#### 用户认证
```bash
# 用户注册
POST /api/auth/register
{
    "username": "patient01",
    "password": "password123",
    "role": "patient"
}

# 用户登录
POST /api/auth/login
{
    "username": "patient01",
    "password": "password123"
}
```

### 数据管理

#### 配置数据源
编辑 `app/configs/sources.yaml`：
```yaml
data_sources:
  filesystem:
    - name: "医学文档库"
      type: "filesystem"
      enabled: true
      config:
        path: "./data_sample"
        file_types: ["*.pdf", "*.docx", "*.md"]
        recursive: true

  csv_excel:
    - name: "医学数据表"
      type: "csv_excel"
      enabled: true
      config:
        path: "./data/medical_data.csv"
        encoding: "utf-8"
```

#### 数据抽取命令
```bash
# 列出所有数据源
python scripts/extract_and_ingest.py --list

# 测试数据源连接
python scripts/extract_and_ingest.py --test "医学文档库"

# 按类型抽取数据
python scripts/extract_and_ingest.py --type filesystem
python scripts/extract_and_ingest.py --type csv_excel

# 按名称抽取数据
python scripts/extract_and_ingest.py --name "医学文档库"
```

## 🔧 配置说明

### 环境变量配置

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `DATABASE_URL` | 数据库连接URL | `sqlite:///medical_chatbot.db` |
| `SECRET_KEY` | Flask密钥 | 随机生成 |
| `LLM_API_URL` | LLM服务地址 | - |
| `LLM_API_KEY` | LLM API密钥 | - |
| `DEBUG` | 调试模式 | `True` |
| `CHROMA_DB_PATH` | 向量数据库路径 | `./chroma_db` |

### 应用配置

主要配置文件位于 `app/configs/` 目录：
- `config.py`: 应用主配置
- `logging_config.py`: 日志配置
- `sources.yaml`: 数据源配置

## 🧪 开发和测试

### 开发环境设置

```bash
# 安装开发依赖
poetry install --with dev

# 启动开发服务器
poetry run poe start

# 或使用Flask开发服务器
python run.py
```

### 数据库操作

```bash
# 创建迁移
flask db migrate -m "描述信息"

# 应用迁移
flask db upgrade

# 回滚迁移
flask db downgrade
```

### 日志查看

应用日志保存在 `logs/app.log`，可以通过以下方式查看：
```bash
# 实时查看日志
tail -f logs/app.log

# 查看错误日志
grep ERROR logs/app.log
```

## 📁 项目结构详解

### 核心模块说明

- **`app/core/`**: 核心功能模块
  - `chains/`: LangChain链定义
  - `extractors/`: 各种数据源抽取器
  - `etl/`: 数据处理管道
  - `memory/`: 对话记忆管理

- **`app/services/`**: 业务服务层
  - `llm_service.py`: 大语言模型服务
  - `knowledge_service.py`: 知识库管理服务
  - `data_extraction_service.py`: 数据抽取服务

- **`app/models/`**: 数据模型定义
  - `user.py`: 用户和用户资料模型
  - `chat_session.py`: 聊天会话和消息模型
  - `token.py`: 认证令牌模型

### 前端组件

- **`app/frontend/components/`**: 页面组件
  - `chat/`: 聊天界面组件
  - `doctor/`: 医生仪表板组件
  - `index/`: 首页组件

## 🤝 贡献指南

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

### 代码规范

- 遵循 PEP 8 Python 代码规范
- 添加适当的注释和文档字符串
- 编写单元测试
- 确保所有测试通过

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持与反馈

如果您遇到问题或有建议，请：

1. 查看 [Issues](../../issues) 页面
2. 创建新的 Issue 描述问题
3. 联系项目维护者

## 🔮 未来规划

- [ ] 集成更多LLM模型支持
- [ ] 添加语音交互功能
- [ ] 实现多语言支持
- [ ] 增强医疗专业术语处理
- [ ] 添加图像识别功能
- [ ] 实现移动端APP
- [ ] 集成第三方医疗API
- [ ] 添加数据分析仪表板

---

**慧问医答** - 让AI为医疗健康服务 🏥✨