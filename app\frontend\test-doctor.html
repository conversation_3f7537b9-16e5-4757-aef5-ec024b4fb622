<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>医生工作台测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .patient-list { border: 1px solid #ccc; padding: 10px; margin: 10px 0; }
        .patient-item { padding: 10px; border-bottom: 1px solid #eee; cursor: pointer; }
        .patient-item:hover { background-color: #f5f5f5; }
        .chat-messages { border: 1px solid #ccc; padding: 10px; height: 300px; overflow-y: auto; }
        .message { margin: 5px 0; padding: 5px; border-radius: 5px; }
        .user-message { background-color: #e3f2fd; }
        .bot-message { background-color: #f3e5f5; }
    </style>
</head>
<body>
    <h1>医生工作台测试页面</h1>
    
    <div>
        <h2>患者列表</h2>
        <div id="patientList" class="patient-list">
            <div>正在加载患者列表...</div>
        </div>
    </div>
    
    <div>
        <h2>聊天消息</h2>
        <div id="chatMessages" class="chat-messages">
            <div>请选择患者查看聊天记录</div>
        </div>
    </div>

    <script>
        console.log('🚀 测试页面开始加载');
        
        // 全局变量
        let currentSessionId = null;
        
        // 加载患者列表
        async function loadPatientList() {
            console.log('🔄 开始加载患者列表...');
            try {
                console.log('📡 发送请求到: /api/doctor/patients');
                const response = await fetch('/api/doctor/patients');
                console.log('📥 收到响应，状态码:', response.status);
                
                const data = await response.json();
                console.log('📋 解析的数据:', data);
                
                if (data.status === 'success') {
                    console.log('✅ 成功获取患者数据，患者数量:', data.patients ? data.patients.length : 0);
                    displayPatientList(data.patients);
                } else {
                    console.error('❌ API返回错误:', data.error || '未知错误');
                }
            } catch (error) {
                console.error('💥 请求异常:', error);
                const patientList = document.getElementById('patientList');
                if (patientList) {
                    patientList.innerHTML = '<div style="color: red;">加载失败: ' + error.message + '</div>';
                }
            }
        }
        
        // 显示患者列表
        function displayPatientList(patients) {
            console.log('🎨 开始显示患者列表，患者数据:', patients);
            
            const patientList = document.getElementById('patientList');
            console.log('📋 找到患者列表元素:', patientList ? '是' : '否');
            
            if (!patientList) {
                console.error('❌ 未找到patientList元素');
                return;
            }
            
            if (!patients || patients.length === 0) {
                console.log('📭 没有患者数据，显示空状态');
                patientList.innerHTML = '<div>暂无患者咨询</div>';
                return;
            }
            
            console.log('🔄 清空现有列表内容');
            patientList.innerHTML = '';
            
            console.log('👥 开始渲染', patients.length, '个患者');
            patients.forEach((patient, index) => {
                console.log(`👤 渲染患者 ${index + 1}:`, patient);
                
                const patientElement = document.createElement('div');
                patientElement.className = 'patient-item';
                patientElement.innerHTML = `
                    <strong>${patient.patient_name}</strong><br>
                    <small>最后消息: ${patient.last_message}</small><br>
                    <small>消息数量: ${patient.message_count} | 优先级: ${patient.priority}</small>
                `;
                
                patientElement.onclick = () => {
                    console.log('👤 点击患者:', patient.patient_name);
                    selectPatient(patient.session_id, patient.patient_name);
                };
                
                patientList.appendChild(patientElement);
            });
            
            console.log('✅ 患者列表渲染完成');
        }
        
        // 选择患者
        function selectPatient(sessionId, patientName) {
            console.log('👤 选择患者:', { sessionId, patientName });
            currentSessionId = sessionId;
            loadChatHistory(sessionId);
        }
        
        // 加载聊天历史
        async function loadChatHistory(sessionId) {
            console.log('💬 开始加载聊天历史，会话ID:', sessionId);
            try {
                const url = `/api/doctor/patient/${sessionId}/messages`;
                console.log('📡 发送请求到:', url);
                
                const response = await fetch(url);
                console.log('📥 收到响应，状态码:', response.status);
                
                const data = await response.json();
                console.log('📋 解析的消息数据:', data);
                
                if (data.status === 'success') {
                    console.log('✅ 成功获取消息，消息数量:', data.messages ? data.messages.length : 0);
                    displayChatMessages(data.messages);
                } else {
                    console.error('❌ 加载聊天历史失败:', data.error);
                }
            } catch (error) {
                console.error('💥 加载聊天历史异常:', error);
            }
        }
        
        // 显示聊天消息
        function displayChatMessages(messages) {
            console.log('💬 开始显示聊天消息，消息数据:', messages);
            
            const chatMessages = document.getElementById('chatMessages');
            console.log('📋 找到聊天消息容器:', chatMessages ? '是' : '否');
            
            if (!chatMessages) {
                console.error('❌ 未找到chatMessages元素');
                return;
            }
            
            console.log('🔄 清空现有消息内容');
            chatMessages.innerHTML = '';
            
            if (!messages || messages.length === 0) {
                console.log('📭 没有消息数据，显示空状态');
                chatMessages.innerHTML = '<div>暂无聊天记录</div>';
                return;
            }
            
            console.log('💬 开始渲染', messages.length, '条消息');
            messages.forEach((message, index) => {
                console.log(`📝 渲染消息 ${index + 1}:`, message);
                
                const messageElement = document.createElement('div');
                messageElement.className = `message ${message.role}-message`;
                messageElement.innerHTML = `
                    <strong>${message.role === 'user' ? '患者' : 'AI助手'}:</strong><br>
                    ${message.content}<br>
                    <small>${message.timestamp}</small>
                `;
                
                chatMessages.appendChild(messageElement);
            });
            
            console.log('✅ 消息渲染完成');
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 DOM 内容已加载，开始初始化');
            loadPatientList();
        });
    </script>
</body>
</html>
