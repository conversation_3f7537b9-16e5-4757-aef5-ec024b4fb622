"""Confluence数据抽取器"""

from typing import Dict, Any, List
import pandas as pd

try:
    from atlassian import Confluence
except ImportError:
    Confluence = None

from .base_extractor import BaseExtractor


class ConfluenceExtractor(BaseExtractor):
    """Confluence数据抽取器
    
    从Confluence知识库中抽取页面内容
    """
    
    def __init__(self, config: Dict[str, Any], metadata: Dict[str, Any]):
        super().__init__(config, metadata)
        self.confluence = None
    
    def extract(self) -> pd.DataFrame:
        """从Confluence抽取数据"""
        if Confluence is None:
            raise ImportError("需要安装 atlassian-python-api 来访问Confluence")
        
        self.validate_config(['base_url', 'username', 'api_token', 'space_key'])
        
        # 初始化Confluence客户端
        self.confluence = Confluence(
            url=self.config['base_url'],
            username=self.config['username'],
            password=self.config['api_token'],
            cloud=True  # 假设使用Confluence Cloud
        )
        
        space_key = self.config['space_key']
        max_pages = self.config.get('max_pages', 100)
        
        try:
            # 获取空间中的所有页面
            pages = self._get_space_pages(space_key, max_pages)
            
            data = []
            for page in pages:
                try:
                    page_data = self._extract_page_content(page)
                    if page_data:
                        data.append(page_data)
                except Exception as e:
                    print(f"处理Confluence页面 {page.get('title', 'Unknown')} 时出错: {str(e)}")
                    continue
            
            print(f"成功处理 {len(data)} 个Confluence页面")
            return self._create_standardized_dataframe(data)
            
        except Exception as e:
            print(f"访问Confluence空间 {space_key} 时出错: {str(e)}")
            return self._create_standardized_dataframe([])
    
    def _get_space_pages(self, space_key: str, max_pages: int) -> List[Dict[str, Any]]:
        """获取空间中的所有页面"""
        try:
            # 获取空间中的页面列表
            pages = self.confluence.get_all_pages_from_space(
                space=space_key,
                start=0,
                limit=max_pages,
                expand='version,body.storage,space,ancestors'
            )
            
            return pages
            
        except Exception as e:
            print(f"获取Confluence空间页面列表失败: {str(e)}")
            return []
    
    def _extract_page_content(self, page: Dict[str, Any]) -> Dict[str, Any]:
        """从Confluence页面提取内容"""
        try:
            page_id = page['id']
            title = page['title']
            
            # 获取页面的完整内容
            full_page = self.confluence.get_page_by_id(
                page_id, 
                expand='body.storage,version,space,ancestors'
            )
            
            # 提取HTML内容并转换为纯文本
            html_content = full_page.get('body', {}).get('storage', {}).get('value', '')
            text_content = self._html_to_text(html_content)
            
            if not text_content.strip():
                print(f"页面 {title} 内容为空，跳过")
                return None
            
            # 构建页面元数据
            page_metadata = {
                'page_id': page_id,
                'title': title,
                'space_key': full_page.get('space', {}).get('key', ''),
                'space_name': full_page.get('space', {}).get('name', ''),
                'page_url': f"{self.config['base_url']}/pages/viewpage.action?pageId={page_id}",
                'version': full_page.get('version', {}).get('number', 1),
                'created_date': full_page.get('version', {}).get('when', ''),
                'content_length': len(text_content),
                'ancestors': self._get_page_ancestors(full_page.get('ancestors', []))
            }
            
            return {
                'content': self._clean_content(text_content),
                'metadata': page_metadata
            }
            
        except Exception as e:
            print(f"提取页面内容失败: {str(e)}")
            return None
    
    def _html_to_text(self, html_content: str) -> str:
        """将HTML内容转换为纯文本"""
        if not html_content:
            return ""
        
        try:
            # 使用BeautifulSoup解析HTML
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 移除脚本和样式
            for script in soup(["script", "style"]):
                script.decompose()
            
            # 提取文本
            text = soup.get_text()
            
            # 清理文本
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            
            return text
            
        except ImportError:
            # 如果没有BeautifulSoup，使用简单的正则表达式清理HTML
            import re
            text = re.sub('<[^<]+?>', '', html_content)
            text = re.sub(r'\s+', ' ', text)
            return text.strip()
        except Exception as e:
            print(f"HTML转换失败: {str(e)}")
            return html_content
    
    def _get_page_ancestors(self, ancestors: List[Dict[str, Any]]) -> List[str]:
        """获取页面的祖先路径"""
        ancestor_titles = []
        for ancestor in ancestors:
            if 'title' in ancestor:
                ancestor_titles.append(ancestor['title'])
        return ancestor_titles
    
    def test_connection(self) -> bool:
        """测试Confluence连接"""
        try:
            if self.confluence is None:
                self.confluence = Confluence(
                    url=self.config['base_url'],
                    username=self.config['username'],
                    password=self.config['api_token'],
                    cloud=True
                )
            
            # 尝试获取用户信息
            user_info = self.confluence.get_user_details_by_username(
                self.config['username']
            )
            print(f"成功连接到Confluence，用户: {user_info.get('displayName', 'Unknown')}")
            return True
            
        except Exception as e:
            print(f"Confluence连接失败: {str(e)}")
            return False 