from typing import Dict
from langchain_core.chat_history import BaseChatMessageHistory
from langchain_community.chat_message_histories import ChatMessageHistory

_sessions: Dict[str, BaseChatMessageHistory] = {}


def get_session_history(session_id: str) -> BaseChatMessageHistory:
    if session_id not in _sessions:
        _sessions[session_id] = ChatMessageHistory()
        print(f"--- New session created: {session_id} ---")
    else:
        print(f"--- Loaded existing session: {session_id} ---")
    return _sessions[session_id]
