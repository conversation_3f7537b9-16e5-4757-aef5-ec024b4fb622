<style>
    /* 现代化聊天界面样式 - 与主页风格保持一致 */
    :root {
        /* 主色调 - 与主页保持一致 */
        --primary: #3b82f6;
        --primary-light: #60a5fa;
        --primary-dark: #1d4ed8;
        --secondary: #10b981;
        --accent: #f59e0b;
        --success: #10b981;
        --warning: #f59e0b;
        --danger: #ef4444;

        /* 中性色系 - 浅色主题 */
        --white: #ffffff;
        --gray-50: #f8fafc;
        --gray-100: #f1f5f9;
        --gray-200: #e2e8f0;
        --gray-300: #cbd5e1;
        --gray-400: #94a3b8;
        --gray-500: #64748b;
        --gray-600: #475569;
        --gray-700: #334155;
        --gray-800: #1e293b;
        --gray-900: #0f172a;

        /* 背景色 */
        --background-color: var(--gray-50);
        --sidebar-bg: rgba(255, 255, 255, 0.95);
        --chat-bg: var(--white);
        --border-color: var(--gray-200);

        /* 文字颜色 */
        --text-primary: var(--gray-900);
        --text-secondary: var(--gray-600);
        --text-muted: var(--gray-500);

        /* 消息气泡颜色 */
        --message-user-bg: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
        --message-user-text: var(--white);
        --message-bot-bg: var(--white);
        --message-bot-text: var(--text-primary);
        --message-doctor-bg: linear-gradient(135deg, var(--success) 0%, #34d399 100%);
        --message-doctor-text: var(--white);

        /* 阴影系统 */
        --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
        --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
        --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
        --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
        --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);

        /* 圆角系统 */
        --radius-xs: 4px;
        --radius-sm: 8px;
        --radius-md: 12px;
        --radius-lg: 16px;
        --radius-xl: 20px;
        --radius-2xl: 24px;
        --radius-full: 9999px;

        /* 动画 */
        --transition-fast: all 0.15s ease;
        --transition-normal: all 0.3s ease;
        --transition-slow: all 0.5s ease;

        /* 渐变背景 */
        --hero-gradient: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
        --glass-bg: rgba(255, 255, 255, 0.95);
        --glass-border: rgba(255, 255, 255, 0.2);
    }

    body {
        font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
        margin: 0;
        padding: 0;
        background: var(--hero-gradient);
        color: var(--text-primary);
        line-height: 1.6;
        font-weight: 400;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    /* 布局样式 */
    .chat-layout {
        display: flex;
        width: 100%;
        height: 100vh;
        overflow: hidden;
        position: relative;
    }

    /* 添加背景装饰 */
    .chat-layout::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -20%;
        width: 40%;
        height: 120%;
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.03) 0%, rgba(147, 51, 234, 0.03) 100%);
        border-radius: 50%;
        filter: blur(60px);
        z-index: 1;
        pointer-events: none;
    }

    .chat-layout::after {
        content: '';
        position: absolute;
        bottom: -30%;
        left: -10%;
        width: 30%;
        height: 60%;
        background: radial-gradient(circle, rgba(16, 185, 129, 0.05) 0%, transparent 70%);
        z-index: 1;
        pointer-events: none;
    }

    /* 侧边栏样式 - 融合设计，无明显边界 */
    .sidebar {
        width: 280px;
        background: transparent;
        border-right: none;
        display: flex;
        flex-direction: column;
        transition: var(--transition-normal);
        position: relative;
        z-index: 10;
        height: 100vh;
        overflow: hidden;
    }

    .sidebar-header {
        padding: 32px 24px 24px 24px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        background: transparent;
    }

    .sidebar-header h2 {
        margin: 0;
        font-size: 28px;
        font-weight: 800;
        color: var(--primary);
        letter-spacing: -0.025em;
        text-align: center;
    }

    .sidebar-actions {
        padding: 20px 24px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        background: transparent;
    }

    .new-chat-action {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 16px 20px;
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
        border: none;
        border-radius: var(--radius-lg);
        color: white;
        cursor: pointer;
        transition: var(--transition-normal);
        font-weight: 600;
        font-size: 15px;
        box-shadow: var(--shadow-md);
    }

    .new-chat-action:hover {
        background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary) 100%);
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .new-chat-action svg {
        flex-shrink: 0;
    }

    .sidebar-content {
        flex-grow: 1;
        overflow-y: auto;
        padding: 20px 24px;
        scrollbar-width: thin;
        background: transparent;
    }

    .sidebar-content::-webkit-scrollbar {
        width: 6px;
    }

    .sidebar-content::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.05);
        border-radius: var(--radius-full);
    }

    .sidebar-content::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.2);
        border-radius: var(--radius-full);
        transition: background var(--transition-normal);
    }

    .sidebar-content::-webkit-scrollbar-thumb:hover {
        background: rgba(0, 0, 0, 0.3);
    }

    /* 现代按钮样式 */
    .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        padding: 12px 20px;
        border-radius: var(--radius-md);
        font-weight: 600;
        font-size: 14px;
        cursor: pointer;
        transition: var(--transition-normal);
        outline: none;
        border: none;
        position: relative;
        overflow: hidden;
        text-decoration: none;
        font-family: inherit;
    }

    .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn:hover::before {
        left: 100%;
    }

    .btn svg {
        width: 18px;
        height: 18px;
        flex-shrink: 0;
    }

    .btn.full-width {
        width: 100%;
        justify-content: center;
    }

    .btn.primary-btn {
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
        color: white;
        box-shadow: var(--shadow-md);
        border: none;
    }

    .btn.primary-btn:hover {
        background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary) 100%);
        box-shadow: var(--shadow-lg);
        transform: translateY(-2px);
    }

    .btn.secondary-btn {
        background: rgba(255, 255, 255, 0.9);
        color: var(--primary);
        border: 2px solid var(--primary);
        box-shadow: var(--shadow-sm);
        backdrop-filter: blur(10px);
    }

    .btn.secondary-btn:hover {
        background: var(--white);
        color: var(--primary-dark);
        border-color: var(--primary-dark);
        box-shadow: var(--shadow-md);
        transform: translateY(-2px);
    }

    .btn.clear-btn {
        color: var(--text-secondary);
        background: rgba(255, 255, 255, 0.7);
        border: 1px solid var(--border-color);
        backdrop-filter: blur(10px);
    }

    .btn.clear-btn:hover {
        background: var(--white);
        color: var(--text-primary);
        border-color: var(--gray-300);
        transform: translateY(-1px);
        box-shadow: var(--shadow-sm);
    }

    .btn.back-btn {
        color: var(--text-secondary);
        background: rgba(255, 255, 255, 0.7);
        border: 1px solid var(--border-color);
        backdrop-filter: blur(10px);
    }

    .btn.back-btn:hover {
        background: var(--white);
        color: var(--text-primary);
        border-color: var(--gray-300);
        transform: translateY(-1px);
        box-shadow: var(--shadow-sm);
    }

    /* 历史会话项样式 - 融合设计，无明显边界 */
    .history-item {
        padding: 16px 20px;
        margin-bottom: 6px;
        cursor: pointer;
        transition: var(--transition-normal);
        background: rgba(255, 255, 255, 0.3);
        border-radius: var(--radius-md);
        border-left: 3px solid transparent;
        position: relative;
        border: none;
    }

    .history-item:hover {
        background: rgba(255, 255, 255, 0.5);
        transform: translateX(4px);
    }

    .history-item.active {
        background: rgba(255, 255, 255, 0.7);
        border-left-color: var(--primary);
    }

    .history-item-title {
        font-weight: 600;
        margin-bottom: 6px;
        color: var(--text-primary);
        font-size: 14px;
        line-height: 1.4;
    }

    .history-item-preview {
        color: var(--text-secondary);
        font-size: 12px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        line-height: 1.4;
        margin-bottom: 8px;
    }

    .history-item-meta {
        display: flex;
        justify-content: space-between;
        font-size: 11px;
        color: var(--text-muted);
    }

    .history-placeholder {
        text-align: center;
        color: var(--text-secondary);
        padding: 40px 20px;
        font-size: 14px;
        font-weight: 500;
    }

    /* 主内容区样式 - 现代玻璃态设计 */
    .main-content {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        height: 100vh;
        overflow: hidden;
        background: transparent;
        position: relative;
        z-index: 2;
    }

    .chat-page {
        height: 100%;
        display: flex;
        flex-direction: column;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: var(--radius-2xl) 0 0 var(--radius-2xl);
        margin: 16px 0 16px 0;
        box-shadow: none;
        border: 1px solid rgba(255, 255, 255, 0.3);
        overflow: hidden;
        position: relative;
    }

    .chat-page::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
        pointer-events: none;
    }

    /* 聊天头部样式 - 现代设计 */
    .chat-header {
        padding: 20px 32px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        z-index: 5;
        box-shadow: var(--shadow-sm);
        position: relative;
    }

    .chat-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
        pointer-events: none;
    }

    .chat-header-title {
        position: relative;
        z-index: 1;
    }

    .chat-header-title h1 {
        margin: 0;
        font-size: 24px;
        font-weight: 700;
        color: var(--primary);
        display: flex;
        align-items: center;
        letter-spacing: -0.025em;
    }

    .chat-header-title h1:before {
        content: "";
        display: inline-block;
        width: 6px;
        height: 24px;
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
        margin-right: 12px;
        border-radius: var(--radius-sm);
        box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
    }

    .chat-header-title p {
        margin: 6px 0 0 18px;
        font-size: 14px;
        color: var(--text-secondary);
        font-weight: 500;
    }

    .chat-actions {
        display: flex;
        gap: 12px;
        position: relative;
        z-index: 1;
    }

    /* 聊天容器样式 - 现代背景设计 */
    .chat-container {
        flex-grow: 1;
        padding: 32px;
        overflow-y: auto;
        background: transparent;
        background-image:
            radial-gradient(rgba(59, 130, 246, 0.03) 1px, transparent 1px),
            radial-gradient(rgba(59, 130, 246, 0.02) 1px, transparent 1px);
        background-size: 60px 60px;
        background-position: 0 0, 30px 30px;
        scrollbar-width: thin;
        position: relative;
    }

    .chat-container::-webkit-scrollbar {
        width: 6px;
    }

    .chat-container::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: var(--radius-full);
    }

    .chat-container::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.1);
        border-radius: var(--radius-full);
        transition: background var(--transition-normal);
    }

    .chat-container::-webkit-scrollbar-thumb:hover {
        background: rgba(0, 0, 0, 0.2);
    }

    /* 现代消息样式 */
    .user-message, .bot-message, .doctor-message {
        margin-bottom: 32px;
        max-width: 80%;
        animation: messageSlideIn 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        position: relative;
    }

    @keyframes messageSlideIn {
        from {
            opacity: 0;
            transform: translateY(20px) scale(0.95);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    .user-message {
        display: flex;
        flex-direction: row-reverse;
        margin-left: auto;
        align-items: flex-end;
    }

    .bot-message {
        display: flex;
        flex-direction: row;
        align-items: flex-end;
    }

    .doctor-message {
        display: flex;
        flex-direction: row-reverse;
        margin-left: auto;
        align-items: flex-end;
    }

    .message-avatar {
        width: 44px;
        height: 44px;
        border-radius: var(--radius-full);
        overflow: hidden;
        background: var(--white);
        box-shadow: var(--shadow-md);
        border: 3px solid var(--white);
        transition: var(--transition-normal);
        flex-shrink: 0;
        position: relative;
    }

    .message-avatar::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
        border-radius: var(--radius-full);
        pointer-events: none;
    }

    .message-avatar:hover {
        transform: scale(1.1);
        box-shadow: var(--shadow-lg);
    }

    .message-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: var(--radius-full);
    }

    .user-message .message-avatar {
        margin-left: 16px;
        border-color: var(--primary-light);
    }

    .bot-message .message-avatar {
        margin-right: 16px;
        border-color: var(--gray-200);
    }

    .doctor-message .message-avatar {
        margin-left: 16px;
        border-color: #34d399;
    }

    .message-content {
        padding: 18px 24px;
        border-radius: var(--radius-xl);
        position: relative;
        box-shadow: var(--shadow-md);
        transition: var(--transition-normal);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: hidden;
    }

    .message-content::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
        pointer-events: none;
    }

    .message-content:hover {
        box-shadow: var(--shadow-lg);
        transform: translateY(-2px);
    }

    .user-message .message-content {
        background: var(--message-user-bg);
        color: var(--message-user-text);
        border-top-right-radius: var(--radius-sm);
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 8px 32px rgba(59, 130, 246, 0.2);
    }

    .user-message .message-content:after {
        content: "";
        position: absolute;
        right: -10px;
        bottom: 12px;
        width: 20px;
        height: 20px;
        background: var(--primary);
        transform: rotate(45deg);
        z-index: -1;
        border-radius: 0 0 var(--radius-sm) 0;
    }

    .bot-message .message-content {
        background: var(--message-bot-bg);
        color: var(--message-bot-text);
        border-top-left-radius: var(--radius-sm);
        border: 1px solid var(--gray-200);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    }

    .bot-message .message-content:after {
        content: "";
        position: absolute;
        left: -10px;
        bottom: 12px;
        width: 20px;
        height: 20px;
        background: var(--white);
        transform: rotate(45deg);
        z-index: -1;
        border-radius: 0 0 0 var(--radius-sm);
        border: 1px solid var(--gray-200);
    }

    .doctor-message .message-content {
        background: var(--message-doctor-bg);
        color: var(--message-doctor-text);
        border-top-right-radius: var(--radius-sm);
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 8px 32px rgba(16, 185, 129, 0.2);
    }

    .doctor-message .message-content:after {
        content: "";
        position: absolute;
        right: -10px;
        bottom: 12px;
        width: 20px;
        height: 20px;
        background: var(--success);
        transform: rotate(45deg);
        z-index: -1;
        border-radius: 0 0 var(--radius-sm) 0;
    }

    .message-text {
        line-height: 1.7;
        white-space: pre-wrap;
        word-break: break-word;
        font-size: 15px;
        font-weight: 500;
        position: relative;
        z-index: 1;
    }

    .message-time {
        font-size: 12px;
        opacity: 0.8;
        margin-top: 8px;
        text-align: right;
        font-weight: 500;
        position: relative;
        z-index: 1;
    }

    /* 现代快速回复按钮 */
    .quick-replies {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 20px;
        margin-bottom: 20px;
        padding: 0 16px;
    }

    .quick-reply-btn {
        background: rgba(255, 255, 255, 0.9);
        border: 2px solid var(--primary);
        border-radius: var(--radius-full);
        padding: 10px 18px;
        font-size: 13px;
        font-weight: 600;
        color: var(--primary);
        cursor: pointer;
        transition: var(--transition-normal);
        backdrop-filter: blur(10px);
        position: relative;
        overflow: hidden;
    }

    .quick-reply-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        transition: left 0.5s;
    }

    .quick-reply-btn:hover::before {
        left: 100%;
    }

    .quick-reply-btn:hover {
        background: var(--primary);
        color: var(--white);
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    /* 现代输入区域样式 */
    .chat-input-container {
        display: flex;
        flex-direction: column;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        padding: 24px 32px;
        border-top: 1px solid rgba(255, 255, 255, 0.2);
        position: relative;
        z-index: 5;
        box-shadow: 0 -8px 32px rgba(0, 0, 0, 0.08);
        border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
        overflow: hidden;
    }

    .chat-input-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
        pointer-events: none;
    }

    .input-tools {
        display: flex;
        margin-bottom: 12px;
        padding: 0 8px;
        position: relative;
        z-index: 1;
    }

    .tool-button {
        background: rgba(255, 255, 255, 0.8);
        border: 1px solid var(--border-color);
        color: var(--text-secondary);
        padding: 10px;
        cursor: pointer;
        border-radius: var(--radius-full);
        transition: var(--transition-normal);
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(10px);
        margin-right: 8px;
    }

    .tool-button:hover {
        background: var(--primary);
        color: var(--white);
        border-color: var(--primary);
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    .tool-button svg {
        width: 20px;
        height: 20px;
    }

    .input-main {
        display: flex;
        align-items: flex-end;
        gap: 16px;
        position: relative;
        z-index: 1;
    }

    .chat-input {
        flex-grow: 1;
        border: 2px solid var(--border-color);
        border-radius: var(--radius-xl);
        padding: 16px 20px;
        font-size: 15px;
        font-weight: 500;
        min-height: 24px;
        max-height: 200px;
        resize: none;
        outline: none;
        transition: var(--transition-normal);
        box-shadow: var(--shadow-sm);
        font-family: inherit;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        line-height: 1.6;
    }

    .chat-input:focus {
        border-color: var(--primary);
        box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
        background: var(--white);
    }

    .chat-input::placeholder {
        color: var(--text-muted);
        font-weight: 500;
    }

    .send-button {
        width: 52px;
        height: 52px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
        border: none;
        border-radius: var(--radius-full);
        color: white;
        cursor: pointer;
        transition: var(--transition-normal);
        outline: none;
        flex-shrink: 0;
        box-shadow: var(--shadow-lg);
        position: relative;
        overflow: hidden;
    }

    .send-button::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        transition: left 0.5s;
    }

    .send-button:hover::before {
        left: 100%;
    }

    .send-button:hover:not(:disabled) {
        background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary) 100%);
        box-shadow: 0 12px 24px rgba(59, 130, 246, 0.4);
        transform: translateY(-3px) scale(1.05);
    }

    .send-button:active:not(:disabled) {
        transform: translateY(-1px) scale(1.02);
    }

    .send-button:disabled {
        background: var(--gray-300);
        cursor: not-allowed;
        box-shadow: var(--shadow-sm);
    }

    .send-button svg {
        width: 22px;
        height: 22px;
        transition: var(--transition-normal);
    }

    .send-button:hover:not(:disabled) svg {
        transform: translateX(2px);
    }

    /* 现代加载指示器样式 */
    .loading-indicator {
        display: flex;
        padding: 24px 0;
        justify-content: center;
        align-items: center;
    }

    .dot {
        width: 10px;
        height: 10px;
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
        border-radius: var(--radius-full);
        margin: 0 6px;
        animation: modernBounce 1.4s infinite ease-in-out;
        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
    }

    .dot:nth-child(1) { animation-delay: 0s; }
    .dot:nth-child(2) { animation-delay: 0.2s; }
    .dot:nth-child(3) { animation-delay: 0.4s; }

    @keyframes modernBounce {
        0%, 100% {
            transform: translateY(0) scale(1);
            opacity: 0.7;
        }
        50% {
            transform: translateY(-12px) scale(1.2);
            opacity: 1;
        }
    }

    /* 现代健康状态指示器 */
    .health-indicator {
        display: flex;
        align-items: center;
        margin-top: 8px;
        margin-left: 20px;
        font-size: 13px;
        font-weight: 600;
        color: var(--text-secondary);
    }

    .health-indicator-dot {
        width: 12px;
        height: 12px;
        border-radius: var(--radius-full);
        background: linear-gradient(135deg, var(--success) 0%, #34d399 100%);
        margin-right: 8px;
        animation: pulse 2s infinite;
        box-shadow: 0 0 12px rgba(16, 185, 129, 0.4);
    }

    @keyframes pulse {
        0%, 100% {
            transform: scale(1);
            opacity: 1;
        }
        50% {
            transform: scale(1.1);
            opacity: 0.8;
        }
    }

    /* 现代加载动画样式 - 与主页风格一致 */
    .chat-loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: var(--hero-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        transition: all 0.5s ease;
    }

    .chat-loading-screen::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -20%;
        width: 40%;
        height: 120%;
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.03) 0%, rgba(147, 51, 234, 0.03) 100%);
        border-radius: 50%;
        filter: blur(60px);
        z-index: 1;
    }

    .loading-content {
        text-align: center;
        color: var(--primary);
        transform: translateY(-20px);
        position: relative;
        z-index: 2;
    }

    .loading-logo {
        position: relative;
        margin-bottom: 3rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 2rem;
    }

    .loading-logo h2 {
        font-size: 3rem;
        font-weight: 900;
        margin: 0;
        letter-spacing: -0.025em;
        color: var(--primary);
        text-shadow: 0 2px 4px rgba(255, 255, 255, 0.8);
    }

    .loading-spinner {
        width: 60px;
        height: 60px;
        border: 5px solid rgba(59, 130, 246, 0.1);
        border-radius: var(--radius-full);
        border-top-color: var(--primary);
        animation: modernSpin 1.2s linear infinite;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
    }

    @keyframes modernSpin {
        to {
            transform: rotate(360deg);
        }
    }

    .loading-text {
        font-size: 1.2rem;
        margin-bottom: 2.5rem;
        color: var(--text-secondary);
        min-height: 1.5rem;
        font-weight: 600;
        text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
    }

    .loading-progress {
        width: 320px;
        height: 8px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: var(--radius-full);
        overflow: hidden;
        margin: 0 auto;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
    }

    .progress-bar {
        height: 100%;
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
        border-radius: var(--radius-full);
        transition: width 0.4s ease;
        width: 0%;
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
    }

    /* 现代响应式样式 */
    @media (max-width: 1024px) {
        .sidebar {
            width: 260px;
        }

        .chat-container {
            padding: 24px;
        }

        .chat-header {
            padding: 16px 24px;
        }

        .chat-input-container {
            padding: 20px 24px;
        }
    }

    @media (max-width: 768px) {
        .sidebar {
            width: 240px;
        }

        .chat-page {
            margin: 12px 12px 12px 0;
            border-radius: var(--radius-xl) 0 0 var(--radius-xl);
        }

        .message-content {
            padding: 16px 20px;
        }

        .user-message, .bot-message, .doctor-message {
            max-width: 90%;
            margin-bottom: 24px;
        }

        .chat-container {
            padding: 20px;
        }

        .chat-header {
            padding: 16px 20px;
        }

        .chat-header-title h1 {
            font-size: 20px;
        }

        .chat-input-container {
            padding: 18px 20px;
        }

        .send-button {
            width: 48px;
            height: 48px;
        }

        .loading-progress {
            width: 260px;
        }

        .loading-logo h2 {
            font-size: 2.5rem;
        }
    }

    @media (max-width: 576px) {
        .chat-layout {
            flex-direction: column;
        }

        .sidebar {
            width: 100%;
            height: auto;
            max-height: 50vh;
            order: 2;
            border-radius: 0;
            margin: 0;
        }

        .main-content {
            order: 1;
            height: auto;
        }

        .chat-page {
            margin: 0;
            border-radius: 0;
        }

        .chat-container {
            max-height: calc(50vh - 120px);
            padding: 16px;
        }

        .sidebar-actions {
            flex-direction: row;
            flex-wrap: wrap;
        }

        .message-content {
            padding: 14px 18px;
        }

        .user-message, .bot-message, .doctor-message {
            max-width: 95%;
            margin-bottom: 20px;
        }

        .message-avatar {
            width: 36px;
            height: 36px;
        }

        .chat-header {
            padding: 14px 16px;
        }

        .chat-header-title h1 {
            font-size: 18px;
        }

        .chat-input-container {
            padding: 16px;
        }

        .chat-input {
            padding: 14px 16px;
            font-size: 14px;
        }

        .send-button {
            width: 44px;
            height: 44px;
        }

        .loading-progress {
            width: 240px;
        }

        .loading-logo h2 {
            font-size: 2rem;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
        }
    }

    @media (max-width: 480px) {
        .chat-page {
            margin: 8px;
        }

        .sidebar {
            margin: 0 8px;
        }

        .chat-container {
            padding: 12px;
        }

        .chat-header {
            padding: 12px;
        }

        .chat-input-container {
            padding: 12px;
        }

        .message-content {
            padding: 12px 16px;
        }

        .loading-progress {
            width: 200px;
        }

        .loading-logo h2 {
            font-size: 1.8rem;
        }
    }
</style>