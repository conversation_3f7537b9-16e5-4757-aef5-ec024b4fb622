import time
import secrets
import logging
from .database import db
from datetime import datetime, timedelta
import string

# 设置日志记录器
logger = logging.getLogger(__name__)

class Token(db.Model):
    """用户认证令牌模型"""
    __tablename__ = 'tokens'
    
    id = db.Column(db.Integer, primary_key=True)
    token = db.Column(db.String(128), unique=True, nullable=False, index=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    created_at = db.Column(db.Float, default=time.time)
    expires_at = db.Column(db.Float, nullable=False)
    
    def __init__(self, user_id, expires_in=86400):
        """创建新令牌
        
        Args:
            user_id: 用户ID
            expires_in: 令牌过期时间（秒），默认24小时
        """
        self.token = secrets.token_hex(32)
        self.user_id = user_id
        self.created_at = time.time()
        self.expires_at = self.created_at + expires_in
    
    def is_expired(self):
        """检查令牌是否已过期"""
        return time.time() > self.expires_at
    
    @classmethod
    def generate_for_user(cls, user_id):
        """为用户生成新令牌"""
        try:
            # 先清除该用户的所有旧令牌
            cls.query.filter_by(user_id=user_id).delete()
            db.session.commit()
            logger.info(f"已删除用户ID={user_id}的旧令牌")
            
            # 创建新令牌
            token = cls(user_id=user_id)
            db.session.add(token)
            db.session.commit()
            logger.info(f"已为用户ID={user_id}创建新令牌")
            
            return token.token
        except Exception as e:
            db.session.rollback()
            logger.error(f"为用户ID={user_id}生成令牌时出错: {str(e)}")
            # 打印详细错误信息
            import traceback
            error_trace = traceback.format_exc()
            logger.error(f"令牌生成错误详情: {error_trace}")
            
            # 尝试使用更简单的方法生成令牌
            try:
                logger.info("尝试使用备用方法生成令牌")
                # 不使用ORM，直接执行SQL
                token_str = secrets.token_hex(32)
                expires_at = time.time() + 86400  # 24小时后过期
                
                sql = """
                INSERT INTO tokens (token, user_id, created_at, expires_at)
                VALUES (:token, :user_id, :created_at, :expires_at)
                """
                
                db.session.execute(sql, {
                    "token": token_str,
                    "user_id": user_id,
                    "created_at": time.time(),
                    "expires_at": expires_at
                })
                
                db.session.commit()
                logger.info("备用方法成功生成令牌")
                return token_str
            except Exception as backup_err:
                db.session.rollback()
                logger.error(f"备用令牌生成也失败: {str(backup_err)}")
                raise

    @classmethod
    def get_user_id_by_token(cls, token_str):
        """通过令牌获取用户ID"""
        try:
            if not token_str:
                logger.warning("尝试验证空令牌")
                return None
            
            token = cls.query.filter_by(token=token_str).first()
            
            if not token:
                logger.warning(f"找不到令牌: {token_str[:10]}...")
                return None
                
            if token.is_expired():
                logger.info(f"令牌已过期: {token_str[:10]}...")
                # 删除过期令牌
                try:
                    db.session.delete(token)
                    db.session.commit()
                    logger.info("已删除过期令牌")
                except Exception as delete_error:
                    logger.error(f"删除过期令牌时出错: {str(delete_error)}")
                    db.session.rollback()
                return None
                
            logger.info(f"令牌有效，用户ID: {token.user_id}")
            return token.user_id
        except Exception as e:
            logger.error(f"验证令牌时出错: {str(e)}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return None
