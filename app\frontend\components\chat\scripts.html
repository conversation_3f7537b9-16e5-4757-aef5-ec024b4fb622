<!-- CDN引用与错误处理 -->
<script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
<script src="https://unpkg.com/axios/dist/axios.min.js"></script>
<script>
    // 确保Vue和axios正确加载，否则提供备用方案
    if (!window.Vue) {
        window.Vue = { createApp: function(options) { return { mount: function() { console.error('Vue未正确加载'); } }; } };
        console.error('Vue CDN加载失败，使用备用方案');
    }
    if (!window.axios) {
        window.axios = { 
            get: function() { return Promise.reject('Axios未正确加载'); },
            post: function() { return Promise.reject('Axios未正确加载'); }
        };
        console.error('Axios CDN加载失败，使用备用方案');
    }
</script>
<script src="./js/main.js?v=7"></script>
<script>
    // 聊天界面加载动画控制
    window.addEventListener('load', function() {
        const loadingScreen = document.getElementById('chatLoadingScreen');
        const chatApp = document.getElementById('chatApp');
        
        let progress = 0;
        const progressBar = document.querySelector('.progress-bar');
        const loadingText = document.querySelector('.loading-text');
        
        const loadingSteps = [
            '正在加载智能问诊系统...',
            '正在连接AI助手...',
            '正在准备对话界面...',
            '加载完成，开始问诊'
        ];
        
        const loadingInterval = setInterval(() => {
            progress += Math.random() * 30 + 15;
            
            if (progress >= 100) {
                progress = 100;
                clearInterval(loadingInterval);
                
                setTimeout(() => {
                    loadingScreen.style.opacity = '0';
                    setTimeout(() => {
                        loadingScreen.style.display = 'none';
                        chatApp.style.display = 'flex';
                        setTimeout(() => {
                            chatApp.style.opacity = '1';
                        }, 50);
                    }, 300);
                }, 400);
            }
            
            progressBar.style.width = progress + '%';
            const stepIndex = Math.min(Math.floor(progress / 25), loadingSteps.length - 1);
            loadingText.textContent = loadingSteps[stepIndex];
        }, 80);
    });

    // 检查用户是否已登录
    document.addEventListener('DOMContentLoaded', function() {
        const token = localStorage.getItem('auth_token');
        const username = localStorage.getItem('username');
        
        if (!token || !username) {
            // 如果未登录，重定向到首页
            window.location.href = '/';
            alert('请先登录再进行问诊');
            return;
        }

        // 返回首页按钮点击事件
        document.getElementById('backBtn').addEventListener('click', function() {
            window.location.href = '/';
        });



        // 自动调整文本框高度
        const messageInput = document.getElementById('messageInput');
        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
            
            // 启用/禁用发送按钮
            document.getElementById('sendBtn').disabled = this.value.trim() === '';
        });
        
        // 清除对话按钮由Vue组件处理
    });
</script> 