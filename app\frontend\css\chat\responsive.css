/* 聊天界面响应式设计 */

/* 平板设备和小屏幕笔记本 */
@media (max-width: 768px) {
    .chat-header {
        padding: 0.75rem 1rem;
    }
    
    .chat-header-title h1 {
        font-size: 1.25rem;
    }
    
    .chat-actions {
        gap: 0.5rem;
    }
    
    .btn {
        padding: 0.625rem 0.875rem;
        font-size: 0.8125rem;
    }
    
    .message {
        max-width: 85%;
    }
    
    .chat-messages {
        padding: 1rem;
        gap: 0.75rem;
    }
    
    .message-avatar {
        width: 2rem;
        height: 2rem;
    }
    
    .message-content {
        padding: 0.875rem 1rem;
        font-size: 0.9rem;
    }
    
    .chat-input-container {
        padding: 0.875rem 1rem;
    }
    
    .chat-input {
        font-size: 0.9rem;
        padding: 0.75rem 0.875rem;
    }
    
    .send-button {
        width: 40px;
        height: 40px;
    }
    
    .loading-indicator {
        margin-left: 2.75rem;
        padding: 0.75rem 0.875rem;
    }
}

/* 手机设备 */
@media (max-width: 480px) {
    .chat-header {
        padding: 0.625rem 0.75rem;
    }

    .chat-header-title {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .chat-header-title h1 {
        font-size: 1.125rem;
    }
    
    .chat-header-title p {
        font-size: 0.75rem;
    }
    
    .chat-actions {
        flex-direction: column;
        gap: 0.375rem;
    }
    
    .btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
    }

    .message {
        max-width: 90%;
    }
    
    .message-avatar {
        width: 1.75rem;
        height: 1.75rem;
    }
    
    .message-content {
        padding: 0.75rem 0.875rem;
        font-size: 0.875rem;
    }
    
    .loading-indicator {
        margin-left: 2.5rem;
        padding: 0.625rem 0.75rem;
    }
    
    .chat-input-container {
        padding: 0.75rem;
        gap: 0.5rem;
    }
    
    .send-button {
        width: 36px;
        height: 36px;
    }
} 