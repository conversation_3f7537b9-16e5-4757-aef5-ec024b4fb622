/* 响应式设计 */

/* 1024px及以下设备 */
@media (max-width: 1024px) {
    .dashboard {
        padding: 1rem;
        gap: 1rem;
    }
    
    .sidebar {
        width: 300px;
    }
    
    .main-content {
        padding: 1.5rem;
    }
    
    .content-card,
    .ai-diagnosis,
    .prescription {
        padding: 1.5rem;
    }
}

/* 768px及以下设备 */
@media (max-width: 768px) {
    .dashboard {
        flex-direction: column;
        padding: 0.5rem;
        gap: 0.5rem;
    }
    
    .sidebar {
        width: 100%;
        border-radius: var(--radius-xl);
    }
    
    .content {
        border-radius: var(--radius-xl);
    }
    
    .main-content {
        padding: 1rem;
    }
    
    .top-nav {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .nav-actions {
        justify-content: center;
    }
    
    .current-patient h2 {
        font-size: 1.25rem;
    }
    
    .sidebar-header {
        padding: 1.5rem;
    }
    
    .doctor-avatar {
        width: 56px;
        height: 56px;
    }
}

/* 480px及以下设备 */
@media (max-width: 480px) {
    .dashboard {
        padding: 0.25rem;
    }
    
    .content-card,
    .ai-diagnosis,
    .prescription {
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .btn {
        padding: 0.625rem 1.25rem;
        font-size: 0.8125rem;
    }
    
    .form-input,
    .form-textarea,
    .form-select {
        padding: 0.625rem 0.875rem;
    }
    
    .patient-item {
        padding: 0.75rem;
    }
    
    .patient-avatar {
        width: 36px;
        height: 36px;
        font-size: 0.8125rem;
    }
}