/* 聊天头部样式 */

.chat-header {
    background: var(--header-bg);
    color: var(--white);
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow-md);
    position: relative;
    z-index: 10;
}

.chat-header-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    z-index: 2;
}

.chat-header-title h1 {
    color: var(--white);
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
    letter-spacing: -0.025em;
}

.chat-header-title p {
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    font-size: 0.875rem;
    font-weight: 500;
}

.chat-actions {
    display: flex;
    gap: 0.75rem;
    z-index: 2;
}

.btn {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1rem;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--duration-normal) ease;
    border: none;
    white-space: nowrap;
}

.clear-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.15);
    color: var(--white);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.clear-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.back-btn {
    background: var(--white);
    color: var(--primary-color);
    font-weight: 600;
}

.back-btn:hover {
    background: var(--gray-100);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
} 