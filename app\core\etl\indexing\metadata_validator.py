"""元数据验证器

验证和标准化文档元数据，确保符合ETL-005的要求
"""

from typing import Dict, Any, List, Optional, Set
from datetime import datetime
import pandas as pd


class MetadataValidator:
    """元数据验证器
    
    确保每个Chunk的元数据包含必需字段并符合格式要求
    """
    
    def __init__(self):
        """初始化元数据验证器"""
        # ETL-005要求的必需字段
        self.required_fields = {
            'chunk_id',
            'document_id', 
            'source_type',
            'source_location'
        }
        
        # 可选但重要的字段
        self.optional_fields = {
            'page_number',
            'clean_status',
            'chunk_index',
            'total_chunks',
            'chunk_length',
            'splitter_type',
            'source_name',
            'file_name',
            'file_extension',
            'category',
            'extracted_at',
            'split_timestamp'
        }
        
        # 字段类型映射
        self.field_types = {
            'chunk_id': str,
            'document_id': str,
            'source_type': str,
            'source_location': str,
            'page_number': int,
            'chunk_index': int,
            'total_chunks': int,
            'chunk_length': int,
            'clean_status': str,
            'splitter_type': str,
            'source_name': str,
            'file_name': str,
            'file_extension': str,
            'category': str
        }
    
    def validate_metadata(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """验证单个元数据记录
        
        Args:
            metadata: 元数据字典
            
        Returns:
            验证和标准化后的元数据
            
        Raises:
            ValueError: 当缺少必需字段时
        """
        if not isinstance(metadata, dict):
            raise ValueError("元数据必须是字典类型")
        
        # 检查必需字段
        missing_fields = self.required_fields - set(metadata.keys())
        if missing_fields:
            raise ValueError(f"缺少必需的元数据字段: {missing_fields}")
        
        # 创建标准化的元数据副本
        validated_metadata = metadata.copy()
        
        # 类型转换和验证
        for field, expected_type in self.field_types.items():
            if field in validated_metadata:
                value = validated_metadata[field]
                
                # 处理None值
                if value is None:
                    if field in self.required_fields:
                        raise ValueError(f"必需字段 {field} 不能为None")
                    continue
                
                # 类型转换
                try:
                    if expected_type == int:
                        validated_metadata[field] = int(value)
                    elif expected_type == str:
                        validated_metadata[field] = str(value)
                except (ValueError, TypeError) as e:
                    raise ValueError(f"字段 {field} 类型转换失败: {e}")
        
        # 添加验证时间戳
        validated_metadata['metadata_validated_at'] = datetime.now().isoformat()
        
        return validated_metadata
    
    def validate_dataframe_metadata(self, df: pd.DataFrame, 
                                  metadata_column: str = 'metadata') -> pd.DataFrame:
        """验证DataFrame中的元数据
        
        Args:
            df: 输入DataFrame
            metadata_column: 元数据列名
            
        Returns:
            验证后的DataFrame
        """
        if df.empty:
            return df
        
        if metadata_column not in df.columns:
            raise ValueError(f"DataFrame中未找到元数据列: {metadata_column}")
        
        validated_df = df.copy()
        validation_errors = []
        
        for index, row in df.iterrows():
            try:
                metadata = row[metadata_column]
                validated_metadata = self.validate_metadata(metadata)
                validated_df.at[index, metadata_column] = validated_metadata
                
            except Exception as e:
                validation_errors.append(f"行 {index}: {e}")
        
        if validation_errors:
            print(f"⚠️  元数据验证发现 {len(validation_errors)} 个错误:")
            for error in validation_errors[:5]:  # 只显示前5个错误
                print(f"  - {error}")
            if len(validation_errors) > 5:
                print(f"  ... 还有 {len(validation_errors) - 5} 个错误")
        
        return validated_df
    
    def enrich_metadata(self, metadata: Dict[str, Any], 
                       additional_fields: Dict[str, Any] = None) -> Dict[str, Any]:
        """丰富元数据信息
        
        Args:
            metadata: 原始元数据
            additional_fields: 额外字段
            
        Returns:
            丰富后的元数据
        """
        enriched_metadata = metadata.copy()
        
        # 添加额外字段
        if additional_fields:
            enriched_metadata.update(additional_fields)
        
        # 自动生成缺失的字段
        if 'chunk_id' not in enriched_metadata and 'document_id' in enriched_metadata:
            chunk_index = enriched_metadata.get('chunk_index', 0)
            enriched_metadata['chunk_id'] = f"{enriched_metadata['document_id']}_chunk_{chunk_index}"
        
        # 推断source_location（如果缺失）
        if 'source_location' not in enriched_metadata:
            if 'file_path' in enriched_metadata:
                enriched_metadata['source_location'] = enriched_metadata['file_path']
            elif 'source_name' in enriched_metadata:
                enriched_metadata['source_location'] = enriched_metadata['source_name']
            else:
                enriched_metadata['source_location'] = 'unknown'
        
        # 设置默认的clean_status
        if 'clean_status' not in enriched_metadata:
            enriched_metadata['clean_status'] = 'processed'
        
        return enriched_metadata
    
    def get_metadata_summary(self, df: pd.DataFrame, 
                           metadata_column: str = 'metadata') -> Dict[str, Any]:
        """获取元数据摘要统计
        
        Args:
            df: 输入DataFrame
            metadata_column: 元数据列名
            
        Returns:
            元数据摘要信息
        """
        if df.empty or metadata_column not in df.columns:
            return {}
        
        summary = {
            'total_records': len(df),
            'field_coverage': {},
            'source_types': {},
            'clean_status_distribution': {},
            'splitter_types': {}
        }
        
        all_fields = set()
        
        for _, row in df.iterrows():
            metadata = row[metadata_column]
            if isinstance(metadata, dict):
                all_fields.update(metadata.keys())
                
                # 统计source_type分布
                source_type = metadata.get('source_type', 'unknown')
                summary['source_types'][source_type] = summary['source_types'].get(source_type, 0) + 1
                
                # 统计clean_status分布
                clean_status = metadata.get('clean_status', 'unknown')
                summary['clean_status_distribution'][clean_status] = \
                    summary['clean_status_distribution'].get(clean_status, 0) + 1
                
                # 统计splitter_type分布
                splitter_type = metadata.get('splitter_type', 'unknown')
                summary['splitter_types'][splitter_type] = \
                    summary['splitter_types'].get(splitter_type, 0) + 1
        
        # 计算字段覆盖率
        for field in all_fields:
            count = sum(1 for _, row in df.iterrows() 
                       if isinstance(row[metadata_column], dict) and 
                       field in row[metadata_column] and 
                       row[metadata_column][field] is not None)
            summary['field_coverage'][field] = {
                'count': count,
                'percentage': (count / len(df)) * 100
            }
        
        return summary
    
    def check_required_fields_coverage(self, df: pd.DataFrame, 
                                     metadata_column: str = 'metadata') -> Dict[str, Any]:
        """检查必需字段的覆盖情况
        
        Args:
            df: 输入DataFrame
            metadata_column: 元数据列名
            
        Returns:
            必需字段覆盖情况报告
        """
        if df.empty:
            return {'status': 'empty_dataframe'}
        
        missing_fields_per_record = []
        
        for index, row in df.iterrows():
            metadata = row[metadata_column]
            if isinstance(metadata, dict):
                missing = self.required_fields - set(metadata.keys())
                if missing:
                    missing_fields_per_record.append({
                        'index': index,
                        'missing_fields': list(missing)
                    })
        
        return {
            'total_records': len(df),
            'records_with_missing_fields': len(missing_fields_per_record),
            'compliance_rate': ((len(df) - len(missing_fields_per_record)) / len(df)) * 100,
            'missing_fields_details': missing_fields_per_record[:10]  # 只返回前10个
        }
