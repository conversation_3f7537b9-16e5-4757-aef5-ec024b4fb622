/* 现代化侧边栏样式 */
.sidebar {
    width: 360px;
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-2xl);
    display: flex;
    flex-direction: column;
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    transition: all var(--duration-normal) var(--ease-out);
    position: relative;
}

.sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
}

.sidebar:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-2xl);
}

.sidebar-header {
    padding: 2rem;
    background: var(--primary-gradient);
    color: var(--white);
    position: relative;
    overflow: hidden;
}

.sidebar-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) rotate(45deg); }
    100% { transform: translateX(100%) rotate(45deg); }
}

.sidebar-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
    position: relative;
    z-index: 2;
    letter-spacing: -0.025em;
}

.doctor-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-top: 1.5rem;
    padding: 1.5rem;
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    transition: all var(--duration-normal) var(--ease-out);
}

.doctor-info:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.doctor-avatar {
    width: 64px;
    height: 64px;
    border-radius: var(--radius-full);
    object-fit: cover;
    border: 3px solid var(--primary-100);
    box-shadow: var(--shadow-md);
    transition: all var(--duration-normal) var(--ease-out);
}

.doctor-avatar:hover {
    transform: scale(1.05);
    border-color: var(--primary-200);
    box-shadow: var(--shadow-lg);
}

.doctor-details h3 {
    color: var(--gray-900);
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
    letter-spacing: -0.025em;
}

.doctor-details p {
    color: var(--gray-500);
    font-size: 0.875rem;
    font-weight: 500;
}

.current-patient {
    margin: 1.5rem;
    padding: 1.5rem;
    background: var(--white);
    border-radius: var(--radius-xl);
    border: 1px solid var(--gray-200);
    box-shadow: var(--shadow-sm);
    transition: all var(--duration-normal) var(--ease-out);
}

.current-patient:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.current-patient-header h4 {
    color: var(--gray-700);
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.75rem;
}

.current-patient-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.current-patient-info::before {
    content: '';
    width: 8px;
    height: 8px;
    background: var(--success);
    border-radius: var(--radius-full);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

#currentPatientName {
    color: var(--gray-900);
    font-weight: 600;
    font-size: 1rem;
}

.patient-list {
    flex: 1;
    overflow-y: auto;
    padding: 0 1.5rem 1.5rem;
}

.patient-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    margin-bottom: 0.75rem;
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--duration-normal) var(--ease-out);
    position: relative;
    overflow: hidden;
}

.patient-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    background: var(--primary-gradient);
    transform: scaleY(0);
    transition: transform var(--duration-normal) var(--ease-out);
}

.patient-item:hover {
    background: var(--gray-25);
    border-color: var(--primary-200);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.patient-item:hover::before {
    transform: scaleY(1);
}

.patient-item.active {
    background: var(--primary-50);
    border-color: var(--primary-300);
    box-shadow: var(--shadow-md);
}

.patient-item.active::before {
    transform: scaleY(1);
}

.patient-info {
    flex: 1;
}

.patient-name {
    font-weight: 600;
    color: var(--gray-900);
    font-size: 0.95rem;
    margin-bottom: 0.25rem;
    letter-spacing: -0.025em;
}

.patient-status {
    font-size: 0.8125rem;
    font-weight: 500;
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-full);
    display: inline-block;
}

.patient-status.urgent {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger);
}

.patient-status.normal {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.patient-status.waiting {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning);
}

.patient-time {
    font-size: 0.8125rem;
    color: var(--gray-500);
    font-weight: 500;
}

/* 患者最后消息 */
.patient-last-message {
    font-size: 0.8125rem;
    color: var(--gray-600);
    margin-bottom: 0.5rem;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 患者元信息 */
.patient-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 0.5rem;
}

.message-count {
    font-size: 0.75rem;
    color: var(--gray-500);
    background: var(--gray-100);
    padding: 0.125rem 0.5rem;
    border-radius: var(--radius-full);
}

/* 高优先级样式 */
.patient-status.high-priority {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

/* 医生已参与样式 */
.patient-status.doctor-involved {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

/* 选中状态 */
.patient-item.selected {
    background: var(--primary-50);
    border-color: var(--primary-300);
    box-shadow: var(--shadow-md);
}

.patient-item.selected::before {
    transform: scaleY(1);
}

.empty-state-message {
    text-align: center;
    color: var(--gray-500);
    font-size: 0.875rem;
    padding: 2rem 1rem;
    font-weight: 500;
}
