// 聊天功能Vue应用
const { createApp } = Vue;

const chatApp = createApp({
    data() {
        return {
            messages: [],
            isTyping: false,
            currentUser: localStorage.getItem('username') || '用户',
            userInput: '',
            sessionId: null,
            historySessions: [],
            lastActivityTime: null,
            sessionTitle: '',
            showSidebar: true,
            loadingHistory: false,
            loadingChat: true,
            messageTemplate: ''
        }
    },
    
    created() {
        // 加载消息模板
        fetch('./components/chat/message-template.html')
            .then(response => response.text())
            .then(html => {
                this.messageTemplate = html;
                // 初始化聊天应用
                this.initializeChat();
            })
            .catch(error => {
                console.error('加载消息模板失败:', error);
                // 即使失败也继续初始化应用
                this.initializeChat();
            });
    },
    
    mounted() {
        this.scrollToBottom();
        this.checkForNewMessages();
        window.addEventListener('resize', this.adjustHeight);
    },
    
    unmounted() {
        window.removeEventListener('resize', this.adjustHeight);
    },
    
    methods: {
        initializeChat() {
            // 从URL参数获取会话ID，如果有的话
            const urlParams = new URLSearchParams(window.location.search);
            const sessionIdParam = urlParams.get('session_id');
            
            if (sessionIdParam) {
                console.log('从URL加载会话:', sessionIdParam);
                this.sessionId = sessionIdParam;
                this.loadChatHistory(sessionIdParam);
            } else {
                // 创建欢迎消息
                this.messages = [
                    {
                        id: 1,
                        role: 'bot',
                        content: '您好！我是慧问医答智能助手。请描述您的症状，我将为您提供专业的健康建议。',
                        timestamp: Date.now() / 1000,
                        avatar: './assets/bot-avatar.svg'
                    },
                    {
                        id: 2,
                        role: 'bot',
                        content: '您可以：\n• 直接在输入框中描述您的症状或健康问题\n• 详细说明症状的持续时间和严重程度\n• 提及任何相关的病史或用药情况',
                        timestamp: Date.now() / 1000,
                        avatar: './assets/bot-avatar.svg'
                    }
                ];
                this.loadingChat = false;
            }
            
            // 延迟加载历史会话，确保DOM已完全加载
            this.$nextTick(() => {
                setTimeout(() => {
                    this.showHistorySessions();
                }, 100);
            });
        },
        
        sendMessage(content) {
            if (!content.trim()) return;

            // 清除输入框
            this.userInput = '';

            // 添加用户消息到本地显示
            const userMsg = {
                id: Date.now(),
                role: 'user',
                content: content.trim(),
                timestamp: Date.now() / 1000,
                avatar: './assets/user-avatar.svg'
            };
            this.messages.push(userMsg);

            // 如果是首条消息，设置为会话标题
            if (!this.sessionTitle && (!this.sessionId || this.messages.length <= 3)) {
                this.sessionTitle = content.trim().substring(0, 50);
                this.lastActivityTime = Date.now() / 1000;
            }

            // 显示加载状态
            this.isTyping = true;
            this.scrollToBottom();

            // 先进行意图识别
            this.recognizeIntentAndSend(content.trim());
        },

        async recognizeIntentAndSend(message) {
            try {
                console.log('🔍 开始意图识别...');
                console.log('用户输入:', message);

                // 设置较短的超时时间，避免影响用户体验
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时

                // 调用意图识别API
                const response = await fetch('/api/intent/recognize', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message
                    }),
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                if (response.ok) {
                    const intentResult = await response.json();

                    // 在控制台显示意图识别结果
                    console.log('🎯 意图识别结果:');
                    console.log('  意图类型:', intentResult.intent);
                    console.log('  置信度:', intentResult.confidence);
                    console.log('  推理过程:', intentResult.reasoning);
                    console.log('  意图描述:', intentResult.description);

                    // 根据意图类型在控制台显示不同的信息
                    switch(intentResult.intent) {
                        case 'knowledge_query':
                            console.log('📚 检测到知识问答意图 - 将路由到知识库查询');
                            break;
                        case 'tool_use':
                            console.log('🔧 检测到工具调用意图 - 将路由到工具处理链');
                            break;
                        case 'general_chat':
                            console.log('💬 检测到一般聊天意图 - 将路由到对话处理链');
                            break;
                        default:
                            console.log('❓ 未知意图类型');
                    }

                } else {
                    console.warn('⚠️ 意图识别API调用失败:', response.status, '- 继续正常聊天流程');
                }

            } catch (error) {
                if (error.name === 'AbortError') {
                    console.warn('⚠️ 意图识别超时 - 继续正常聊天流程');
                } else {
                    console.warn('⚠️ 意图识别过程出错:', error.message, '- 继续正常聊天流程');
                }
            }

            // 无论意图识别是否成功，都继续发送消息到原有的处理流程
            this.sendMessageStream(message);
        },

        sendMessageStream(message) {
            // 创建机器人消息占位符
            const botMsg = {
                id: Date.now() + 1,
                role: 'bot',
                content: '',
                timestamp: Date.now() / 1000,
                avatar: './assets/bot-avatar.svg',
                isStreaming: true
            };
            this.messages.push(botMsg);
            this.scrollToBottom();

            // 创建请求数据
            const requestData = {
                message: message,
                session_id: this.sessionId,
                username: this.currentUser
            };

            // 获取身份验证token
            const token = localStorage.getItem('auth_token');
            const headers = {
                'Content-Type': 'application/json',
            };

            // 如果有token，添加到请求头
            if (token) {
                headers['Authorization'] = `Bearer ${token}`;
            }

            // 使用fetch发送POST请求到流式端点
            fetch('/api/chat/stream', {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(requestData)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();

                const readStream = () => {
                    reader.read().then(({ done, value }) => {
                        if (done) {
                            // 流结束
                            this.isTyping = false;
                            botMsg.isStreaming = false;
                            this.updateSessionInfo();
                            return;
                        }

                        // 解析SSE数据
                        const chunk = decoder.decode(value);
                        const lines = chunk.split('\n');

                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                try {
                                    const data = JSON.parse(line.slice(6));

                                    if (data.type === 'start') {
                                        this.sessionId = data.session_id;
                                        this.isTyping = false;
                                    } else if (data.type === 'chunk') {
                                        botMsg.content += data.content;
                                        this.$forceUpdate();
                                        this.$nextTick(() => {
                                            this.scrollToBottom();
                                        });
                                    } else if (data.type === 'end') {
                                        botMsg.content = data.full_response;
                                        botMsg.isStreaming = false;
                                        this.isTyping = false;
                                        this.updateSessionInfo();
                                    } else if (data.type === 'error') {
                                        botMsg.content = data.content;
                                        botMsg.isStreaming = false;
                                        this.isTyping = false;
                                    }
                                } catch (e) {
                                    console.error('解析SSE数据出错:', e);
                                }
                            }
                        }

                        readStream(); // 继续读取
                    });
                };

                readStream();
            })
            .catch(error => {
                console.error('流式请求出错:', error);
                this.isTyping = false;
                botMsg.content = '抱歉，发送消息时出现了网络错误。请检查您的网络连接并重试。';
                botMsg.isStreaming = false;
                this.scrollToBottom();
            });
        },

        updateSessionInfo() {
            // 更新URL，不刷新页面
            if (history.pushState && this.sessionId) {
                const newurl = window.location.protocol + "//" + window.location.host +
                              window.location.pathname + '?session_id=' + this.sessionId;
                window.history.pushState({ path: newurl }, '', newurl);
            }

            // 更新侧边栏历史会话
            this.showHistorySessions();
        },

        loadChatHistory(sessionId) {
            console.log('📚 开始加载聊天历史:', sessionId);
            this.loadingChat = true;
            this.messages = [];
            this.sessionId = sessionId;

            // 获取身份验证token
            const token = localStorage.getItem('auth_token');
            const headers = {};

            // 如果有token，添加到请求头
            if (token) {
                headers['Authorization'] = `Bearer ${token}`;
            }

            axios.get(`/api/chat/history?session_id=${sessionId}`, { headers })
                .then(response => {
                    console.log('📡 历史记录API响应:', response.data);
                    if (response.data.status === 'success') {
                        console.log('✅ 成功获取历史记录，消息数量:', response.data.history.length);

                        this.messages = response.data.history.map((msg, index) => ({
                            ...msg,
                            id: index + 1, // 添加唯一ID
                            type: msg.role, // 添加type字段，与模板保持一致
                            time: new Date(msg.timestamp * 1000).toLocaleString(), // 格式化时间，精确到秒
                            avatar: msg.role === 'user'
                                ? './assets/user-avatar.svg'
                                : msg.role === 'doctor'
                                    ? './assets/doctor-avatar.svg'
                                    : './assets/bot-avatar.svg'
                        }));

                        console.log('🔄 更新后的messages:', this.messages);
                        console.log('🔍 第一条消息详情:', this.messages[0]);

                        // 设置会话标题（使用第一条用户消息）
                        const firstUserMsg = this.messages.find(m => m.role === 'user');
                        if (firstUserMsg) {
                            this.sessionTitle = firstUserMsg.content.substring(0, 50);
                        }

                        // 强制Vue更新
                        this.$forceUpdate();

                        // 验证Vue实例状态
                        console.log('🎯 Vue实例messages长度:', this.messages.length);
                        console.log('🎯 Vue实例loadingChat状态:', this.loadingChat);

                        // 临时添加一个测试消息来验证显示
                        this.messages.push({
                            id: 999,
                            type: 'bot',
                            role: 'bot',
                            content: '🧪 这是一条测试消息，用于验证Vue渲染',
                            time: new Date().toLocaleString(),
                            avatar: './assets/bot-avatar.svg'
                        });
                        console.log('🧪 添加测试消息后的messages长度:', this.messages.length);

                        // 重要：设置加载完成状态
                        this.loadingChat = false;
                        console.log('✅ 设置loadingChat为false');

                        this.$nextTick(() => {
                            console.log('📜 滚动到底部');

                            // 检查DOM是否更新
                            const chatMessages = document.querySelector('.chat-messages');
                            console.log('🔍 chat-messages元素:', chatMessages);
                            if (chatMessages) {
                                console.log('🔍 chat-messages子元素数量:', chatMessages.children.length);
                                console.log('🔍 chat-messages innerHTML:', chatMessages.innerHTML.substring(0, 200));
                            }

                            this.scrollToBottom();
                        });
                    } else {
                        console.error('加载历史记录失败:', response.data.error);
                        this.messages = [{
                            id: 1,
                            role: 'bot',
                            content: '抱歉，无法加载聊天历史。请重试或开始新的对话。',
                            timestamp: Date.now() / 1000,
                            avatar: './assets/bot-avatar.svg'
                        }];
                    }
                    this.loadingChat = false;
                })
                .catch(error => {
                    console.error('获取聊天历史出错:', error);
                    this.messages = [{
                        id: 1,
                        role: 'bot',
                        content: '抱歉，获取聊天历史时出现网络错误。请检查您的网络连接并重试。',
                        timestamp: Date.now() / 1000,
                        avatar: './assets/bot-avatar.svg'
                    }];
                    this.loadingChat = false;
                });
        },

        showHistorySessions() {
            console.log('🔄 开始加载历史会话...');

            // 获取身份验证token
            const token = localStorage.getItem('auth_token');
            const headers = {};

            // 如果有token，添加到请求头
            if (token) {
                headers['Authorization'] = `Bearer ${token}`;
            }

            axios.get('/api/chat/active_sessions', { headers })
                .then(response => {
                    console.log('📡 历史会话API响应:', response.data);
                    if (response.data.status === 'success') {
                        this.historySessions = response.data.sessions || [];
                        console.log('✅ 成功获取历史会话，数量:', this.historySessions.length);
                        this.renderSidebarSessions();
                    } else {
                        console.error('❌ 获取历史会话失败:', response.data.error);
                        this.historySessions = [];
                        this.renderSidebarSessions();
                    }
                })
                .catch(error => {
                    console.error('❌ 获取历史会话出错:', error);
                    this.historySessions = [];
                    this.renderSidebarSessions();
                });
        },

        renderSidebarSessions(retryCount = 0) {
            console.log('🎨 开始渲染侧边栏会话...');
            const sessionsList = document.getElementById('sessionsList');
            console.log('📋 sessionsList元素:', sessionsList);

            if (!sessionsList) {
                console.error('❌ 找不到sessionsList元素');
                if (retryCount < 3) {
                    console.log(`🔄 重试第${retryCount + 1}次...`);
                    setTimeout(() => {
                        this.renderSidebarSessions(retryCount + 1);
                    }, 200);
                }
                return;
            }

            console.log('📊 历史会话数量:', this.historySessions.length);
            if (this.historySessions.length === 0) {
                sessionsList.innerHTML = '<div class="history-placeholder">暂无历史会话</div>';
                console.log('📝 显示暂无历史会话');
                return;
            }
            
            // 清空现有内容
            sessionsList.innerHTML = '<div class="history-section-title">历史会话</div>';
            
            // 排序：最近活动的在前面
            const sortedSessions = [...this.historySessions].sort((a, b) => b.last_activity - a.last_activity);
            
            // 渲染每个会话项
            sortedSessions.forEach(session => {
                const sessionItem = document.createElement('div');
                sessionItem.className = 'history-item' + 
                                       (session.session_id === this.sessionId ? ' active' : '') + 
                                       (session.priority === 'high' ? ' high-priority' : '');
                
                // 格式化日期
                const dateStr = this.formatDate(session.last_activity);
                
                // 准备会话内容HTML，包括医生标签和优先级
                // 使用last_message作为标题，如果没有则使用默认标题
                const displayTitle = session.title || session.last_message || '新会话';
                let sessionHTML = `
                    <div class="history-title">
                        ${displayTitle}
                        ${session.doctor_involved ? '<span class="doctor-tag">医生已参与</span>' : ''}
                    </div>
                    <div class="history-meta">
                        <span class="history-time">${dateStr}</span>
                        <span class="session-id-preview">#${session.session_id.substring(0, 6)}</span>
                    </div>
                `;
                
                sessionItem.innerHTML = sessionHTML;
                sessionItem.addEventListener('click', () => {
                    console.log('🖱️ 点击历史会话:', session.session_id);
                    // 调用全局函数来加载历史会话
                    if (window.loadHistorySession) {
                        window.loadHistorySession(session.session_id);
                    } else {
                        console.error('❌ 全局loadHistorySession函数不存在');
                    }
                });
                
                sessionsList.appendChild(sessionItem);
            });
        },
        
        clearChat() {
            if (!confirm('确定要清除当前对话吗？这将从会话中删除所有消息。')) {
                return;
            }
            
            if (this.sessionId) {
                axios.delete(`/api/chat/session?session_id=${this.sessionId}`)
                    .then(response => {
                        if (response.data.status === 'success') {
                            // 重置UI
                            this.sessionId = null;
                            this.sessionTitle = '';
                            this.messages = [{
                                id: 1,
                                role: 'bot',
                                content: '对话已清除。请问有什么可以帮助您的吗？',
                                timestamp: Date.now() / 1000,
                                avatar: './assets/bot-avatar.svg'
                            }];
                            
                            // 更新URL
                            if (history.pushState) {
                                const newurl = window.location.protocol + "//" + window.location.host + window.location.pathname;
                                window.history.pushState({ path: newurl }, '', newurl);
                            }
                            
                            // 更新侧边栏
                            this.showHistorySessions();
                        } else {
                            alert('清除对话失败: ' + response.data.error);
                        }
                    })
                    .catch(error => {
                        console.error('清除对话出错:', error);
                        alert('清除对话时出现网络错误。请检查您的网络连接并重试。');
                    });
            } else {
                // 本地会话，直接清除
                this.messages = [{
                    id: 1,
                    role: 'bot',
                    content: '对话已清除。请问有什么可以帮助您的吗？',
                    timestamp: Date.now() / 1000,
                    avatar: './assets/bot-avatar.svg'
                }];
            }
        },
        
        createNewChat() {
            // 重置会话状态
            this.sessionId = null;
            this.sessionTitle = '';
            this.messages = [
                {
                    id: 1,
                    role: 'bot',
                    content: '您好！我是慧问医答智能助手。请描述您的症状，我将为您提供专业的健康建议。',
                    timestamp: Date.now() / 1000,
                    avatar: './assets/bot-avatar.svg'
                },
                {
                    id: 2,
                    role: 'bot',
                    content: '您可以：\n• 点击下方快速回复按钮选择常见症状\n• 直接在输入框中描述您的情况\n• 上传相关图片帮助诊断',
                    timestamp: Date.now() / 1000,
                    avatar: './assets/bot-avatar.svg'
                }
            ];
            
            // 更新URL
            if (history.pushState) {
                const newurl = window.location.protocol + "//" + window.location.host + window.location.pathname;
                window.history.pushState({ path: newurl }, '', newurl);
            }
            
            // 关闭侧边栏（在移动视图上）
            this.toggleSidebar(false);
            
            // 更新侧边栏
            this.showHistorySessions();
        },
        
        scrollToBottom() {
            setTimeout(() => {
                const container = document.querySelector('.chat-messages');
                if (container) {
                    container.scrollTop = container.scrollHeight;
                }
            }, 100);
        },
        
        adjustHeight() {
            const messageInput = document.getElementById('messageInput');
            if (messageInput) {
                messageInput.style.height = 'auto';
                messageInput.style.height = (messageInput.scrollHeight) + 'px';
            }
        },
        
        toggleSidebar(forcedState) {
            const newState = forcedState !== undefined ? forcedState : !this.showSidebar;
            this.showSidebar = newState;
            
            // 切换侧边栏显示状态
            document.querySelector('.chat-layout').classList.toggle('sidebar-hidden', !newState);
        },
        
        checkForNewMessages() {
            // 每30秒检查一次新消息
            setInterval(() => {
                if (this.sessionId) {
                    axios.get(`/api/chat/history?session_id=${this.sessionId}&check_update=true`)
                        .then(response => {
                            if (response.data.status === 'success' && 
                                response.data.history.length > this.messages.length) {
                                // 有新消息，重新加载
                                this.loadChatHistory(this.sessionId);
                            }
                            // 同时刷新侧边栏会话列表
                            this.showHistorySessions();
                        })
                        .catch(error => {
                            console.error('检查新消息出错:', error);
                        });
                }
            }, 30000); // 30秒
        },
        
        formatDate(timestamp) {
            if (!timestamp) return '未知时间';
            
            const date = new Date(timestamp * 1000);
            const now = new Date();
            const yesterday = new Date(now);
            yesterday.setDate(now.getDate() - 1);
            
            // 同一天显示时间
            if (date.toDateString() === now.toDateString()) {
                return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            }
            // 昨天显示"昨天"
            else if (date.toDateString() === yesterday.toDateString()) {
                return '昨天';
            }
            // 一周内显示星期几
            else if (now.getTime() - date.getTime() < 7 * 24 * 60 * 60 * 1000) {
                const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
                return weekdays[date.getDay()];
            }
            // 其他显示日期
            else {
                return date.toLocaleDateString();
            }
        },
        
        formatMessageTime(timestamp) {
            if (!timestamp) return '';
            
            const date = new Date(timestamp * 1000);
            return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        }
    },
    
    template: `
        <div class="chat-messages">
            <div v-for="message in messages" :key="message.id" :class="message.role + '-message'">
                <div class="message-avatar">
                    <img :src="message.avatar" :alt="message.role" />
                </div>
                <div class="message-content">
                    <div class="message-text">{{ message.content }}</div>
                    <div class="message-meta">
                        <span v-if="message.role === 'doctor'" class="doctor-name">{{ message.doctor_name || '医生' }}</span>
                        <span class="message-time">{{ formatMessageTime(message.timestamp) }}</span>
                    </div>
                </div>
            </div>
            
            <div v-if="isTyping" class="bot-message">
                <div class="message-avatar">
                    <img src="./assets/bot-avatar.svg" alt="AI助手" />
                </div>
                <div class="message-content">
                    <div class="loading-indicator">
                        <div class="dot"></div>
                        <div class="dot"></div>
                        <div class="dot"></div>
                    </div>
                </div>
            </div>
        </div>
    `
});

// 等待页面加载完成后挂载应用 - 禁用以避免与chat.html中的Vue实例冲突
// document.addEventListener('DOMContentLoaded', () => {
//     window.chatAppInstance = chatApp.mount('#app');
//     console.log('聊天应用已挂载');
// });

// 导出应用实例，供外部访问
window.chatApp = chatApp; 