"""CSV和Excel数据抽取器"""

import os
from typing import Dict, Any, List
import pandas as pd
import numpy as np

from .base_extractor import BaseExtractor


class CsvExcelExtractor(BaseExtractor):
    """CSV和Excel数据抽取器
    
    支持的文件格式：
    - CSV (.csv)
    - Excel (.xlsx, .xls)
    """
    
    def extract(self) -> pd.DataFrame:
        """从CSV/Excel文件抽取数据"""
        self.validate_config(['path'])
        
        file_path = self.config['path']
        
        if not os.path.exists(file_path):
            raise ValueError(f"文件不存在: {file_path}")
        
        # 根据文件扩展名选择读取方法
        file_ext = os.path.splitext(file_path)[1].lower()
        
        if file_ext == '.csv':
            df = self._read_csv_file(file_path)
        elif file_ext in ['.xlsx', '.xls']:
            df = self._read_excel_file(file_path)
        else:
            raise ValueError(f"不支持的文件格式: {file_ext}")
        
        if df is None or df.empty:
            print(f"文件 {file_path} 为空或无法读取")
            return self._create_standardized_dataframe([])
        
        # 创建包含原始列结构的DataFrame，以支持行转文本功能
        result_df = df.copy()

        # 添加content列（序列化的行数据）
        content_data = []
        metadata_data = []

        for index, row in df.iterrows():
            # 将整行数据序列化为内容
            row_content = self._serialize_row(row, df.columns)
            content_data.append(row_content)

            # 创建行级元数据
            row_metadata = {
                'file_path': file_path,
                'file_name': os.path.basename(file_path),
                'row_index': index,
                'total_rows': len(df),
                'columns': list(df.columns),
                'original_data': row.to_dict()
            }
            metadata_data.append(row_metadata)

        # 添加标准列
        result_df['content'] = content_data
        result_df['metadata'] = metadata_data

        print(f"成功处理 {len(result_df)} 行数据")

        # 添加通用元数据到每行的metadata中
        for i in range(len(result_df)):
            if isinstance(result_df.at[i, 'metadata'], dict):
                result_df.at[i, 'metadata'].update({
                    'extracted_at': self.extracted_at.isoformat(),
                    'extractor_type': self.__class__.__name__,
                    **self.metadata
                })

        return result_df
    
    def _read_csv_file(self, file_path: str) -> pd.DataFrame:
        """读取CSV文件"""
        try:
            # 获取配置参数
            encoding = self.config.get('encoding', 'utf-8')
            delimiter = self.config.get('delimiter', ',')
            header = self.config.get('header', 0)
            
            # 如果编码失败，尝试其他编码
            try:
                df = pd.read_csv(file_path, encoding=encoding, delimiter=delimiter, header=header)
            except UnicodeDecodeError:
                print(f"使用 {encoding} 编码失败，尝试自动检测编码")
                # 尝试常见编码
                for enc in ['gbk', 'gb2312', 'utf-8-sig', 'latin1']:
                    try:
                        df = pd.read_csv(file_path, encoding=enc, delimiter=delimiter, header=header)
                        print(f"成功使用 {enc} 编码读取文件")
                        break
                    except:
                        continue
                else:
                    raise ValueError(f"无法找到合适的编码读取文件: {file_path}")
            
            return df
            
        except Exception as e:
            print(f"读取CSV文件 {file_path} 失败: {str(e)}")
            return None
    
    def _read_excel_file(self, file_path: str) -> pd.DataFrame:
        """读取Excel文件"""
        try:
            sheet_name = self.config.get('sheet_name', 0)  # 默认读取第一个sheet
            header = self.config.get('header', 0)
            
            df = pd.read_excel(file_path, sheet_name=sheet_name, header=header)
            return df
            
        except Exception as e:
            print(f"读取Excel文件 {file_path} 失败: {str(e)}")
            return None
    
    def _serialize_row(self, row: pd.Series, columns: List[str]) -> str:
        """将数据行序列化为文本内容"""
        content_parts = []
        
        for col in columns:
            value = row[col]
            
            # 处理空值
            if pd.isna(value):
                value = ""
            elif isinstance(value, (int, float)):
                if pd.isna(value):
                    value = ""
                else:
                    value = str(value)
            else:
                value = str(value)
            
            if value.strip():  # 只有非空值才添加
                content_parts.append(f"{col}: {value}")
        
        return " | ".join(content_parts) if content_parts else ""
    
    def get_column_summary(self, file_path: str) -> Dict[str, Any]:
        """获取文件列信息摘要（用于调试和配置）"""
        try:
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext == '.csv':
                df = self._read_csv_file(file_path)
            elif file_ext in ['.xlsx', '.xls']:
                df = self._read_excel_file(file_path)
            else:
                return {"error": f"不支持的文件格式: {file_ext}"}
            
            if df is None:
                return {"error": "无法读取文件"}
                
            return {
                "columns": list(df.columns),
                "total_rows": len(df),
                "data_types": df.dtypes.to_dict(),
                "sample_data": df.head(3).to_dict()
            }
            
        except Exception as e:
            return {"error": str(e)} 