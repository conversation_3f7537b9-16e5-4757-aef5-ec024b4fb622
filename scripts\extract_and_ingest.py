"""数据抽取和摄取脚本

支持从sources.yaml配置的多种数据源抽取数据，
进行标准化处理，并摄取到向量存储中。
"""

import argparse
import sys
import os

# 添加项目根目录到路径，以便导入模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.knowledge_service import (
    extract_and_ingest_all_sources,
    extract_and_ingest_by_source_type,
    extract_and_ingest_by_source_name,
    get_available_data_sources,
    test_data_source_connection,
    load_and_ingest  # 保留传统方式
)


def main():
    parser = argparse.ArgumentParser(
        description="数据抽取和摄取工具 - 支持多种数据源",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 抽取所有启用的数据源
  python scripts/extract_and_ingest.py --all
  
  # 按数据源类型抽取
  python scripts/extract_and_ingest.py --type filesystem
  python scripts/extract_and_ingest.py --type csv_excel
  
  # 按数据源名称抽取
  python scripts/extract_and_ingest.py --name "医学文档库"
  
  # 列出所有可用数据源
  python scripts/extract_and_ingest.py --list
  
  # 测试数据源连接
  python scripts/extract_and_ingest.py --test "医学文档库"
  
  # 传统方式（向后兼容）
  python scripts/extract_and_ingest.py --source ./data_sample
        """
    )
    
    # 创建互斥组，确保只能选择一种操作模式
    group = parser.add_mutually_exclusive_group(required=True)
    
    group.add_argument(
        "--all",
        action="store_true",
        help="抽取所有启用的数据源"
    )
    
    group.add_argument(
        "--type",
        type=str,
        choices=["filesystem", "csv_excel", "web_url", "confluence", "git"],
        help="按数据源类型抽取 (filesystem/csv_excel/web_url/confluence/git)"
    )
    
    group.add_argument(
        "--name",
        type=str,
        help="按数据源名称抽取"
    )
    
    group.add_argument(
        "--list",
        action="store_true",
        help="列出所有可用的数据源"
    )
    
    group.add_argument(
        "--test",
        type=str,
        help="测试指定数据源的连接"
    )
    
    # 传统方式支持（向后兼容）
    group.add_argument(
        "--source",
        type=str,
        help="传统方式：指定目录路径进行文档摄取"
    )
    
    args = parser.parse_args()
    
    try:
        if args.all:
            print("=== 抽取所有启用的数据源 ===")
            extract_and_ingest_all_sources()
            
        elif args.type:
            print(f"=== 按类型抽取数据源: {args.type} ===")
            extract_and_ingest_by_source_type(args.type)
            
        elif args.name:
            print(f"=== 按名称抽取数据源: {args.name} ===")
            extract_and_ingest_by_source_name(args.name)
            
        elif args.list:
            print("=== 可用数据源列表 ===")
            sources = get_available_data_sources()
            _print_available_sources(sources)
            
        elif args.test:
            print(f"=== 测试数据源连接: {args.test} ===")
            result = test_data_source_connection(args.test)
            _print_connection_test_result(result)
            
        elif args.source:
            print(f"=== 传统方式摄取目录: {args.source} ===")
            load_and_ingest(data_path=args.source)
            
    except Exception as e:
        print(f"执行失败: {str(e)}")
        sys.exit(1)


def _print_available_sources(sources):
    """打印可用数据源信息"""
    if not sources:
        print("没有配置任何数据源。")
        return
    
    total_sources = 0
    enabled_sources = 0
    
    for source_type, source_list in sources.items():
        print(f"\n📁 {source_type.upper()}:")
        
        if not source_list:
            print("  (无配置的数据源)")
            continue
            
        for source in source_list:
            status = "✅ 启用" if source['enabled'] else "❌ 禁用"
            category = source.get('category', 'unknown')
            print(f"  - {source['name']} ({category}) - {status}")
            
            total_sources += 1
            if source['enabled']:
                enabled_sources += 1
    
    print(f"\n📊 统计: 总计 {total_sources} 个数据源，{enabled_sources} 个已启用")


def _print_connection_test_result(result):
    """打印连接测试结果"""
    if result.get('success'):
        print(f"✅ 连接成功!")
        print(f"   数据源: {result.get('source_name', 'Unknown')}")
        print(f"   类型: {result.get('type', 'Unknown')}")
        
        if 'test_data_count' in result:
            print(f"   测试数据量: {result['test_data_count']} 条记录")
    else:
        print(f"❌ 连接失败!")
        if 'source_name' in result:
            print(f"   数据源: {result['source_name']}")
        if 'type' in result:
            print(f"   类型: {result['type']}")
        if 'error' in result:
            print(f"   错误: {result['error']}")


if __name__ == "__main__":
    main() 