# 数据抽取与标准化功能

## 功能概述

该功能为MedicalBot平台提供了强大的多数据源抽取和标准化能力，支持从 `sources.yaml` 配置文件中定义的多种数据源抽取原始数据，并将所有数据统一转换为标准化的Pandas DataFrame格式进行后续处理。

## 支持的数据源类型

✅ **文件系统** (filesystem)
- PDF文件 (.pdf)
- Word文档 (.docx)
- Markdown文件 (.md)
- 纯文本文件 (.txt)

✅ **CSV/Excel文件** (csv_excel)
- CSV文件 (.csv)
- Excel文件 (.xlsx, .xls)

✅ **网页URL** (web_url)
- 单个网页抓取
- 多页面爬取
- 自定义请求头

✅ **Confluence** (confluence)
- Confluence Cloud/Server
- 空间页面批量抓取
- 页面内容和元数据

✅ **Git仓库** (git)
- 公开/私有仓库
- 指定分支和文件类型
- 提交历史信息

## 核心特性

### 1. 统一数据格式
所有数据源的数据都被标准化为包含以下列的DataFrame：
- `content`: 文本内容或行数据
- `metadata`: 来源信息和元数据

### 2. 数据质量控制
- 自动去重
- 空内容过滤
- 内容长度限制
- 空白字符标准化

### 3. 丰富的元数据
每条记录包含详细的来源信息：
- 数据源名称和类型
- 文件路径/URL信息
- 抽取时间戳
- 处理状态信息

## 配置文件

### 配置文件位置
```
app/configs/sources.yaml
```

### 配置文件结构
```yaml
data_sources:
  filesystem:
    - name: "医学文档库"
      type: "filesystem"
      enabled: true
      config:
        path: "./data_sample"
        file_types: ["*.pdf", "*.docx", "*.md", "*.txt"]
        recursive: true
      metadata:
        source_type: "document"
        category: "medical_docs"

  csv_excel:
    - name: "医学数据表"
      type: "csv_excel"
      enabled: true
      config:
        path: "./data/medical_data.csv"
        encoding: "utf-8"
        delimiter: ","
      metadata:
        source_type: "structured_data"
        category: "medical_records"

global_config:
  standardization:
    max_content_length: 10000
    min_content_length: 10
    remove_duplicates: true
    remove_empty: true
```

## 使用方法

### 1. 命令行工具

#### 抽取所有启用的数据源
```bash
python scripts/extract_and_ingest.py --all
```

#### 按数据源类型抽取
```bash
python scripts/extract_and_ingest.py --type filesystem
python scripts/extract_and_ingest.py --type csv_excel
python scripts/extract_and_ingest.py --type web_url
```

#### 按数据源名称抽取
```bash
python scripts/extract_and_ingest.py --name "医学文档库"
```

#### 列出所有可用数据源
```bash
python scripts/extract_and_ingest.py --list
```

#### 测试数据源连接
```bash
python scripts/extract_and_ingest.py --test "医学文档库"
```

### 2. Python API

```python
from app.services.data_extraction_service import DataExtractionService

# 初始化服务
service = DataExtractionService()

# 抽取所有数据源
df = service.extract_all_sources()

# 按类型抽取
df = service.extract_by_source_type('filesystem')

# 按名称抽取
df = service.extract_by_source_name('医学文档库')

# 获取统计信息
stats = service.get_extraction_statistics(df)
```

### 3. 与现有系统集成

```python
from app.services.knowledge_service import (
    extract_and_ingest_all_sources,
    extract_and_ingest_by_source_type
)

# 抽取并直接摄取到向量存储
extract_and_ingest_all_sources()
extract_and_ingest_by_source_type('filesystem')
```

## 数据标准化流程

1. **结构验证**: 确保DataFrame包含必需的列
2. **内容清理**: 标准化空白字符和特殊字符
3. **质量过滤**: 移除空内容和过短/过长的内容
4. **去重处理**: 基于内容的智能去重
5. **元数据标准化**: 统一元数据格式和结构
6. **处理信息添加**: 添加标准化过程的详细信息

## 扩展新数据源

### 1. 创建抽取器类
```python
from app.core.extractors.base_extractor import BaseExtractor

class MyCustomExtractor(BaseExtractor):
    def extract(self) -> pd.DataFrame:
        # 实现具体的抽取逻辑
        data = []
        # ... 抽取数据 ...
        return self._create_standardized_dataframe(data)
```

### 2. 注册抽取器
在 `DataExtractionService` 中添加映射：
```python
self.extractor_mapping = {
    'my_custom': MyCustomExtractor,
    # ... 其他抽取器
}
```

### 3. 更新配置文件
在 `sources.yaml` 中添加相应配置段。

## 监控和统计

系统提供丰富的统计信息：
- 抽取记录总数
- 内容长度统计
- 数据源分布
- 处理成功/失败率
- 数据质量指标

## 错误处理

- 自动跳过无法处理的文件
- 详细的错误日志
- 优雅的异常处理
- 连接测试功能

## 性能优化

- 多线程文件处理
- 增量更新支持
- 内存优化的大文件处理
- 可配置的处理参数

## 向后兼容性

保留了原有的 `ingest_data.py` 脚本功能，确保现有工作流程不受影响。新功能作为增强版本提供，可以与现有系统无缝集成。 