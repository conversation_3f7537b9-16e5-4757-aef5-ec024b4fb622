"""混合检索器实现

实现SRH-001混合搜索核心功能，结合语义搜索和关键词搜索，
使用RRF（倒数排序融合）算法合并结果。
"""

from typing import List, Dict, Any, Optional, Union
import asyncio
from concurrent.futures import ThreadPoolExecutor, as_completed
from langchain_core.retrievers import BaseRetriever
from langchain_core.documents import Document
from langchain_core.callbacks import CallbackManagerForRetrieverRun

from ..vector_store import get_vector_store
from ..search_engines import BaseSearchEngine, ElasticsearchEngine, SimpleSearchEngine
from ..fusion import RRFFusion
from ..rerankers import BaseReranker, get_reranker


class EnsembleRetriever(BaseRetriever):
    """混合检索器

    结合向量搜索和关键词搜索，使用RRF算法融合结果。
    实现SRH-001功能要求。
    """

    # 定义模型字段
    vector_retriever: Optional[BaseRetriever] = None
    keyword_search_engine: Optional[BaseSearchEngine] = None
    vector_weight: float = 0.5
    keyword_weight: float = 0.5
    rrf_k: int = 60
    top_k: int = 10
    rrf_fusion: Optional[RRFFusion] = None
    reranker: Optional[BaseReranker] = None
    enable_reranking: bool = False

    class Config:
        arbitrary_types_allowed = True
    
    def __init__(
        self,
        vector_retriever: Optional[BaseRetriever] = None,
        keyword_search_engine: Optional[BaseSearchEngine] = None,
        vector_weight: float = 0.5,
        keyword_weight: float = 0.5,
        rrf_k: int = 60,
        top_k: int = 10,
        search_engine_config: Optional[Dict[str, Any]] = None,
        reranker_config: Optional[Dict[str, Any]] = None,
        enable_reranking: bool = False,
        **kwargs
    ):
        """初始化混合检索器

        Args:
            vector_retriever: 向量检索器，如果为None则自动创建
            keyword_search_engine: 关键词搜索引擎，如果为None则自动创建
            vector_weight: 向量搜索权重
            keyword_weight: 关键词搜索权重
            rrf_k: RRF算法的k参数
            top_k: 返回的文档数量
            search_engine_config: 搜索引擎配置
            reranker_config: 重排器配置
            enable_reranking: 是否启用重排器
        """
        # 初始化向量检索器
        if vector_retriever is None:
            vector_store = get_vector_store()
            vector_retriever = vector_store.as_retriever(search_kwargs={"k": top_k})

        # 初始化关键词搜索引擎
        if keyword_search_engine is None:
            keyword_search_engine = EnsembleRetriever._init_default_search_engine(search_engine_config)

        # 初始化RRF融合器
        rrf_fusion = RRFFusion(k=rrf_k)

        # 初始化重排器
        reranker = None
        if enable_reranking:
            reranker = self._init_reranker(reranker_config)

        # 调用父类初始化
        super().__init__(
            vector_retriever=vector_retriever,
            keyword_search_engine=keyword_search_engine,
            vector_weight=vector_weight,
            keyword_weight=keyword_weight,
            rrf_k=rrf_k,
            top_k=top_k,
            rrf_fusion=rrf_fusion,
            reranker=reranker,
            enable_reranking=enable_reranking,
            **kwargs
        )

        print(f"✅ EnsembleRetriever初始化完成")
        print(f"   - 向量搜索权重: {vector_weight}")
        print(f"   - 关键词搜索权重: {keyword_weight}")
        print(f"   - RRF参数k: {rrf_k}")
        print(f"   - 返回文档数: {top_k}")
        print(f"   - 重排器启用: {enable_reranking}")
        if enable_reranking and reranker:
            print(f"   - 重排器类型: {reranker.__class__.__name__}")
            print(f"   - 重排器模型: {reranker.model_name}")
    
    @staticmethod
    def _init_default_search_engine(config: Optional[Dict[str, Any]] = None) -> BaseSearchEngine:
        """初始化默认搜索引擎"""
        config = config or {}
        engine_type = config.get('type', 'simple')

        try:
            if engine_type == 'elasticsearch':
                return ElasticsearchEngine(config.get('elasticsearch', {}))
            else:
                return SimpleSearchEngine(config.get('simple', {}))
        except Exception as e:
            print(f"⚠️  搜索引擎初始化失败，使用简单搜索引擎: {e}")
            return SimpleSearchEngine({})

    @staticmethod
    def _init_reranker(config: Optional[Dict[str, Any]] = None) -> Optional[BaseReranker]:
        """初始化重排器"""
        if not config:
            print("⚠️  重排器配置为空，使用默认配置")
            config = {}

        try:
            reranker_type = config.get('type', 'bge_reranker')

            # 构建重排器配置
            reranker_config = {
                'model_name': config.get('model', {}).get('name', 'BAAI/bge-reranker-base'),
                'device': config.get('model', {}).get('device', 'cpu'),
                'max_length': config.get('model', {}).get('max_length', 512),
                'normalize_scores': config.get('model', {}).get('normalize_scores', True),
                'top_n': config.get('parameters', {}).get('top_n', 20),
                'batch_size': config.get('parameters', {}).get('batch_size', 32),
                'max_content_length': config.get('parameters', {}).get('max_content_length', 512)
            }

            reranker = get_reranker(
                reranker_type,
                reranker_config,
                use_cache=config.get('performance', {}).get('use_cache', True)
            )

            print(f"✅ 重排器初始化成功: {reranker_type}")
            return reranker

        except Exception as e:
            print(f"⚠️  重排器初始化失败: {e}")
            return None
    
    def _get_relevant_documents(
        self, 
        query: str, 
        *, 
        run_manager: CallbackManagerForRetrieverRun
    ) -> List[Document]:
        """获取相关文档的主要方法"""
        print(f"🔍 开始混合搜索: {query}")
        
        # 并行执行向量搜索和关键词搜索
        vector_docs = []
        keyword_docs = []
        
        with ThreadPoolExecutor(max_workers=2) as executor:
            # 提交搜索任务
            vector_future = executor.submit(self._vector_search, query)
            keyword_future = executor.submit(self._keyword_search, query)
            
            # 获取结果
            try:
                vector_docs = vector_future.result(timeout=30)
                print(f"   向量搜索返回 {len(vector_docs)} 个文档")
            except Exception as e:
                print(f"   ⚠️  向量搜索失败: {e}")
            
            try:
                keyword_docs = keyword_future.result(timeout=30)
                print(f"   关键词搜索返回 {len(keyword_docs)} 个文档")
            except Exception as e:
                print(f"   ⚠️  关键词搜索失败: {e}")
        
        # 使用RRF算法融合结果
        fused_docs = self._rrf_fusion_with_module(vector_docs, keyword_docs)
        print(f"   RRF融合后返回 {len(fused_docs)} 个文档")

        # 如果启用重排器，进行精排
        if self.enable_reranking and self.reranker:
            try:
                print(f"🔄 开始重排器精排...")
                reranked_docs = self.reranker.rerank(query, fused_docs, top_n=self.top_k)
                print(f"   重排器精排后返回 {len(reranked_docs)} 个文档")
                return reranked_docs
            except Exception as e:
                print(f"⚠️  重排器精排失败，返回融合结果: {e}")
                return fused_docs[:self.top_k]

        return fused_docs[:self.top_k]
    
    def _vector_search(self, query: str) -> List[Document]:
        """执行向量搜索"""
        try:
            return self.vector_retriever.get_relevant_documents(query)
        except Exception as e:
            print(f"向量搜索错误: {e}")
            return []
    
    def _keyword_search(self, query: str) -> List[Document]:
        """执行关键词搜索"""
        try:
            # 使用搜索引擎进行关键词搜索
            search_results = self.keyword_search_engine.search(
                query=query,
                size=self.top_k * 2  # 获取更多结果用于融合
            )
            
            # 转换为Document对象
            documents = []
            for result in search_results:
                content = result.get('content', '')
                metadata = {k: v for k, v in result.items() if k != 'content'}
                metadata['search_score'] = result.get('_score', 0)
                documents.append(Document(page_content=content, metadata=metadata))
            
            return documents
        except Exception as e:
            print(f"关键词搜索错误: {e}")
            return []

    def _rrf_fusion_with_module(self, vector_docs: List[Document], keyword_docs: List[Document]) -> List[Document]:
        """使用RRF模块进行融合"""
        search_results = {
            'vector': vector_docs,
            'keyword': keyword_docs
        }

        weights = {
            'vector': self.vector_weight,
            'keyword': self.keyword_weight
        }

        return self.rrf_fusion.fuse_results(search_results, weights)

    def _rrf_fusion(self, vector_docs: List[Document], keyword_docs: List[Document]) -> List[Document]:
        """使用RRF算法融合搜索结果
        
        RRF公式: score = Σ(weight / (k + rank))
        其中rank是文档在各个搜索结果中的排名（从1开始）
        """
        # 创建文档到分数的映射
        doc_scores = {}
        doc_objects = {}  # 保存文档对象的引用
        
        # 处理向量搜索结果
        for rank, doc in enumerate(vector_docs, 1):
            doc_key = self._get_doc_key(doc)
            doc_objects[doc_key] = doc
            
            # RRF分数计算
            rrf_score = self.vector_weight / (self.rrf_k + rank)
            doc_scores[doc_key] = doc_scores.get(doc_key, 0) + rrf_score
        
        # 处理关键词搜索结果
        for rank, doc in enumerate(keyword_docs, 1):
            doc_key = self._get_doc_key(doc)
            if doc_key not in doc_objects:
                doc_objects[doc_key] = doc
            
            # RRF分数计算
            rrf_score = self.keyword_weight / (self.rrf_k + rank)
            doc_scores[doc_key] = doc_scores.get(doc_key, 0) + rrf_score
        
        # 按融合分数排序
        sorted_docs = sorted(
            doc_scores.items(),
            key=lambda x: x[1],
            reverse=True
        )
        
        # 返回排序后的文档，并在元数据中添加融合分数
        result_docs = []
        for doc_key, score in sorted_docs:
            doc = doc_objects[doc_key]
            # 添加融合分数到元数据
            doc.metadata['rrf_score'] = score
            result_docs.append(doc)
        
        return result_docs
    
    def _get_doc_key(self, doc: Document) -> str:
        """生成文档的唯一标识符
        
        优先使用chunk_id，然后是_id，最后使用内容的hash
        """
        metadata = doc.metadata
        
        # 优先使用chunk_id
        if 'chunk_id' in metadata:
            return f"chunk_{metadata['chunk_id']}"
        
        # 其次使用_id
        if '_id' in metadata:
            return f"id_{metadata['_id']}"
        
        # 最后使用内容hash
        content_hash = hash(doc.page_content)
        return f"hash_{content_hash}"

    async def _aget_relevant_documents(
        self,
        query: str,
        *,
        run_manager: CallbackManagerForRetrieverRun
    ) -> List[Document]:
        """异步获取相关文档"""
        # 对于异步版本，我们可以使用asyncio来并行执行搜索
        print(f"🔍 开始异步混合搜索: {query}")

        # 创建异步任务
        vector_task = asyncio.create_task(self._async_vector_search(query))
        keyword_task = asyncio.create_task(self._async_keyword_search(query))

        # 等待两个搜索完成
        vector_docs, keyword_docs = await asyncio.gather(
            vector_task, keyword_task, return_exceptions=True
        )

        # 处理异常结果
        if isinstance(vector_docs, Exception):
            print(f"   ⚠️  异步向量搜索失败: {vector_docs}")
            vector_docs = []
        else:
            print(f"   异步向量搜索返回 {len(vector_docs)} 个文档")

        if isinstance(keyword_docs, Exception):
            print(f"   ⚠️  异步关键词搜索失败: {keyword_docs}")
            keyword_docs = []
        else:
            print(f"   异步关键词搜索返回 {len(keyword_docs)} 个文档")

        # 使用RRF算法融合结果
        fused_docs = self._rrf_fusion_with_module(vector_docs, keyword_docs)
        print(f"   异步RRF融合后返回 {len(fused_docs)} 个文档")

        # 如果启用重排器，进行精排
        if self.enable_reranking and self.reranker:
            try:
                print(f"🔄 开始异步重排器精排...")
                # 在线程池中运行重排器
                loop = asyncio.get_event_loop()
                reranked_docs = await loop.run_in_executor(
                    None,
                    self.reranker.rerank,
                    query,
                    fused_docs,
                    self.top_k
                )
                print(f"   异步重排器精排后返回 {len(reranked_docs)} 个文档")
                return reranked_docs
            except Exception as e:
                print(f"⚠️  异步重排器精排失败，返回融合结果: {e}")
                return fused_docs[:self.top_k]

        return fused_docs[:self.top_k]

    async def _async_vector_search(self, query: str) -> List[Document]:
        """异步向量搜索"""
        try:
            # 由于langchain的向量检索器可能不支持异步，我们在线程池中运行
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, self._vector_search, query)
        except Exception as e:
            print(f"异步向量搜索错误: {e}")
            return []

    async def _async_keyword_search(self, query: str) -> List[Document]:
        """异步关键词搜索"""
        try:
            # 在线程池中运行关键词搜索
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, self._keyword_search, query)
        except Exception as e:
            print(f"异步关键词搜索错误: {e}")
            return []

    def get_search_stats(self) -> Dict[str, Any]:
        """获取搜索统计信息"""
        stats = {
            'retriever_type': 'EnsembleRetriever',
            'vector_weight': self.vector_weight,
            'keyword_weight': self.keyword_weight,
            'rrf_k': self.rrf_k,
            'top_k': self.top_k,
            'vector_retriever_type': type(self.vector_retriever).__name__,
            'keyword_search_engine_type': type(self.keyword_search_engine).__name__,
            'reranking_enabled': self.enable_reranking
        }

        # 添加搜索引擎信息
        if hasattr(self.keyword_search_engine, 'get_engine_info'):
            stats['keyword_engine_info'] = self.keyword_search_engine.get_engine_info()

        # 添加重排器信息
        if self.enable_reranking and self.reranker:
            stats['reranker_info'] = self.reranker.get_model_info()
            if hasattr(self.reranker, 'get_performance_stats'):
                stats['reranker_performance'] = self.reranker.get_performance_stats()

        return stats

    def update_weights(self, vector_weight: float, keyword_weight: float):
        """更新搜索权重"""
        self.vector_weight = vector_weight
        self.keyword_weight = keyword_weight
        print(f"✅ 搜索权重已更新: 向量={vector_weight}, 关键词={keyword_weight}")

    def update_rrf_k(self, k: int):
        """更新RRF参数k"""
        self.rrf_k = k
        self.rrf_fusion.k = k
        print(f"✅ RRF参数k已更新: {k}")


def create_ensemble_retriever(
    vector_weight: float = 0.5,
    keyword_weight: float = 0.5,
    rrf_k: int = 60,
    top_k: int = 10,
    search_engine_config: Optional[Dict[str, Any]] = None,
    reranker_config: Optional[Dict[str, Any]] = None,
    enable_reranking: bool = False
) -> EnsembleRetriever:
    """创建混合检索器的工厂函数

    Args:
        vector_weight: 向量搜索权重
        keyword_weight: 关键词搜索权重
        rrf_k: RRF算法的k参数
        top_k: 返回的文档数量
        search_engine_config: 搜索引擎配置
        reranker_config: 重排器配置
        enable_reranking: 是否启用重排器

    Returns:
        配置好的EnsembleRetriever实例
    """
    return EnsembleRetriever(
        vector_weight=vector_weight,
        keyword_weight=keyword_weight,
        rrf_k=rrf_k,
        top_k=top_k,
        search_engine_config=search_engine_config,
        reranker_config=reranker_config,
        enable_reranking=enable_reranking
    )
