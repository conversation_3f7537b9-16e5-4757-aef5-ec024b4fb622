/* 现代化组件样式 */

/* 现代按钮系统 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--duration-normal) var(--ease-out);
    position: relative;
    overflow: hidden;
    text-decoration: none;
    font-family: inherit;
    letter-spacing: -0.025em;
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all var(--duration-normal) var(--ease-out);
}

.btn:hover::before {
    width: 300px;
    height: 300px;
}

.btn:active {
    transform: translateY(1px);
}

.btn-primary {
    background: var(--primary-gradient);
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--white);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
    box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
    background: var(--gray-50);
    border-color: var(--gray-400);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-success {
    background: var(--secondary-gradient);
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* 加入会话按钮 */
.join-session-area {
    padding: 2rem;
    text-align: center;
    background: var(--white);
    border-radius: var(--radius-xl);
    border: 1px solid var(--gray-200);
    margin-bottom: 1.5rem;
    box-shadow: var(--shadow-sm);
}

.join-session-btn {
    background: var(--primary-gradient);
    color: var(--white);
    border: none;
    border-radius: var(--radius-lg);
    padding: 1rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--duration-normal) var(--ease-out);
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.join-session-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all var(--duration-normal) var(--ease-out);
}

.join-session-btn:hover::before {
    width: 300px;
    height: 300px;
}

.join-session-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* 医学关键词高亮 */
.highlight-symptom {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(16, 185, 129, 0.1));
    color: var(--primary-dark);
    padding: 0.125rem 0.25rem;
    border-radius: var(--radius-sm);
    font-weight: 500;
}

/* 消息输入区域 */
.message-input-area {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    margin-top: 1rem;
    transition: all var(--duration-normal) var(--ease-out);
}

.message-input-area:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--gray-300);
}

.input-container {
    display: flex;
    align-items: flex-end;
    gap: 1rem;
}

.doctor-message-input {
    flex: 1;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-lg);
    padding: 0.875rem 1.125rem;
    font-size: 0.9375rem;
    line-height: 1.5;
    resize: none;
    min-height: 48px;
    max-height: 120px;
    font-family: inherit;
    transition: all var(--duration-normal) var(--ease-out);
    background: var(--gray-50);
}

.doctor-message-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px var(--primary-100);
    background: var(--white);
}

.doctor-message-input::placeholder {
    color: var(--gray-500);
    font-weight: 500;
}

.send-message-btn {
    background: var(--primary-gradient);
    color: var(--white);
    border: none;
    border-radius: var(--radius-lg);
    padding: 0.875rem;
    cursor: pointer;
    transition: all var(--duration-normal) var(--ease-out);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 48px;
    height: 48px;
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.send-message-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all var(--duration-normal) var(--ease-out);
}

.send-message-btn:hover:not(:disabled)::before {
    width: 100px;
    height: 100px;
}

.send-message-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.send-message-btn:disabled {
    background: var(--gray-300);
    cursor: not-allowed;
    transform: none;
    box-shadow: var(--shadow-sm);
}

/* 诊断和处方区域样式优化 */
.diagnosis-section {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.diagnosis-item {
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    border: 1px solid var(--gray-200);
}

.diagnosis-header {
    margin-bottom: 1rem;
}

.diagnosis-header h4 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-900);
}

.diagnosis-content p {
    color: var(--gray-700);
    line-height: 1.6;
}

.diagnosis-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--gray-200);
}

/* 加载和错误状态 */
.loading-state,
.error-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 2rem;
    text-align: center;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--gray-200);
    border-top: 3px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-icon {
    color: var(--danger);
    margin-bottom: 1rem;
}

.error-state p {
    color: var(--gray-600);
    margin-bottom: 1.5rem;
}

/* 处方表单样式 */
.prescription-form {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: 2rem;
}

.prescription-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--gray-200);
}

/* 模态框样式 */
.join-session-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
    animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
    to {
        opacity: 1;
        backdrop-filter: blur(8px);
    }
}

.modal-content {
    background: var(--white);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-2xl);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease-out;
    border: 1px solid var(--gray-200);
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: 2rem 2rem 1rem;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--gray-900);
    letter-spacing: -0.025em;
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--gray-500);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--radius-md);
    transition: all var(--duration-normal) var(--ease-out);
}

.close-modal:hover {
    background: var(--gray-100);
    color: var(--gray-700);
}

.modal-body {
    padding: 1rem 2rem;
}

.modal-body p {
    color: var(--gray-600);
    margin-bottom: 1rem;
    line-height: 1.6;
    font-weight: 500;
}

.join-message-options {
    margin: 1.5rem 0;
}

.join-message-option {
    padding: 1rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--duration-normal) var(--ease-out);
    margin-bottom: 0.75rem;
    background: var(--gray-50);
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-700);
}

.join-message-option:hover {
    border-color: var(--primary);
    background: var(--primary-50);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.join-message-option.selected {
    border-color: var(--primary);
    background: var(--primary-50);
    color: var(--primary-700);
    box-shadow: var(--shadow-sm);
}

#customJoinMessage {
    width: 100%;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-lg);
    padding: 0.875rem;
    font-size: 0.875rem;
    line-height: 1.5;
    resize: vertical;
    min-height: 80px;
    font-family: inherit;
    transition: all var(--duration-normal) var(--ease-out);
    background: var(--gray-50);
}

#customJoinMessage:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px var(--primary-100);
    background: var(--white);
}

.modal-footer {
    padding: 1rem 2rem 2rem;
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    border-top: 1px solid var(--gray-200);
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: var(--radius-lg);
    color: var(--white);
    font-weight: 600;
    z-index: 1001;
    animation: notificationSlideIn 0.3s ease-out;
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes notificationSlideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification.success {
    background: var(--secondary-gradient);
}

.notification.error {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.notification.info {
    background: var(--primary-gradient);
}


