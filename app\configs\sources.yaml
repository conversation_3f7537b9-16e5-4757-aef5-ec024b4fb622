# 数据源配置文件
# 支持的数据源类型：filesystem, csv_excel, web_url, confluence, git

# 全局配置
global_config:
  # 数据标准化配置
  standardization:
    min_content_length: 10
    max_content_length: 10000
    remove_duplicates: true

  # 文本切分配置
  text_splitting:
    chunk_size: 1000
    chunk_overlap: 200
    recursive:
      separators: ["\n\n", "\n", "。", "！", "？", "；", "，", ".", "!", "?", ";", ",", " ", ""]
    semantic:
      embedding_model: "text-embedding-ada-002"
      similarity_threshold: 0.7

  # 双重索引配置
  indexing:
    vector_collection_name: "medical_documents"
    search_index_name: "medical_documents"
    search_engine_type: "simple"  # 可选: "elasticsearch", "simple"
    search_engine_config:
      data_dir: "./data/search_index"
      enable_persistence: true
      # Elasticsearch配置（如果使用）
      # host: "localhost"
      # port: 9200
      # username: ""
      # password: ""
      # use_ssl: false

  # 输出格式配置
  output:
    format: "dataframe"  # 可选: "dataframe", "json", "csv"

data_sources:
  # 文件系统数据源 - 支持 PDF/DOCX/MD/TXT 等文件
  filesystem:
    - name: "医学文档库"
      type: "filesystem"
      enabled: true
      config:
        path: "./data_sample"
        file_types: ["*.pdf", "*.docx", "*.md", "*.txt"]
        recursive: true
        encoding: "utf-8"
      metadata:
        source_type: "document"
        category: "medical_docs"
      # 数据清洗步骤配置
      cleaning_steps:
        - type: "regex"
          description: "移除页眉页脚信息"
          pattern: "页码[：:]\\s*\\d+"
          replace_with: ""
          columns: ["content"]
        - type: "regex"
          description: "移除医学编码"
          pattern: "\\b[A-Z]\\d{2}\\.\\d+\\b"
          replace_with: ""
          columns: ["content"]
        - type: "apply"
          description: "标准化医学术语"
          function_path: "scripts.custom_cleaners.normalize_medical_terms"
          columns: ["content"]
        - type: "apply"
          description: "移除个人信息"
          function_path: "scripts.custom_cleaners.remove_personal_info"
          columns: ["content"]
        - type: "dropna"
          description: "移除空内容记录"
          subset: ["content"]

  # CSV/Excel 文件数据源
  csv_excel:
    - name: "医学数据表"
      type: "csv_excel"
      enabled: true
      config:
        path: "./data/medical_data.csv"
        encoding: "utf-8"
        delimiter: ","
        header: 0
      metadata:
        source_type: "structured_data"
        category: "medical_records"
      # 数据清洗步骤配置
      cleaning_steps:
        - type: "fillna"
          description: "填充空值"
          value: "未知"
          columns: ["content"]
        - type: "drop_duplicates"
          description: "移除重复记录"
          subset: ["content"]
          keep: "first"
        - type: "apply"
          description: "标准化疾病名称"
          function_path: "scripts.custom_cleaners.standardize_disease_names"
          columns: ["content"]
      # 行转文本配置
      row_to_text:
        enabled: true
        template: "疾病'{疾病名称}'的主要症状包括{症状}，推荐的治疗方法是{治疗方法}，预防措施为{预防措施}。"
        output_column: "natural_language_description"
        fallback_template: "医学记录：疾病名称为{疾病名称}，相关信息：症状({症状})，治疗({治疗方法})，预防({预防措施})。"
        preprocessing:
          null_replacements:
            疾病名称: "未知疾病"
            症状: "症状不明"
            治疗方法: "治疗方案待定"
            预防措施: "预防措施待补充"
    
    - name: "药物信息表"
      type: "csv_excel"
      enabled: false
      config:
        path: "./data/drug_info.xlsx"
        sheet_name: "Sheet1"
      metadata:
        source_type: "structured_data"
        category: "drug_info"
      # 数据清洗步骤配置
      cleaning_steps:
        - type: "regex"
          description: "清理药物编码"
          pattern: "\\b[A-Z]{2,4}\\d{4,6}\\b"
          replace_with: ""
          columns: ["content"]
        - type: "fillna"
          description: "填充缺失的药物信息"
          value: "信息缺失"
        - type: "drop_duplicates"
          description: "移除重复的药物记录"
          subset: ["content"]
      # 行转文本配置
      row_to_text:
        enabled: true
        template: "药物'{drug_name}'的规格为{specification}，适应症为{indication}，用法用量：{dosage}，注意事项：{precautions}。"
        output_column: "natural_language_description"
        preprocessing:
          null_replacements:
            drug_name: "未知药物"
            specification: "规格不详"
            indication: "适应症待查"
            dosage: "用量请咨询医生"
            precautions: "请遵医嘱"

  # 网页URL数据源
  web_url:
    - name: "医学知识网站"
      type: "web_url"
      enabled: true
      config:
        urls:
          - "https://zh.wikipedia.org/wiki/%E6%B5%81%E8%A1%8C%E6%80%A7%E6%84%9F%E5%86%92?utm_source=chatgpt.com"
        max_pages: 10
        timeout: 30
        headers:
          User-Agent: "MedicalBot/1.0"
      metadata:
        source_type: "web_content"
        category: "online_medical"

  # Confluence数据源
  confluence:
    - name: "内部医学知识库"
      type: "confluence"
      enabled: false
      config:
        base_url: "https://your-company.atlassian.net/wiki"
        username: "your-username"
        api_token: "your-api-token"
        space_key: "MEDICAL"
        max_pages: 100
      metadata:
        source_type: "confluence"
        category: "internal_docs"

  # Git仓库数据源
  git:
    - name: "医学研究资料库"
      type: "git"
      enabled: false
      config:
        repository_url: "https://github.com/medical-research/docs.git"
        branch: "main"
        local_path: "./temp/medical_research"
        file_patterns: ["*.md", "*.txt", "*.rst"]
        clone_depth: 1
      metadata:
        source_type: "git_repository"
        category: "research_docs"

# 全局配置
global_config:
  # 数据标准化配置
  standardization:
    content_column: "content"
    metadata_column: "metadata"
    max_content_length: 10000
    min_content_length: 10
    
  # 数据质量配置
  quality_control:
    remove_duplicates: true
    remove_empty: true
    normalize_whitespace: true
    
  # 输出配置
  output:
    format: "dataframe"  # dataframe, json, csv
    include_source_info: true
    add_timestamp: true 