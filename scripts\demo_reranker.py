"""重排器功能演示脚本

演示SRH-002可插拔结果重排功能，展示Cross-encoder模型的精排效果
"""

import sys
import os
from typing import List, Dict, Any
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from langchain_core.documents import Document
from app.core.rerankers import (
    CrossEncoderReranker, 
    RerankerFactory,
    get_reranker
)
from app.core.retrievers.ensemble_retriever import create_ensemble_retriever
from app.configs.settings import config


def create_medical_documents() -> List[Document]:
    """创建医学文档用于演示"""
    documents = [
        Document(
            page_content="高血压是一种常见的心血管疾病，收缩压持续≥140mmHg或舒张压≥90mmHg。主要症状包括头痛、头晕、心悸、胸闷等。治疗包括生活方式干预和药物治疗。",
            metadata={"source": "心血管疾病指南", "chunk_id": "hypertension_1", "category": "疾病"}
        ),
        Document(
            page_content="糖尿病是一组以高血糖为特征的代谢性疾病。1型糖尿病多见于青少年，2型糖尿病多见于成年人。需要控制血糖、血压、血脂。",
            metadata={"source": "内分泌疾病手册", "chunk_id": "diabetes_1", "category": "疾病"}
        ),
        Document(
            page_content="阿司匹林是一种非甾体抗炎药，具有解热、镇痛、抗炎和抗血小板聚集作用。常用于心血管疾病的一级和二级预防。",
            metadata={"source": "药物治疗学", "chunk_id": "aspirin_1", "category": "药物"}
        ),
        Document(
            page_content="心肌梗死是冠状动脉急性闭塞导致心肌缺血性坏死。典型症状为持续性胸痛，可放射至左臂、颈部。需要紧急介入治疗。",
            metadata={"source": "急诊医学", "chunk_id": "mi_1", "category": "疾病"}
        ),
        Document(
            page_content="心电图是记录心脏电活动的检查方法，可诊断心律失常、心肌梗死、心肌缺血等心脏疾病。是心血管疾病诊断的重要工具。",
            metadata={"source": "心电图诊断学", "chunk_id": "ecg_1", "category": "检查"}
        ),
        Document(
            page_content="高血压患者的生活方式管理包括：限制钠盐摄入、控制体重、规律运动、戒烟限酒、保持心理平衡。",
            metadata={"source": "高血压防治指南", "chunk_id": "hypertension_2", "category": "治疗"}
        ),
        Document(
            page_content="ACE抑制剂是治疗高血压的一线药物，通过抑制血管紧张素转换酶降低血压。常见副作用包括干咳、血管性水肿。",
            metadata={"source": "心血管药物学", "chunk_id": "ace_inhibitor_1", "category": "药物"}
        ),
        Document(
            page_content="冠心病是冠状动脉粥样硬化性心脏病，主要表现为心绞痛和心肌梗死。危险因素包括高血压、糖尿病、高脂血症、吸烟等。",
            metadata={"source": "心血管疾病指南", "chunk_id": "chd_1", "category": "疾病"}
        )
    ]
    return documents


def demo_reranker_basic_functionality():
    """演示重排器基本功能"""
    print("🔬 演示重排器基本功能")
    print("-" * 40)
    
    # 创建重排器
    reranker = CrossEncoderReranker({
        'model_name': 'BAAI/bge-reranker-base',
        'top_n': 5,
        'batch_size': 8,
        'device': 'cpu'
    })
    
    # 创建测试文档
    documents = create_medical_documents()
    
    # 测试查询
    queries = [
        "高血压的症状和治疗方法",
        "心肌梗死的诊断和急救",
        "糖尿病患者的血糖控制",
        "阿司匹林的作用机制和副作用"
    ]
    
    for query in queries:
        print(f"\n查询: '{query}'")
        
        # 模拟初始检索结果（随机顺序）
        import random
        shuffled_docs = documents.copy()
        random.shuffle(shuffled_docs)
        initial_docs = shuffled_docs[:6]  # 取前6个作为初始结果
        
        print("初始检索结果（未重排）:")
        for i, doc in enumerate(initial_docs, 1):
            content = doc.page_content[:60] + "..."
            category = doc.metadata.get('category', 'Unknown')
            print(f"  {i}. [{category}] {content}")
        
        # 使用重排器重排
        start_time = time.time()
        reranked_docs = reranker.rerank(query, initial_docs, top_n=3)
        end_time = time.time()
        
        print(f"\n重排后结果（Top-3，耗时: {end_time - start_time:.3f}秒）:")
        for i, doc in enumerate(reranked_docs, 1):
            score = doc.metadata['rerank_score']
            content = doc.page_content[:60] + "..."
            category = doc.metadata.get('category', 'Unknown')
            print(f"  {i}. 分数: {score:.4f} [{category}] {content}")


def demo_reranker_comparison():
    """演示不同重排器的比较"""
    print("\n🔍 演示不同重排器的比较")
    print("-" * 40)
    
    # 创建不同类型的重排器
    rerankers = {
        'BGE重排器': RerankerFactory.create_bge_reranker({'top_n': 3}),
        '轻量级重排器': RerankerFactory.create_lightweight_reranker({'top_n': 3})
    }
    
    documents = create_medical_documents()[:5]  # 使用前5个文档
    query = "高血压的药物治疗方案"
    
    print(f"查询: '{query}'")
    print(f"文档数量: {len(documents)}")
    
    for name, reranker in rerankers.items():
        print(f"\n{name} ({reranker.model_name}):")
        
        try:
            start_time = time.time()
            reranked_docs = reranker.rerank(query, documents)
            end_time = time.time()
            
            for i, doc in enumerate(reranked_docs, 1):
                score = doc.metadata['rerank_score']
                content = doc.page_content[:50] + "..."
                print(f"  {i}. 分数: {score:.4f} - {content}")
            
            print(f"  处理时间: {end_time - start_time:.3f}秒")
            
        except Exception as e:
            print(f"  ⚠️  重排失败: {e}")


def demo_ensemble_retriever_with_reranker():
    """演示集成重排器的混合检索器"""
    print("\n🔄 演示集成重排器的混合检索器")
    print("-" * 40)
    
    # 创建不启用重排器的检索器
    retriever_without_reranker = create_ensemble_retriever(
        vector_weight=0.6,
        keyword_weight=0.4,
        top_k=5,
        enable_reranking=False
    )
    
    # 创建启用重排器的检索器
    reranker_config = {
        'type': 'bge_reranker',
        'model': {
            'name': 'BAAI/bge-reranker-base',
            'device': 'cpu'
        },
        'parameters': {
            'top_n': 5,
            'batch_size': 16
        }
    }
    
    retriever_with_reranker = create_ensemble_retriever(
        vector_weight=0.6,
        keyword_weight=0.4,
        top_k=5,
        reranker_config=reranker_config,
        enable_reranking=True
    )
    
    print("检索器配置对比:")
    print(f"  不启用重排器: {retriever_without_reranker.enable_reranking}")
    print(f"  启用重排器: {retriever_with_reranker.enable_reranking}")
    if retriever_with_reranker.reranker:
        print(f"  重排器模型: {retriever_with_reranker.reranker.model_name}")
    
    # 获取统计信息
    stats_without = retriever_without_reranker.get_search_stats()
    stats_with = retriever_with_reranker.get_search_stats()
    
    print("\n统计信息对比:")
    print(f"  不启用重排器 - 重排启用: {stats_without.get('reranking_enabled', False)}")
    print(f"  启用重排器 - 重排启用: {stats_with.get('reranking_enabled', False)}")
    
    if 'reranker_info' in stats_with:
        reranker_info = stats_with['reranker_info']
        print(f"  重排器信息:")
        print(f"    - 模型名称: {reranker_info.get('model_name', 'Unknown')}")
        print(f"    - 重排器类型: {reranker_info.get('reranker_type', 'Unknown')}")
        print(f"    - Top-N: {reranker_info.get('top_n', 'Unknown')}")


def demo_configuration_system():
    """演示配置系统"""
    print("\n⚙️  演示配置系统")
    print("-" * 40)
    
    print("当前重排器配置:")
    print(f"  启用状态: {config.retrieval.reranker.enabled}")
    print(f"  重排器类型: {config.retrieval.reranker.type}")
    print(f"  模型名称: {config.retrieval.reranker.model.name}")
    print(f"  设备: {config.retrieval.reranker.model.device}")
    print(f"  最大长度: {config.retrieval.reranker.model.max_length}")
    print(f"  Top-N: {config.retrieval.reranker.parameters.top_n}")
    print(f"  批处理大小: {config.retrieval.reranker.parameters.batch_size}")
    print(f"  使用缓存: {config.retrieval.reranker.performance.use_cache}")
    
    # 演示如何通过配置创建重排器
    print("\n通过配置创建重排器:")
    try:
        reranker_config = {
            'type': config.retrieval.reranker.type,
            'model': config.retrieval.reranker.model.model_dump(),
            'parameters': config.retrieval.reranker.parameters.model_dump()
        }
        
        reranker = get_reranker(
            config.retrieval.reranker.type,
            {
                'model_name': config.retrieval.reranker.model.name,
                'device': config.retrieval.reranker.model.device,
                'top_n': config.retrieval.reranker.parameters.top_n,
                'batch_size': config.retrieval.reranker.parameters.batch_size
            }
        )
        
        print(f"  ✅ 成功创建重排器: {type(reranker).__name__}")
        print(f"  模型信息: {reranker.get_model_info()}")
        
    except Exception as e:
        print(f"  ⚠️  创建重排器失败: {e}")


def demo_performance_analysis():
    """演示性能分析"""
    print("\n📊 演示性能分析")
    print("-" * 40)
    
    # 创建重排器
    reranker = CrossEncoderReranker({
        'batch_size': 16,
        'top_n': 10
    })
    
    documents = create_medical_documents()
    queries = [
        "高血压治疗",
        "心肌梗死诊断", 
        "糖尿病管理",
        "药物副作用"
    ]
    
    print("性能测试结果:")
    total_time = 0
    total_docs = 0
    
    for i, query in enumerate(queries, 1):
        start_time = time.time()
        reranked_docs = reranker.rerank(query, documents, top_n=5)
        end_time = time.time()
        
        processing_time = end_time - start_time
        total_time += processing_time
        total_docs += len(documents)
        
        print(f"  查询 {i}: {processing_time:.3f}秒 ({len(documents)} 文档 -> {len(reranked_docs)} 结果)")
    
    avg_time = total_time / len(queries)
    docs_per_second = total_docs / total_time
    
    print(f"\n性能统计:")
    print(f"  平均处理时间: {avg_time:.3f}秒/查询")
    print(f"  处理速度: {docs_per_second:.1f}文档/秒")
    print(f"  总处理时间: {total_time:.3f}秒")
    
    # 获取重排器性能统计
    perf_stats = reranker.get_performance_stats()
    print(f"\n重排器统计:")
    for key, value in perf_stats.items():
        if isinstance(value, dict):
            print(f"  {key}:")
            for sub_key, sub_value in value.items():
                print(f"    {sub_key}: {sub_value}")
        else:
            print(f"  {key}: {value}")


def main():
    """主演示函数"""
    print("🚀 重排器功能演示")
    print("=" * 50)
    print("实现SRH-002可插拔结果重排功能")
    print("使用Cross-encoder模型对Top-N结果进行精排")
    print("=" * 50)
    
    try:
        # 演示重排器基本功能
        demo_reranker_basic_functionality()
        
        # 演示不同重排器比较
        demo_reranker_comparison()
        
        # 演示集成重排器的混合检索器
        demo_ensemble_retriever_with_reranker()
        
        # 演示配置系统
        demo_configuration_system()
        
        # 演示性能分析
        demo_performance_analysis()
        
        print("\n" + "=" * 50)
        print("🎉 重排器功能演示完成！")
        print("\n主要特性:")
        print("✅ 支持多种Cross-encoder模型（BGE、MiniLM等）")
        print("✅ 可插拔设计，通过配置文件控制启用/禁用")
        print("✅ 对Top-N结果进行精排，提升上下文质量")
        print("✅ 支持批处理和缓存，优化性能")
        print("✅ 集成到混合检索器，无缝融入现有架构")
        print("✅ 提供详细的性能统计和监控")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
