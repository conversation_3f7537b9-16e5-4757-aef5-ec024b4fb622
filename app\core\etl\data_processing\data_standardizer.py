"""数据标准化器

用于统一处理和标准化来自不同数据源的DataFrame数据
"""

import re
from typing import Dict, Any, List, Optional
import pandas as pd
from datetime import datetime


class DataStandardizer:
    """数据标准化器
    
    负责将来自不同数据源的DataFrame统一标准化，确保数据质量和一致性
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化数据标准化器
        
        Args:
            config: 标准化配置参数
        """
        self.config = config or {}
        
        # 默认配置
        self.max_content_length = self.config.get('max_content_length', 10000)
        self.min_content_length = self.config.get('min_content_length', 10)
        self.remove_duplicates = self.config.get('remove_duplicates', True)
        self.remove_empty = self.config.get('remove_empty', True)
        self.normalize_whitespace = self.config.get('normalize_whitespace', True)
        self.content_column = self.config.get('content_column', 'content')
        self.metadata_column = self.config.get('metadata_column', 'metadata')
    
    def standardize(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化DataFrame
        
        Args:
            df: 要标准化的DataFrame
            
        Returns:
            标准化后的DataFrame
        """
        if df.empty:
            return df
        
        print(f"开始标准化数据，原始数据量: {len(df)} 条")
        
        # 1. 验证DataFrame结构
        df = self._validate_dataframe_structure(df)
        
        # 2. 清理内容
        if self.normalize_whitespace:
            df = self._normalize_whitespace(df)
        
        # 3. 过滤无效数据
        if self.remove_empty:
            df = self._remove_empty_content(df)
        
        # 4. 内容长度过滤
        df = self._filter_by_content_length(df)
        
        # 5. 去除重复
        if self.remove_duplicates:
            df = self._remove_duplicates(df)
        
        # 6. 标准化元数据
        df = self._standardize_metadata(df)
        
        # 7. 添加标准化信息
        df = self._add_standardization_info(df)
        
        print(f"数据标准化完成，最终数据量: {len(df)} 条")
        
        return df
    
    def _validate_dataframe_structure(self, df: pd.DataFrame) -> pd.DataFrame:
        """验证和修复DataFrame结构"""
        # 确保必需的列存在
        if self.content_column not in df.columns:
            raise ValueError(f"DataFrame缺少必需的列: {self.content_column}")
        
        if self.metadata_column not in df.columns:
            df[self.metadata_column] = [{}] * len(df)
        
        # 确保内容列是字符串类型
        df[self.content_column] = df[self.content_column].astype(str)
        
        return df
    
    def _normalize_whitespace(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化空白字符"""
        def normalize_text(text):
            if not isinstance(text, str):
                return str(text)
            
            # 替换各种空白字符为标准空格
            text = re.sub(r'\s+', ' ', text)
            # 去除首尾空白
            text = text.strip()
            
            return text
        
        df[self.content_column] = df[self.content_column].apply(normalize_text)
        return df
    
    def _remove_empty_content(self, df: pd.DataFrame) -> pd.DataFrame:
        """移除空内容的行"""
        original_count = len(df)
        
        # 移除空字符串或只有空白字符的行
        df = df[df[self.content_column].str.strip() != '']
        
        removed_count = original_count - len(df)
        if removed_count > 0:
            print(f"移除了 {removed_count} 个空内容行")
        
        return df
    
    def _filter_by_content_length(self, df: pd.DataFrame) -> pd.DataFrame:
        """根据内容长度过滤数据"""
        original_count = len(df)
        
        # 计算内容长度
        content_lengths = df[self.content_column].str.len()
        
        # 过滤太短或太长的内容
        valid_mask = (content_lengths >= self.min_content_length) & (content_lengths <= self.max_content_length)
        df = df[valid_mask]
        
        filtered_count = original_count - len(df)
        if filtered_count > 0:
            print(f"根据长度过滤移除了 {filtered_count} 个条目")
            print(f"  - 最小长度限制: {self.min_content_length}")
            print(f"  - 最大长度限制: {self.max_content_length}")
        
        return df
    
    def _remove_duplicates(self, df: pd.DataFrame) -> pd.DataFrame:
        """移除重复内容"""
        original_count = len(df)
        
        # 基于内容去重（忽略大小写和空白差异）
        df['_normalized_content'] = df[self.content_column].str.lower().str.strip()
        df = df.drop_duplicates(subset=['_normalized_content'], keep='first')
        df = df.drop(columns=['_normalized_content'])
        
        removed_count = original_count - len(df)
        if removed_count > 0:
            print(f"移除了 {removed_count} 个重复内容")
        
        return df
    
    def _standardize_metadata(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化元数据格式"""
        def standardize_metadata_item(metadata):
            if not isinstance(metadata, dict):
                metadata = {}
            
            # 确保基本字段存在
            if 'content_length' not in metadata:
                metadata['content_length'] = 0
            
            if 'source_info' not in metadata:
                metadata['source_info'] = {}
            
            return metadata
        
        df[self.metadata_column] = df[self.metadata_column].apply(standardize_metadata_item)
        
        # 更新content_length字段
        for i, row in df.iterrows():
            content_length = len(str(row[self.content_column]))
            if isinstance(df.at[i, self.metadata_column], dict):
                df.at[i, self.metadata_column]['content_length'] = content_length
        
        return df
    
    def _add_standardization_info(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加标准化处理信息"""
        standardization_info = {
            'standardized_at': datetime.now().isoformat(),
            'standardizer_version': '1.0',
            'total_records': len(df),
            'quality_filters_applied': [
                'whitespace_normalization' if self.normalize_whitespace else None,
                'empty_content_removal' if self.remove_empty else None,
                'content_length_filtering',
                'duplicate_removal' if self.remove_duplicates else None
            ]
        }
        
        # 过滤掉None值
        standardization_info['quality_filters_applied'] = [
            f for f in standardization_info['quality_filters_applied'] if f is not None
        ]
        
        # 添加到每行的元数据中
        for i, row in df.iterrows():
            if isinstance(df.at[i, self.metadata_column], dict):
                df.at[i, self.metadata_column]['standardization_info'] = standardization_info
        
        return df
    
    def get_statistics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """获取数据统计信息"""
        if df.empty:
            return {"error": "DataFrame为空"}
        
        content_lengths = df[self.content_column].str.len()
        
        # 提取数据源类型统计
        source_types = []
        for _, row in df.iterrows():
            metadata = row[self.metadata_column]
            if isinstance(metadata, dict):
                source_type = metadata.get('source_type', 'unknown')
                extractor_type = metadata.get('extractor_type', 'unknown')
                source_types.append(f"{source_type}_{extractor_type}")
        
        source_type_counts = pd.Series(source_types).value_counts().to_dict()
        
        return {
            "total_records": len(df),
            "content_length_stats": {
                "min": int(content_lengths.min()),
                "max": int(content_lengths.max()),
                "mean": float(content_lengths.mean()),
                "median": float(content_lengths.median())
            },
            "source_type_distribution": source_type_counts,
            "columns": list(df.columns),
            "sample_metadata_keys": self._get_sample_metadata_keys(df)
        }
    
    def _get_sample_metadata_keys(self, df: pd.DataFrame) -> List[str]:
        """获取元数据字段示例"""
        all_keys = set()
        
        for _, row in df.head(5).iterrows():
            metadata = row[self.metadata_column]
            if isinstance(metadata, dict):
                all_keys.update(metadata.keys())
        
        return list(all_keys)
    
    def validate_standardized_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """验证标准化后的数据质量"""
        issues = []
        
        # 检查必需列
        if self.content_column not in df.columns:
            issues.append(f"缺少内容列: {self.content_column}")
        
        if self.metadata_column not in df.columns:
            issues.append(f"缺少元数据列: {self.metadata_column}")
        
        # 检查空内容
        if not df.empty:
            empty_content_count = (df[self.content_column].str.strip() == '').sum()
            if empty_content_count > 0:
                issues.append(f"存在 {empty_content_count} 个空内容记录")
        
        # 检查元数据格式
        invalid_metadata_count = 0
        for _, row in df.iterrows():
            if not isinstance(row[self.metadata_column], dict):
                invalid_metadata_count += 1
        
        if invalid_metadata_count > 0:
            issues.append(f"存在 {invalid_metadata_count} 个无效元数据格式")
        
        return {
            "is_valid": len(issues) == 0,
            "issues": issues,
            "total_records": len(df),
            "validation_timestamp": datetime.now().isoformat()
        } 