/* 全局样式 - 简约高级版 */
:root {
    /* 简约高级配色 */
    --primary-color: #2c5282;
    --secondary-color: #4c6ef5;
    --accent-color: #5a67d8;
    --success-color: #38a169;
    --warning-color: #dd6b20;
    --danger-color: #e53e3e;
    
    /* 简约渐变 */
    --primary-gradient: linear-gradient(to right, #2c5282, #3182ce);
    --secondary-gradient: linear-gradient(to right, #4c6ef5, #6b8cff);
    --success-gradient: linear-gradient(to right, #38a169, #48bb78);
    
    /* 中性色 */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* 阴影系统 */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* 边框半径 */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-full: 9999px;
    
    /* 动画时长 */
    --duration-fast: 0.15s;
    --duration-normal: 0.3s;
    --duration-slow: 0.5s;
    
    /* 间距系统 */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-12: 3rem;
    --space-16: 4rem;
    
    /* 排版系统 */
    --font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

/* 全局样式重置 */
*, *::before, *::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    color: var(--gray-800);
    line-height: 1.6;
    background-color: var(--gray-50);
    min-height: 100vh;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--duration-normal) ease;
}

a:hover {
    color: var(--secondary-color);
}

img {
    max-width: 100%;
    height: auto;
}

/* 按钮基础样式 */
.btn {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.25rem;
    font-size: 0.875rem;
    font-weight: 600;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--duration-normal) ease;
    border: none;
    line-height: 1;
    white-space: nowrap;
    text-decoration: none;
    overflow: hidden;
}

/* 主要按钮样式 */
.btn-primary {
    background: var(--primary-color);
    color: var(--white);
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* 次要按钮样式 */
.btn-secondary {
    background: var(--white);
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--gray-100);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* 轮廓按钮样式 */
.btn-outline {
    background: transparent;
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.btn-outline:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

/* 成功按钮样式 */
.btn-success {
    background: var(--success-color);
    color: var(--white);
}

.btn-success:hover {
    background: var(--success-gradient);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* 危险按钮样式 */
.btn-danger {
    background: var(--danger-color);
    color: var(--white);
}

.btn-danger:hover {
    opacity: 0.9;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* 警告按钮样式 */
.btn-warning {
    background: var(--warning-color);
    color: var(--white);
}

.btn-warning:hover {
    opacity: 0.9;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* 链接按钮样式 */
.btn-link {
    background: transparent;
    color: var(--primary-color);
    padding: 0.5rem 0.75rem;
    text-decoration: none;
    box-shadow: none;
}

.btn-link:hover {
    text-decoration: underline;
    color: var(--secondary-color);
}

/* 图标按钮 */
.btn-icon {
    width: 38px;
    height: 38px;
    padding: 0;
    border-radius: var(--radius-md);
}

/* 按钮尺寸 */
.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
}

.btn-lg {
    padding: 1rem 1.5rem;
    font-size: 1rem;
}

/* 按钮状态 */
.btn:disabled {
    opacity: 0.65;
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
}

/* 徽章样式 */
.badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    line-height: 1;
    border-radius: var(--radius-full);
    text-transform: capitalize;
}

.badge-primary {
    background: var(--primary-color);
    color: var(--white);
}

.badge-secondary {
    background: var(--gray-200);
    color: var(--gray-800);
}

.badge-success {
    background: var(--success-color);
    color: var(--white);
}

.badge-danger {
    background: var(--danger-color);
    color: var(--white);
}

.badge-warning {
    background: var(--warning-color);
    color: var(--white);
}

/* 卡片样式 */
.card {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: all var(--duration-normal) ease;
    border: 1px solid var(--gray-200);
}

.card-header {
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    padding: 1.25rem 1.5rem;
    border-top: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

/* 表单样式 */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--gray-700);
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: 0.95rem;
    color: var(--gray-800);
    background-color: var(--white);
    transition: all var(--duration-normal) ease;
    font-family: var(--font-family);
}

.form-control::placeholder {
    color: var(--gray-400);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(44, 82, 130, 0.1);
}

.form-text {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.75rem;
    color: var(--gray-500);
}

.form-check {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
}

.form-check-input {
    width: 1rem;
    height: 1rem;
    margin-right: 0.5rem;
    cursor: pointer;
    accent-color: var(--primary-color);
}

.form-check-label {
    font-size: 0.875rem;
    color: var(--gray-700);
    cursor: pointer;
    user-select: none;
}

/* 提示框样式 */
.alert {
    padding: 1rem 1.25rem;
    border-radius: var(--radius-md);
    margin-bottom: 1rem;
    border-left: 4px solid transparent;
}

.alert-primary {
    background-color: rgba(76, 110, 245, 0.1);
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.alert-success {
    background-color: rgba(56, 161, 105, 0.1);
    color: var(--success-color);
    border-color: var(--success-color);
}

.alert-danger {
    background-color: rgba(229, 62, 62, 0.1);
    color: var(--danger-color);
    border-color: var(--danger-color);
}

.alert-warning {
    background-color: rgba(221, 107, 32, 0.1);
    color: var(--warning-color);
    border-color: var(--warning-color);
}

/* 实用工具类 */
.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.text-left {
    text-align: left;
}

.d-flex {
    display: flex;
}

.flex-column {
    flex-direction: column;
}

.align-items-center {
    align-items: center;
}

.justify-content-between {
    justify-content: space-between;
}

.justify-content-center {
    justify-content: center;
}

.gap-1 {
    gap: 0.25rem;
}

.gap-2 {
    gap: 0.5rem;
}

.gap-3 {
    gap: 0.75rem;
}

.gap-4 {
    gap: 1rem;
}

.mb-1 {
    margin-bottom: 0.25rem;
}

.mb-2 {
    margin-bottom: 0.5rem;
}

.mb-3 {
    margin-bottom: 0.75rem;
}

.mb-4 {
    margin-bottom: 1rem;
}

.mt-1 {
    margin-top: 0.25rem;
}

.mt-2 {
    margin-top: 0.5rem;
}

.mt-3 {
    margin-top: 0.75rem;
}

.mt-4 {
    margin-top: 1rem;
}

.w-100 {
    width: 100%;
}

.h-100 {
    height: 100%;
}

.text-primary {
    color: var(--primary-color);
    }

.text-secondary {
    color: var(--secondary-color);
    }

.text-success {
    color: var(--success-color);
}

.text-danger {
    color: var(--danger-color);
}

.text-warning {
    color: var(--warning-color);
}

.text-muted {
    color: var(--gray-500);
}

.font-weight-bold {
    font-weight: 700;
}

.font-weight-normal {
    font-weight: 400;
}

.font-weight-light {
    font-weight: 300;
}

/* 动画类 */
.fade-in {
    animation: fadeIn var(--duration-normal) ease;
    }
    
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.slide-up {
    animation: slideUp var(--duration-normal) ease;
    }
    
@keyframes slideUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* 响应式工具类 */
@media (max-width: 768px) {
    .hide-md {
        display: none !important;
    }
    
    .text-center-md {
        text-align: center !important;
    }
}

@media (max-width: 480px) {
    .hide-sm {
        display: none !important;
    }
    
    .text-center-sm {
        text-align: center !important;
    }
    
    html {
        font-size: 14px;
    }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 5px;
    height: 5px;
    }
    
::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: var(--radius-full);
    transition: background var(--duration-normal) ease;
    }

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
} 

/* 登录成功弹窗样式 */
.custom-modal {
    display: none;
    position: fixed;
    z-index: 99999;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0,0,0,0.5);
    justify-content: center;
    align-items: center;
}
.custom-modal.show {
    display: flex !important;
}
.custom-modal-content {
    background: #fff;
    padding: 2rem 3rem;
    border-radius: 1rem;
    box-shadow: 0 8px 32px rgba(44,82,130,0.15);
    font-size: 1.3rem;
    color: #1976d2;
    font-weight: 600;
    text-align: center;
    min-width: 200px;
    animation: fadeIn 0.3s;
}

.custom-modal-btn {
    margin-top: 1.2rem;
    padding: 0.5rem 2.2rem;
    background: var(--primary-color);
    color: #fff;
    border: none;
    border-radius: 0.5rem;
    font-size: 1rem;
    cursor: pointer;
    transition: background 0.2s;
}
.custom-modal-btn:hover {
    background: var(--secondary-color);
}

