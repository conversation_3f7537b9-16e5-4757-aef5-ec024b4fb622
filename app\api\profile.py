from flask import Blueprint, request, jsonify
import logging
from app.models.database import db
from app.models.user import UserProfile
from app.models.token import Token

# 设置日志记录器
logger = logging.getLogger(__name__)

profile_bp = Blueprint('profile', __name__)

def get_user_id_from_token():
    """从请求头中获取用户ID"""
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return None

    token_str = auth_header.split(' ')[1]
    return Token.get_user_id_by_token(token_str)

@profile_bp.route('/api/profile', methods=['GET'])
def get_profile():
    """获取用户个人资料"""
    try:
        user_id = get_user_id_from_token()
        if not user_id:
            return jsonify({
                'success': False,
                'message': '未提供有效的身份验证令牌'
            }), 401

        profile = UserProfile.query.filter_by(user_id=user_id).first()
        if profile:
            return jsonify({
                'success': True,
                'data': profile.to_dict()
            })
        else:
            return jsonify({
                'success': True,
                'data': None,
                'message': '未找到个人资料'
            })
    except Exception as e:
        logger.error(f"获取个人资料错误: {str(e)}")
        return jsonify({
            'success': False,
            'message': '服务器错误'
        }), 500

@profile_bp.route('/api/profile', methods=['POST'])
def update_profile():
    """更新用户个人资料"""
    try:
        user_id = get_user_id_from_token()
        if not user_id:
            return jsonify({
                'success': False,
                'message': '未提供有效的身份验证令牌'
            }), 401

        data = request.json
        if not data:
            return jsonify({
                'success': False,
                'message': '请提供要更新的数据'
            }), 400

        # 查找现有的个人资料
        profile = UserProfile.query.filter_by(user_id=user_id).first()

        if profile:
            # 更新现有资料
            for key, value in data.items():
                if hasattr(profile, key):
                    setattr(profile, key, value)
            logger.info(f"更新用户 {user_id} 的个人资料")
        else:
            # 创建新的个人资料
            profile = UserProfile(user_id=user_id, **data)
            db.session.add(profile)
            logger.info(f"为用户 {user_id} 创建新的个人资料")

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '保存成功',
            'data': profile.to_dict()
        })

    except Exception as e:
        logger.error(f"更新个人资料错误: {str(e)}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': '保存失败，请稍后再试'
        }), 500