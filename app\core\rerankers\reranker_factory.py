"""重排器工厂和管理

提供重排器的创建、管理和配置功能
"""

from typing import Dict, Any, Optional, Type, Union
from enum import Enum

from .base_reranker import BaseReranker
from .cross_encoder_reranker import CrossEncoderReranker


class RerankerType(Enum):
    """重排器类型枚举"""
    CROSS_ENCODER = "cross_encoder"
    BGE_RERANKER = "bge_reranker"
    SENTENCE_TRANSFORMER = "sentence_transformer"
    CUSTOM = "custom"


class RerankerFactory:
    """重排器工厂类
    
    负责创建和管理不同类型的重排器实例
    """
    
    # 注册的重排器类型
    _reranker_registry: Dict[str, Type[BaseReranker]] = {
        RerankerType.CROSS_ENCODER.value: CrossEncoderReranker,
        RerankerType.BGE_RERANKER.value: CrossEncoderReranker,  # BGE使用CrossEncoder实现
        RerankerType.SENTENCE_TRANSFORMER.value: CrossEncoderReranker,
    }
    
    # 默认模型配置
    _default_models = {
        RerankerType.BGE_RERANKER.value: {
            'model_name': 'BAAI/bge-reranker-base',
            'max_length': 512,
            'normalize_scores': True
        },
        RerankerType.CROSS_ENCODER.value: {
            'model_name': 'cross-encoder/ms-marco-MiniLM-L-6-v2',
            'max_length': 512,
            'normalize_scores': True
        },
        RerankerType.SENTENCE_TRANSFORMER.value: {
            'model_name': 'sentence-transformers/all-MiniLM-L6-v2',
            'max_length': 384,
            'normalize_scores': True
        }
    }
    
    @classmethod
    def create_reranker(
        cls,
        reranker_type: Union[str, RerankerType],
        config: Optional[Dict[str, Any]] = None
    ) -> BaseReranker:
        """创建重排器实例
        
        Args:
            reranker_type: 重排器类型
            config: 重排器配置
            
        Returns:
            重排器实例
            
        Raises:
            ValueError: 不支持的重排器类型
        """
        # 标准化重排器类型
        if isinstance(reranker_type, RerankerType):
            reranker_type = reranker_type.value
        
        if reranker_type not in cls._reranker_registry:
            raise ValueError(f"不支持的重排器类型: {reranker_type}")
        
        # 合并默认配置和用户配置
        final_config = cls._get_merged_config(reranker_type, config)
        
        # 创建重排器实例
        reranker_class = cls._reranker_registry[reranker_type]
        reranker = reranker_class(final_config)
        
        print(f"✅ 创建重排器: {reranker_type}")
        return reranker
    
    @classmethod
    def _get_merged_config(
        cls,
        reranker_type: str,
        user_config: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """合并默认配置和用户配置
        
        Args:
            reranker_type: 重排器类型
            user_config: 用户配置
            
        Returns:
            合并后的配置
        """
        # 获取默认配置
        default_config = cls._default_models.get(reranker_type, {}).copy()
        
        # 合并用户配置
        if user_config:
            default_config.update(user_config)
        
        # 添加重排器类型信息
        default_config['reranker_type'] = reranker_type
        
        return default_config
    
    @classmethod
    def register_reranker(
        cls,
        reranker_type: str,
        reranker_class: Type[BaseReranker],
        default_config: Optional[Dict[str, Any]] = None
    ):
        """注册新的重排器类型
        
        Args:
            reranker_type: 重排器类型名称
            reranker_class: 重排器类
            default_config: 默认配置
        """
        cls._reranker_registry[reranker_type] = reranker_class
        
        if default_config:
            cls._default_models[reranker_type] = default_config
        
        print(f"✅ 注册重排器类型: {reranker_type}")
    
    @classmethod
    def get_available_rerankers(cls) -> Dict[str, Dict[str, Any]]:
        """获取可用的重排器类型和配置
        
        Returns:
            重排器类型和配置信息
        """
        return {
            reranker_type: {
                'class': reranker_class.__name__,
                'default_config': cls._default_models.get(reranker_type, {})
            }
            for reranker_type, reranker_class in cls._reranker_registry.items()
        }
    
    @classmethod
    def create_bge_reranker(cls, config: Optional[Dict[str, Any]] = None) -> BaseReranker:
        """创建BGE重排器的便捷方法
        
        Args:
            config: 配置参数
            
        Returns:
            BGE重排器实例
        """
        bge_config = {
            'model_name': 'BAAI/bge-reranker-base',
            'top_n': 20,
            'batch_size': 32,
            'device': 'cpu',
            'max_length': 512,
            'normalize_scores': True
        }
        
        if config:
            bge_config.update(config)
        
        return cls.create_reranker(RerankerType.BGE_RERANKER, bge_config)
    
    @classmethod
    def create_lightweight_reranker(cls, config: Optional[Dict[str, Any]] = None) -> BaseReranker:
        """创建轻量级重排器的便捷方法
        
        Args:
            config: 配置参数
            
        Returns:
            轻量级重排器实例
        """
        lightweight_config = {
            'model_name': 'cross-encoder/ms-marco-MiniLM-L-6-v2',
            'top_n': 20,
            'batch_size': 64,
            'device': 'cpu',
            'max_length': 256,
            'normalize_scores': True
        }
        
        if config:
            lightweight_config.update(config)
        
        return cls.create_reranker(RerankerType.CROSS_ENCODER, lightweight_config)


class RerankerManager:
    """重排器管理器
    
    管理重排器实例的生命周期和缓存
    """
    
    def __init__(self):
        self._reranker_cache: Dict[str, BaseReranker] = {}
        self._usage_stats: Dict[str, Dict[str, Any]] = {}
    
    def get_reranker(
        self,
        reranker_type: Union[str, RerankerType],
        config: Optional[Dict[str, Any]] = None,
        use_cache: bool = True
    ) -> BaseReranker:
        """获取重排器实例
        
        Args:
            reranker_type: 重排器类型
            config: 配置参数
            use_cache: 是否使用缓存
            
        Returns:
            重排器实例
        """
        # 生成缓存键
        cache_key = self._generate_cache_key(reranker_type, config)
        
        # 检查缓存
        if use_cache and cache_key in self._reranker_cache:
            reranker = self._reranker_cache[cache_key]
            self._update_usage_stats(cache_key, from_cache=True)
            return reranker
        
        # 创建新实例
        reranker = RerankerFactory.create_reranker(reranker_type, config)
        
        # 缓存实例
        if use_cache:
            self._reranker_cache[cache_key] = reranker
        
        # 更新统计
        self._update_usage_stats(cache_key, from_cache=False)
        
        return reranker
    
    def _generate_cache_key(
        self,
        reranker_type: Union[str, RerankerType],
        config: Optional[Dict[str, Any]]
    ) -> str:
        """生成缓存键
        
        Args:
            reranker_type: 重排器类型
            config: 配置参数
            
        Returns:
            缓存键
        """
        if isinstance(reranker_type, RerankerType):
            reranker_type = reranker_type.value
        
        # 使用重要配置参数生成键
        key_parts = [reranker_type]
        
        if config:
            important_keys = ['model_name', 'device', 'max_length', 'top_n']
            for key in important_keys:
                if key in config:
                    key_parts.append(f"{key}={config[key]}")
        
        return "_".join(key_parts)
    
    def _update_usage_stats(self, cache_key: str, from_cache: bool):
        """更新使用统计
        
        Args:
            cache_key: 缓存键
            from_cache: 是否来自缓存
        """
        if cache_key not in self._usage_stats:
            self._usage_stats[cache_key] = {
                'total_requests': 0,
                'cache_hits': 0,
                'cache_misses': 0
            }
        
        stats = self._usage_stats[cache_key]
        stats['total_requests'] += 1
        
        if from_cache:
            stats['cache_hits'] += 1
        else:
            stats['cache_misses'] += 1
    
    def get_usage_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取使用统计
        
        Returns:
            使用统计信息
        """
        return self._usage_stats.copy()
    
    def clear_cache(self):
        """清空缓存"""
        self._reranker_cache.clear()
        print("✅ 重排器缓存已清空")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息
        
        Returns:
            缓存信息
        """
        return {
            'cached_rerankers': len(self._reranker_cache),
            'cache_keys': list(self._reranker_cache.keys()),
            'usage_stats': self._usage_stats
        }


# 全局重排器管理器实例
_global_reranker_manager = RerankerManager()


def create_reranker(
    reranker_type: Union[str, RerankerType],
    config: Optional[Dict[str, Any]] = None
) -> BaseReranker:
    """创建重排器的便捷函数
    
    Args:
        reranker_type: 重排器类型
        config: 配置参数
        
    Returns:
        重排器实例
    """
    return RerankerFactory.create_reranker(reranker_type, config)


def get_reranker(
    reranker_type: Union[str, RerankerType],
    config: Optional[Dict[str, Any]] = None,
    use_cache: bool = True
) -> BaseReranker:
    """获取重排器实例的便捷函数
    
    Args:
        reranker_type: 重排器类型
        config: 配置参数
        use_cache: 是否使用缓存
        
    Returns:
        重排器实例
    """
    return _global_reranker_manager.get_reranker(reranker_type, config, use_cache)
