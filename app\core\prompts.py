from langchain_core.prompts import PromptTemplate, ChatPromptTemplate, MessagesPlaceholder

REPHRASE_QUESTION_PROMPT_TEMPLATE = """
以下是一段对话和一个后续问题，请将该后续问题改写为一个独立的问题，保持其原有语言不变。

对话历史:
{chat_history}

后续问题: {input}
独立问题:"""
rephrase_question_prompt = ChatPromptTemplate.from_messages([
    ("system", REPHRASE_QUESTION_PROMPT_TEMPLATE),
    MessagesPlaceholder(variable_name="chat_history"),
    ("human", "{input}"),
])

RAG_ANSWER_PROMPT_TEMPLATE = """
你是一个擅长问答任务的专家助手。
请利用以下检索到的上下文来回答问题。
如果你不知道答案，就直接说你不知道。
请保持回答简洁。

问题: {input} 
上下文: {context} 
回答:"""
rag_answer_prompt = ChatPromptTemplate.from_messages([
    ("system", RAG_ANSWER_PROMPT_TEMPLATE),
    MessagesPlaceholder(variable_name="chat_history"),
    ("human", "{input}"),
])

AGENT_SYSTEM_PROMPT = (
    "你是一个能够使用一系列工具的有用助手。"
    "请使用提供的工具来回答用户的问题。"
    "如果无法使用工具回答，请如实说明。"
)
agent_prompt = ChatPromptTemplate.from_messages([
    ("system", AGENT_SYSTEM_PROMPT),
    MessagesPlaceholder(variable_name="chat_history", optional=True),
    ("human", "{input}"),
    MessagesPlaceholder(variable_name="agent_scratchpad"),
])

SIMPLE_CHAT_PROMPT = ChatPromptTemplate.from_messages([
    ("system", "你是一个乐于助人且友好的对话型 AI。"),
    MessagesPlaceholder(variable_name="chat_history"),
    ("human", "{input}"),
])