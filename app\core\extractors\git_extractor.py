"""Git仓库数据抽取器"""

import os
import shutil
import glob
from pathlib import Path
from typing import Dict, Any, List
import pandas as pd

try:
    import git
except ImportError:
    git = None

from .base_extractor import BaseExtractor


class GitExtractor(BaseExtractor):
    """Git仓库数据抽取器
    
    从Git仓库中克隆并抽取文件内容
    """
    
    def extract(self) -> pd.DataFrame:
        """从Git仓库抽取数据"""
        if git is None:
            raise ImportError("需要安装 GitPython 来操作Git仓库")
        
        self.validate_config(['repository_url', 'local_path'])
        
        repo_url = self.config['repository_url']
        local_path = self.config['local_path']
        branch = self.config.get('branch', 'main')
        file_patterns = self.config.get('file_patterns', ['*.md', '*.txt'])
        clone_depth = self.config.get('clone_depth', 1)
        
        data = []
        temp_repo_path = None
        
        try:
            # 克隆或更新仓库
            temp_repo_path = self._clone_or_update_repo(
                repo_url, local_path, branch, clone_depth
            )
            
            if temp_repo_path is None:
                print("无法克隆或访问Git仓库")
                return self._create_standardized_dataframe([])
            
            # 从仓库中提取文件
            data = self._extract_files_from_repo(temp_repo_path, file_patterns, repo_url)
            
        except Exception as e:
            print(f"处理Git仓库时出错: {str(e)}")
        finally:
            # 清理临时文件（如果配置了清理）
            if self.config.get('cleanup_after_extraction', False) and temp_repo_path:
                self._cleanup_repo(temp_repo_path)
        
        print(f"从Git仓库成功提取 {len(data)} 个文件")
        return self._create_standardized_dataframe(data)
    
    def _clone_or_update_repo(self, repo_url: str, local_path: str, 
                             branch: str, clone_depth: int) -> str:
        """克隆或更新Git仓库"""
        try:
            # 确保本地路径存在
            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            
            if os.path.exists(local_path) and os.path.isdir(local_path):
                # 检查是否是有效的Git仓库
                try:
                    repo = git.Repo(local_path)
                    # 尝试更新现有仓库
                    print(f"更新现有仓库: {local_path}")
                    origin = repo.remotes.origin
                    origin.fetch()
                    
                    # 切换到指定分支
                    if branch in repo.heads:
                        repo.heads[branch].checkout()
                        origin.pull()
                    else:
                        print(f"分支 {branch} 不存在，使用当前分支")
                    
                    return local_path
                    
                except git.exc.InvalidGitRepositoryError:
                    # 不是有效的Git仓库，删除并重新克隆
                    print(f"删除无效的仓库目录: {local_path}")
                    shutil.rmtree(local_path)
            
            # 克隆新仓库
            print(f"克隆Git仓库: {repo_url} -> {local_path}")
            
            clone_kwargs = {
                'depth': clone_depth,
                'branch': branch
            }
            
            # 移除depth参数如果值为None或0
            if not clone_depth:
                del clone_kwargs['depth']
            
            repo = git.Repo.clone_from(repo_url, local_path, **clone_kwargs)
            return local_path
            
        except Exception as e:
            print(f"克隆Git仓库失败: {str(e)}")
            return None
    
    def _extract_files_from_repo(self, repo_path: str, file_patterns: List[str], 
                                repo_url: str) -> List[Dict[str, Any]]:
        """从Git仓库中提取文件内容"""
        data = []
        
        # 获取仓库信息
        try:
            repo = git.Repo(repo_path)
            current_commit = repo.head.commit
            commit_info = {
                'commit_hash': current_commit.hexsha,
                'commit_message': current_commit.message.strip(),
                'commit_author': str(current_commit.author),
                'commit_date': current_commit.committed_datetime.isoformat()
            }
        except:
            commit_info = {}
        
        # 收集所有匹配的文件
        all_files = []
        for pattern in file_patterns:
            pattern_path = os.path.join(repo_path, '**', pattern)
            files = glob.glob(pattern_path, recursive=True)
            all_files.extend(files)
        
        print(f"在Git仓库中找到 {len(all_files)} 个匹配文件")
        
        # 处理每个文件
        for file_path in all_files:
            try:
                content = self._read_file_content(file_path)
                if content:
                    relative_path = os.path.relpath(file_path, repo_path)
                    
                    # 获取文件的Git历史信息
                    file_git_info = self._get_file_git_info(repo_path, relative_path)
                    
                    file_metadata = {
                        'repository_url': repo_url,
                        'file_path': file_path,
                        'relative_path': relative_path,
                        'file_name': os.path.basename(file_path),
                        'file_extension': os.path.splitext(file_path)[1],
                        'file_size': os.path.getsize(file_path),
                        **commit_info,
                        **file_git_info
                    }
                    
                    data.append({
                        'content': self._clean_content(content),
                        'metadata': file_metadata
                    })
                    
            except Exception as e:
                print(f"处理文件 {file_path} 时出错: {str(e)}")
                continue
        
        return data
    
    def _read_file_content(self, file_path: str) -> str:
        """读取文件内容"""
        try:
            # 尝试多种编码读取文件
            encodings = ['utf-8', 'gbk', 'gb2312', 'latin1']
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        return f.read()
                except UnicodeDecodeError:
                    continue
            
            # 如果所有编码都失败，尝试二进制读取并检测编码
            try:
                import chardet
                with open(file_path, 'rb') as f:
                    raw_data = f.read()
                    detected = chardet.detect(raw_data)
                    encoding = detected.get('encoding', 'utf-8')
                
                with open(file_path, 'r', encoding=encoding) as f:
                    return f.read()
            except:
                pass
            
            print(f"无法读取文件 {file_path}，跳过")
            return ""
            
        except Exception as e:
            print(f"读取文件 {file_path} 失败: {str(e)}")
            return ""
    
    def _get_file_git_info(self, repo_path: str, relative_path: str) -> Dict[str, Any]:
        """获取文件的Git历史信息"""
        try:
            repo = git.Repo(repo_path)
            
            # 获取文件的最后修改信息
            commits = list(repo.iter_commits(paths=relative_path, max_count=1))
            
            if commits:
                last_commit = commits[0]
                return {
                    'last_modified_commit': last_commit.hexsha,
                    'last_modified_author': str(last_commit.author),
                    'last_modified_date': last_commit.committed_datetime.isoformat(),
                    'last_modified_message': last_commit.message.strip()
                }
            else:
                return {
                    'last_modified_commit': 'unknown',
                    'last_modified_author': 'unknown',
                    'last_modified_date': 'unknown',
                    'last_modified_message': 'unknown'
                }
                
        except Exception as e:
            print(f"获取文件 {relative_path} 的Git信息失败: {str(e)}")
            return {}
    
    def _cleanup_repo(self, repo_path: str) -> None:
        """清理仓库目录"""
        try:
            if os.path.exists(repo_path):
                shutil.rmtree(repo_path)
                print(f"已清理仓库目录: {repo_path}")
        except Exception as e:
            print(f"清理仓库目录失败: {str(e)}")
    
    def get_repo_info(self, repo_path: str) -> Dict[str, Any]:
        """获取仓库基本信息（用于调试）"""
        try:
            if not os.path.exists(repo_path):
                return {"error": "仓库路径不存在"}
            
            repo = git.Repo(repo_path)
            
            return {
                "current_branch": repo.active_branch.name,
                "remote_url": repo.remotes.origin.url,
                "total_commits": len(list(repo.iter_commits())),
                "last_commit": {
                    "hash": repo.head.commit.hexsha,
                    "message": repo.head.commit.message.strip(),
                    "author": str(repo.head.commit.author),
                    "date": repo.head.commit.committed_datetime.isoformat()
                }
            }
            
        except Exception as e:
            return {"error": str(e)} 