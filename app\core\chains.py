from typing import List, Optional, Dict, Any
from langchain.chains import create_history_aware_retriever, create_retrieval_chain, <PERSON><PERSON><PERSON><PERSON>
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain.agents import AgentExecutor, create_tool_calling_agent
from langchain.memory import ConversationBufferWindowMemory
from langchain_core.chat_history import BaseChatMessageHistory
from app.core.factories import create_llm_instance, get_tools
from app.core.prompts import (
    agent_prompt,
    rephrase_question_prompt,
    rag_answer_prompt,
    SIMPLE_CHAT_PROMPT
)
from app.core.vector_store import get_vector_store
from app.core.retrievers import create_ensemble_retriever
from app.configs.settings import config

def get_rag_chain(use_hybrid_search: Optional[bool] = None, search_config: Optional[Dict[str, Any]] = None):
    """获取RAG链，默认使用配置文件中的混合搜索设置

    Args:
        use_hybrid_search: 是否使用混合搜索，如果为None则使用配置文件设置
        search_config: 搜索配置，包含权重、RRF参数等，如果为None则使用配置文件设置

    Returns:
        配置好的RAG链
    """
    llm = create_llm_instance()

    # 从配置文件获取默认设置
    if use_hybrid_search is None:
        use_hybrid_search = config.hybrid_search.enabled

    if use_hybrid_search:
        print("🔄 创建混合搜索RAG链")
        # 使用混合检索器
        if search_config is None:
            # 使用配置文件中的设置
            search_config = {
                'vector_weight': config.hybrid_search.weights.vector,
                'keyword_weight': config.hybrid_search.weights.keyword,
                'rrf_k': config.hybrid_search.rrf.k,
                'top_k': config.hybrid_search.results.top_k,
                'search_engine_config': {
                    'type': config.hybrid_search.search_engine.type,
                    'simple': config.hybrid_search.search_engine.simple.model_dump(),
                    'elasticsearch': config.hybrid_search.search_engine.elasticsearch.model_dump()
                },
                'reranker_config': {
                    'type': config.retrieval.reranker.type,
                    'model': config.retrieval.reranker.model.model_dump(),
                    'parameters': config.retrieval.reranker.parameters.model_dump(),
                    'performance': config.retrieval.reranker.performance.model_dump()
                },
                'enable_reranking': config.retrieval.reranker.enabled
            }

        retriever = create_ensemble_retriever(
            vector_weight=search_config.get('vector_weight', 0.5),
            keyword_weight=search_config.get('keyword_weight', 0.5),
            rrf_k=search_config.get('rrf_k', 60),
            top_k=search_config.get('top_k', 10),
            search_engine_config=search_config.get('search_engine_config'),
            reranker_config=search_config.get('reranker_config'),
            enable_reranking=search_config.get('enable_reranking', False)
        )
    else:
        print("🔄 创建传统向量搜索RAG链")
        # 使用传统向量检索器
        vector_store = get_vector_store()
        retriever = vector_store.as_retriever()

    history_aware_retriever = create_history_aware_retriever(
        llm, retriever, rephrase_question_prompt
    )
    question_answer_chain = create_stuff_documents_chain(llm, rag_answer_prompt)
    rag_chain = create_retrieval_chain(history_aware_retriever, question_answer_chain)
    return rag_chain


def get_vector_rag_chain():
    """获取传统向量搜索RAG链（向后兼容）"""
    return get_rag_chain(use_hybrid_search=False)


def get_hybrid_rag_chain(search_config: Optional[Dict[str, Any]] = None):
    """获取混合搜索RAG链"""
    return get_rag_chain(use_hybrid_search=True, search_config=search_config)

def get_conversation_chain(
    tool_names: List[str],
    chat_history_backend: Optional[BaseChatMessageHistory] = None,
    memory_window_size: int = 5
):
    llm = create_llm_instance()
    tools = get_tools(tool_names)

    memory = ConversationBufferWindowMemory(
        k=memory_window_size,
        memory_key="chat_history",
        chat_memory=chat_history_backend,
        return_messages=True,
    )

    if tools:
        print("--- Creating Agent chain with tools ---")
        prompt = agent_prompt
        agent = create_tool_calling_agent(llm, tools, prompt)
        return AgentExecutor(
            agent=agent,
            tools=tools,
            memory=memory,
            verbose=True
        )
    else:
        print("--- No tools provided, creating simple LLM chain ---")
        prompt = SIMPLE_CHAT_PROMPT
        return LLMChain(
            llm=llm,
            prompt=prompt,
            memory=memory,
            verbose=True
        )