<div class="main-content">
    <div class="chat-messages" id="chatMessages">
        <div class="empty-state-message">
            请从左侧选择一位患者开始问诊
        </div>
    </div>

    <!-- AI诊断报告 -->
    <div class="ai-diagnosis" id="aiDiagnosis" style="display: none;">
        <div class="section-header">
            <h2>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                AI智能诊断
            </h2>
            <span class="diagnosis-confidence">置信度: <strong>85%</strong></span>
        </div>
        <div class="diagnosis-content" id="diagnosisContent">
            <!-- AI诊断内容将通过JavaScript加载 -->
        </div>
    </div>

    <!-- 处方开具 -->
    <div class="prescription" id="prescriptionSection" style="display: none;">
        <div class="section-header">
            <h2>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M11 4H4a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2v-7"></path>
                    <path d="M18.5 2.5a2.121 2.121 0 013 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                </svg>
                电子处方
            </h2>
        </div>
        <div class="prescription-form">
            <!-- 处方表单内容 -->
            <div class="form-group">
                <label>药品名称</label>
                <input type="text" placeholder="请输入药品名称">
            </div>
            <div class="form-group">
                <label>用法用量</label>
                <textarea placeholder="请输入用法用量说明"></textarea>
            </div>
            <div class="form-actions">
                <button class="btn btn-primary">保存处方</button>
                <button class="btn btn-secondary">取消</button>
            </div>
        </div>
    </div>
    
    <!-- 加入会话按钮 -->
    <div class="join-session-area" id="joinSessionArea" style="display: none;">
        <button class="join-session-btn" id="joinSessionBtn">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                <circle cx="8.5" cy="7" r="4"></circle>
                <line x1="20" y1="8" x2="20" y2="14"></line>
                <line x1="23" y1="11" x2="17" y2="11"></line>
            </svg>
            加入患者会话
        </button>
    </div>
    
    <!-- 加入会话对话框 -->
    <div class="join-session-modal" id="joinSessionModal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>加入患者会话</h3>
                <button id="closeJoinModalBtn" class="close-modal">&times;</button>
            </div>
            <div class="modal-body">
                <p>您即将加入 <span id="joinPatientName">患者</span> 的会话。</p>
                <p>选择一条加入消息或输入自定义消息：</p>
                <div class="join-message-options">
                    <div class="join-message-option" data-message="您好，我是主治医师，我将接手您的问诊。">
                        您好，我是主治医师，我将接手您的问诊。
                    </div>
                    <div class="join-message-option" data-message="您好，我是值班医生，有什么可以帮助您的吗？">
                        您好，我是值班医生，有什么可以帮助您的吗？
                    </div>
                    <div class="join-message-option" data-message="您好，我已查看了您的症状描述，现在为您进行专业解答。">
                        您好，我已查看了您的症状描述，现在为您进行专业解答。
                    </div>
                </div>
                <textarea id="customJoinMessage" placeholder="或者输入自定义消息..."></textarea>
            </div>
            <div class="modal-footer">
                <button id="cancelJoinBtn" class="btn btn-secondary">取消</button>
                <button id="confirmJoinBtn" class="btn btn-primary">确认加入</button>
            </div>
        </div>
    </div>

    <!-- 消息输入区域 -->
    <div class="message-input-area" id="messageInputArea" style="display: none;">
        <div class="input-container">
            <textarea class="doctor-message-input" id="doctorMessageInput" 
                     placeholder="输入回复消息..." rows="2"></textarea>
            <button class="send-message-btn" id="sendMessageBtn" disabled>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="22" y1="2" x2="11" y2="13"></line>
                    <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                </svg>
            </button>
        </div>
    </div>
</div> 
