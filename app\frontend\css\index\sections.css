/* 各个内容区域样式 */

/* 数据统计区域 */
.stats-section {
    padding: 4rem 0;
    background: var(--white);
    margin-top: -2rem;
    position: relative;
    z-index: 3;
    border-radius: 2rem 2rem 0 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 2.5rem 2rem;
    background: var(--white);
    border-radius: var(--radius-xl);
    border: 1px solid var(--gray-200);
    transition: all var(--duration-normal) ease;
    box-shadow: var(--shadow-sm);
}

.stat-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.stat-icon {
    width: 70px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-gradient);
    border-radius: var(--radius-xl);
    color: var(--white);
    flex-shrink: 0;
    position: relative;
    transition: all var(--duration-normal) ease;
    box-shadow: var(--shadow-md);
}

.stat-icon::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: var(--radius-xl);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%);
    pointer-events: none;
}

.stat-icon svg {
    width: 32px;
    height: 32px;
    z-index: 1;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.stat-card:hover .stat-icon {
    transform: scale(1.1);
    box-shadow: var(--shadow-lg);
}

.stat-info {
    flex: 1;
    min-width: 0;
}

.stat-number {
    font-size: 2.25rem;
    font-weight: 900;
    color: var(--gray-900);
    line-height: 1;
    margin-bottom: 0.5rem;
    letter-spacing: -0.025em;
}

.stat-description {
    font-size: 0.95rem;
    color: var(--gray-600);
    font-weight: 500;
    line-height: 1.4;
}

/* 为不同统计卡片添加特色颜色 */
.stat-card:nth-child(1) .stat-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card:nth-child(2) .stat-icon {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card:nth-child(3) .stat-icon {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card:nth-child(4) .stat-icon {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

/* 服务流程 */
.process-section {
    padding: 6rem 0;
    background: var(--gray-50);
}

.process-steps {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto;
}

.step-item {
    text-align: center;
    max-width: 250px;
    position: relative;
}

.step-number {
    width: 60px;
    height: 60px;
    background: var(--primary-gradient);
    color: var(--white);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 800;
    margin: 0 auto 1.5rem;
    position: relative;
    z-index: 2;
}

.step-content h3 {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 0.75rem;
}

.step-content p {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: 1rem;
}

.step-icon {
    font-size: 2rem;
    opacity: 0.7;
}

.step-connector {
    width: 100px;
    height: 2px;
    background: var(--gray-300);
    position: relative;
}

.step-connector::after {
    content: '';
    position: absolute;
    right: -8px;
    top: -6px;
    width: 0;
    height: 0;
    border-left: 10px solid var(--gray-300);
    border-top: 7px solid transparent;
    border-bottom: 7px solid transparent;
}

/* 特色功能 */
.features-section {
    padding: 6rem 0;
    background: var(--white);
}

.features-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    grid-template-rows: auto auto;
    gap: 2rem;
}

.feature-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: 2rem;
    border: 1px solid var(--gray-200);
    transition: all var(--duration-normal) ease;
    position: relative;
    overflow: hidden;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.large-card {
    grid-row: span 2;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background: var(--primary-gradient);
    color: var(--white);
}

.feature-icon {
    width: 48px;
    height: 48px;
    background: var(--gray-100);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    color: var(--primary-color);
}

.feature-icon.primary {
    background: rgba(255, 255, 255, 0.2);
    color: var(--white);
}

.feature-card h3 {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    color: var(--gray-900);
}

.large-card h3 {
    color: var(--white);
    font-size: 1.5rem;
}

.feature-card p {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: 1rem;
}

.large-card p {
    color: rgba(255, 255, 255, 0.9);
}

.feature-list {
    list-style: none;
    margin: 1.5rem 0;
}

.feature-list li {
    padding: 0.5rem 0;
    padding-left: 1.5rem;
    position: relative;
    color: rgba(255, 255, 255, 0.9);
}

.feature-list li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: rgba(255, 255, 255, 0.8);
    font-weight: bold;
}

.feature-visual {
    margin-top: 2rem;
}

.ai-animation {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.pulse-dot {
    width: 12px;
    height: 12px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: var(--radius-full);
    animation: pulse 2s infinite;
}

.delay-1 {
    animation-delay: 0.5s;
}

.delay-2 {
    animation-delay: 1s;
}

/* 专家团队 */
.team-section {
    padding: 6rem 0;
    background: var(--gray-50);
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
}

.doctor-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: 2rem;
    border: 1px solid var(--gray-200);
    transition: all var(--duration-normal) ease;
    text-align: center;
}

.doctor-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.doctor-avatar {
    position: relative;
    display: inline-block;
    margin-bottom: 1.5rem;
}

.doctor-avatar img {
    width: 80px;
    height: 80px;
    border-radius: var(--radius-full);
    border: 3px solid var(--gray-200);
}

.online-badge {
    position: absolute;
    bottom: 0;
    right: 0;
    background: var(--success-color);
    color: var(--white);
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-full);
    font-weight: 600;
}

.online-badge.offline {
    background: var(--gray-400);
}

.doctor-info h3 {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 0.25rem;
}

.doctor-title {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.doctor-experience {
    color: var(--gray-600);
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

.doctor-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.tag {
    background: var(--gray-100);
    color: var(--gray-700);
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-full);
    font-size: 0.75rem;
    font-weight: 500;
}

.doctor-stats {
    display: flex;
    justify-content: space-around;
    margin-top: 1rem;
}

.stat {
    text-align: center;
}

.stat-number {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--gray-900);
}

.stat-label {
    font-size: 0.75rem;
    color: var(--gray-600);
    margin-top: 0.25rem;
}

/* 用户评价 */
.testimonials-section {
    padding: 6rem 0;
    background: var(--white);
}

.testimonials-slider {
    position: relative;
    max-width: 800px;
    margin: 0 auto 2rem;
}

.testimonial-card {
    display: none;
    text-align: center;
    padding: 2rem;
}

.testimonial-card.active {
    display: block;
    animation: fadeIn var(--duration-normal) ease;
}

.testimonial-content {
    max-width: 600px;
    margin: 0 auto;
}

.quote-icon {
    font-size: 4rem;
    color: var(--primary-color);
    opacity: 0.3;
    margin-bottom: 1rem;
    line-height: 1;
}

.testimonial-text {
    font-size: 1.125rem;
    line-height: 1.8;
    color: var(--gray-700);
    margin-bottom: 2rem;
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.author-avatar img {
    width: 50px;
    height: 50px;
    border-radius: var(--radius-full);
    border: 2px solid var(--gray-200);
}

.author-info h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 0.25rem;
}

.author-info p {
    font-size: 0.875rem;
    color: var(--gray-600);
}

.rating {
    color: var(--warning-color);
    font-size: 1.25rem;
    margin-top: 1rem;
}

.slider-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2rem;
}

.slider-btn {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-full);
    border: 1px solid var(--gray-300);
    background: var(--white);
    color: var(--gray-600);
    font-size: 1.5rem;
    cursor: pointer;
    transition: all var(--duration-normal) ease;
}

.slider-btn:hover {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.slider-dots {
    display: flex;
    gap: 0.5rem;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: var(--radius-full);
    background: var(--gray-300);
    cursor: pointer;
    transition: background var(--duration-normal) ease;
}

.dot.active {
    background: var(--primary-color);
}

/* FAQ区域 */
.faq-section {
    padding: 6rem 0;
    background: var(--gray-50);
}

.faq-list {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background: var(--white);
    border-radius: var(--radius-lg);
    margin-bottom: 1rem;
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.faq-question {
    padding: 1.5rem 2rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background var(--duration-normal) ease;
}

.faq-question:hover {
    background: var(--gray-50);
}

.faq-question h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.faq-toggle {
    font-size: 1.5rem;
    color: var(--primary-color);
    font-weight: bold;
    transition: transform var(--duration-normal) ease;
}

.faq-item.active .faq-toggle {
    transform: rotate(45deg);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height var(--duration-normal) ease;
}

.faq-item.active .faq-answer {
    max-height: 200px;
}

.faq-answer p {
    padding: 0 2rem 1.5rem;
    color: var(--gray-700);
    line-height: 1.6;
    margin: 0;
}

/* CTA区域 */
.cta-section {
    padding: 6rem 0;
    background: var(--primary-gradient);
    color: var(--white);
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    letter-spacing: -0.025em;
}

.cta-content p {
    font-size: 1.25rem;
    opacity: 0.9;
    margin-bottom: 2rem;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.cta-primary-btn,
.cta-secondary-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 2rem;
    font-size: 1.125rem;
    font-weight: 700;
    border-radius: var(--radius-lg);
    transition: all var(--duration-normal) ease;
    cursor: pointer;
    border: none;
    text-decoration: none;
}

.cta-primary-btn .btn-icon,
.cta-secondary-btn .btn-icon {
    font-size: 1.375rem;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.cta-primary-btn .btn-icon svg,
.cta-secondary-btn .btn-icon svg {
    width: 1em;
    height: 1em;
    fill: currentColor;
    display: block;
}

.cta-primary-btn {
    background: var(--white);
    color: var(--primary-color);
}

.cta-primary-btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

.cta-secondary-btn {
    background: transparent;
    color: var(--white);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.cta-secondary-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

/* 页脚 */
.site-footer {
    background: var(--gray-900);
    color: var(--gray-300);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    color: var(--white);
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.footer-section h4 {
    color: var(--white);
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.footer-section p {
    line-height: 1.6;
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: var(--gray-400);
    text-decoration: none;
    transition: color var(--duration-normal) ease;
}

.footer-section a:hover {
    color: var(--white);
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-link {
    width: 40px;
    height: 40px;
    background: var(--gray-800);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    transition: all var(--duration-normal) ease;
}

.social-link:hover {
    background: var(--primary-color);
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid var(--gray-800);
    padding-top: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
}

.footer-links {
    display: flex;
    gap: 2rem;
} 