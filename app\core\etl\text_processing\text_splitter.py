"""智能文本切分器

支持语义（semantic）和递归（recursive）两种文本切分策略
"""

import re
import math
from typing import List, Dict, Any, Optional, Tuple
import pandas as pd
from datetime import datetime
from abc import ABC, abstractmethod

try:
    from langchain_text_splitters import RecursiveCharacterTextSplitter
    from langchain_text_splitters import SemanticChunker
    from langchain_openai import OpenAIEmbeddings
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False
    print("警告: langchain_text_splitters 未安装，将使用简化版本的文本切分器")


class BaseTextSplitter(ABC):
    """文本切分器基类"""
    
    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 200, **kwargs):
        """初始化文本切分器
        
        Args:
            chunk_size: 块大小
            chunk_overlap: 块重叠大小
            **kwargs: 其他参数
        """
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.kwargs = kwargs
    
    @abstractmethod
    def split_text(self, text: str) -> List[str]:
        """切分文本
        
        Args:
            text: 要切分的文本
            
        Returns:
            切分后的文本块列表
        """
        pass
    
    def get_splitter_info(self) -> Dict[str, Any]:
        """获取切分器信息"""
        return {
            'type': self.__class__.__name__,
            'chunk_size': self.chunk_size,
            'chunk_overlap': self.chunk_overlap,
            'kwargs': self.kwargs
        }


class RecursiveTextSplitter(BaseTextSplitter):
    """递归文本切分器
    
    按照字符级别递归切分文本，优先保持段落、句子的完整性
    """
    
    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 200, **kwargs):
        super().__init__(chunk_size, chunk_overlap, **kwargs)
        
        # 默认分隔符优先级（中文优化）
        self.separators = kwargs.get('separators', [
            "\n\n",  # 段落分隔
            "\n",    # 行分隔
            "。",    # 中文句号
            "！",    # 中文感叹号
            "？",    # 中文问号
            "；",    # 中文分号
            "，",    # 中文逗号
            ".",     # 英文句号
            "!",     # 英文感叹号
            "?",     # 英文问号
            ";",     # 英文分号
            ",",     # 英文逗号
            " ",     # 空格
            ""       # 字符级别
        ])
        
        if LANGCHAIN_AVAILABLE:
            self.langchain_splitter = RecursiveCharacterTextSplitter(
                chunk_size=chunk_size,
                chunk_overlap=chunk_overlap,
                separators=self.separators
            )
        else:
            self.langchain_splitter = None
    
    def split_text(self, text: str) -> List[str]:
        """递归切分文本"""
        if not text or not text.strip():
            return []
        
        if self.langchain_splitter:
            # 使用langchain的递归切分器
            return self.langchain_splitter.split_text(text)
        else:
            # 使用简化版本的递归切分
            return self._simple_recursive_split(text)
    
    def _simple_recursive_split(self, text: str) -> List[str]:
        """简化版本的递归切分"""
        if len(text) <= self.chunk_size:
            return [text]
        
        chunks = []
        
        # 尝试按分隔符切分
        for separator in self.separators:
            if separator in text:
                parts = text.split(separator)
                current_chunk = ""
                
                for part in parts:
                    # 如果当前块加上新部分不超过限制
                    if len(current_chunk) + len(part) + len(separator) <= self.chunk_size:
                        if current_chunk:
                            current_chunk += separator + part
                        else:
                            current_chunk = part
                    else:
                        # 保存当前块
                        if current_chunk:
                            chunks.append(current_chunk)
                        
                        # 如果单个部分太长，递归切分
                        if len(part) > self.chunk_size:
                            chunks.extend(self._simple_recursive_split(part))
                        else:
                            current_chunk = part
                
                # 添加最后一个块
                if current_chunk:
                    chunks.append(current_chunk)
                
                return chunks
        
        # 如果没有找到合适的分隔符，按字符切分
        chunks = []
        for i in range(0, len(text), self.chunk_size - self.chunk_overlap):
            chunk = text[i:i + self.chunk_size]
            if chunk.strip():
                chunks.append(chunk)
        
        return chunks


class SemanticTextSplitter(BaseTextSplitter):
    """语义文本切分器
    
    基于语义相似性进行文本切分，保持语义的连贯性
    """
    
    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 200, **kwargs):
        super().__init__(chunk_size, chunk_overlap, **kwargs)
        
        self.embedding_model = kwargs.get('embedding_model', 'text-embedding-ada-002')
        self.similarity_threshold = kwargs.get('similarity_threshold', 0.7)
        
        if LANGCHAIN_AVAILABLE:
            try:
                # 尝试使用OpenAI嵌入
                embeddings = OpenAIEmbeddings(model=self.embedding_model)
                self.langchain_splitter = SemanticChunker(
                    embeddings=embeddings,
                    breakpoint_threshold_type="percentile",
                    breakpoint_threshold_amount=75
                )
            except Exception as e:
                print(f"警告: 无法初始化语义切分器，将使用简化版本: {e}")
                self.langchain_splitter = None
        else:
            self.langchain_splitter = None
    
    def split_text(self, text: str) -> List[str]:
        """语义切分文本"""
        if not text or not text.strip():
            return []
        
        if self.langchain_splitter:
            try:
                # 使用langchain的语义切分器
                return self.langchain_splitter.split_text(text)
            except Exception as e:
                print(f"语义切分失败，回退到简化版本: {e}")
                return self._simple_semantic_split(text)
        else:
            # 使用简化版本的语义切分
            return self._simple_semantic_split(text)
    
    def _simple_semantic_split(self, text: str) -> List[str]:
        """简化版本的语义切分
        
        基于句子边界和段落结构进行切分，尽量保持语义完整性
        """
        # 首先按段落切分
        paragraphs = re.split(r'\n\s*\n', text)
        chunks = []
        current_chunk = ""
        
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue
            
            # 如果当前段落加上现有块不超过限制
            if len(current_chunk) + len(paragraph) + 2 <= self.chunk_size:
                if current_chunk:
                    current_chunk += "\n\n" + paragraph
                else:
                    current_chunk = paragraph
            else:
                # 保存当前块
                if current_chunk:
                    chunks.append(current_chunk)
                
                # 如果单个段落太长，按句子切分
                if len(paragraph) > self.chunk_size:
                    sentence_chunks = self._split_by_sentences(paragraph)
                    chunks.extend(sentence_chunks)
                    current_chunk = ""
                else:
                    current_chunk = paragraph
        
        # 添加最后一个块
        if current_chunk:
            chunks.append(current_chunk)
        
        return chunks
    
    def _split_by_sentences(self, text: str) -> List[str]:
        """按句子切分长段落"""
        # 中文和英文句子分隔符
        sentence_endings = r'[。！？.!?]'
        sentences = re.split(f'({sentence_endings})', text)
        
        chunks = []
        current_chunk = ""
        
        i = 0
        while i < len(sentences):
            sentence = sentences[i]
            
            # 如果是标点符号，与前一个句子合并
            if i + 1 < len(sentences) and re.match(sentence_endings, sentences[i + 1]):
                sentence += sentences[i + 1]
                i += 2
            else:
                i += 1
            
            sentence = sentence.strip()
            if not sentence:
                continue
            
            # 检查是否超过块大小限制
            if len(current_chunk) + len(sentence) <= self.chunk_size:
                current_chunk += sentence
            else:
                # 保存当前块
                if current_chunk:
                    chunks.append(current_chunk)
                
                # 如果单个句子太长，强制切分
                if len(sentence) > self.chunk_size:
                    for j in range(0, len(sentence), self.chunk_size - self.chunk_overlap):
                        chunk = sentence[j:j + self.chunk_size]
                        if chunk.strip():
                            chunks.append(chunk)
                    current_chunk = ""
                else:
                    current_chunk = sentence
        
        # 添加最后一个块
        if current_chunk:
            chunks.append(current_chunk)
        
        return chunks


class TextSplitterManager:
    """文本切分管理器
    
    管理不同类型的文本切分器，提供统一的接口
    """
    
    def __init__(self, splitter_config: Dict[str, Any] = None):
        """初始化切分管理器
        
        Args:
            splitter_config: 切分器配置
        """
        self.config = splitter_config or {}
        self.default_chunk_size = self.config.get('chunk_size', 1000)
        self.default_chunk_overlap = self.config.get('chunk_overlap', 200)
        
        # 支持的切分器类型
        self.splitter_types = {
            'recursive': RecursiveTextSplitter,
            'semantic': SemanticTextSplitter
        }
    
    def create_splitter(self, splitter_type: str, **kwargs) -> BaseTextSplitter:
        """创建指定类型的切分器
        
        Args:
            splitter_type: 切分器类型 ('recursive' 或 'semantic')
            **kwargs: 切分器参数
            
        Returns:
            文本切分器实例
        """
        if splitter_type not in self.splitter_types:
            raise ValueError(f"不支持的切分器类型: {splitter_type}. 支持的类型: {list(self.splitter_types.keys())}")
        
        # 合并默认配置和传入参数
        splitter_kwargs = {
            'chunk_size': self.default_chunk_size,
            'chunk_overlap': self.default_chunk_overlap,
            **self.config.get(splitter_type, {}),
            **kwargs
        }
        
        splitter_class = self.splitter_types[splitter_type]
        return splitter_class(**splitter_kwargs)
    
    def split_dataframe(self, df: pd.DataFrame, splitter_type: str = 'recursive', 
                       text_column: str = 'content', source_name: str = "Unknown") -> pd.DataFrame:
        """对DataFrame中的文本进行切分
        
        Args:
            df: 输入DataFrame
            splitter_type: 切分器类型
            text_column: 包含文本的列名
            source_name: 数据源名称
            
        Returns:
            包含切分后文本块的DataFrame
        """
        if df.empty:
            print(f"数据源 {source_name}: 输入DataFrame为空，跳过文本切分")
            return df
        
        if text_column not in df.columns:
            print(f"数据源 {source_name}: 未找到文本列 '{text_column}'，跳过文本切分")
            return df
        
        print(f"\n开始文本切分: {source_name}")
        print(f"切分策略: {splitter_type}")
        print(f"输入数据量: {len(df)} 条记录")
        
        # 创建切分器
        splitter = self.create_splitter(splitter_type)
        
        # 切分文本
        chunks_data = []
        total_chunks = 0
        total_chars_before = 0
        total_chars_after = 0
        
        for index, row in df.iterrows():
            text = str(row[text_column]) if pd.notna(row[text_column]) else ""
            
            if not text.strip():
                continue
            
            total_chars_before += len(text)
            
            # 切分文本
            chunks = splitter.split_text(text)
            
            # 为每个块创建记录
            for chunk_index, chunk in enumerate(chunks):
                chunk = chunk.strip()
                if not chunk:
                    continue
                
                total_chars_after += len(chunk)
                
                # 创建块的元数据
                chunk_metadata = {
                    'chunk_id': f"{source_name}_{index}_{chunk_index}",
                    'document_id': f"{source_name}_{index}",
                    'chunk_index': chunk_index,
                    'total_chunks': len(chunks),
                    'chunk_length': len(chunk),
                    'splitter_type': splitter_type,
                    'splitter_config': splitter.get_splitter_info(),
                    'original_row_index': index,
                    'split_timestamp': datetime.now().isoformat()
                }
                
                # 继承原始元数据
                if 'metadata' in row and isinstance(row['metadata'], dict):
                    chunk_metadata.update(row['metadata'])
                
                # 创建块记录
                chunk_record = row.copy()
                chunk_record[text_column] = chunk
                chunk_record['metadata'] = chunk_metadata
                
                chunks_data.append(chunk_record)
                total_chunks += 1
        
        # 创建结果DataFrame
        if chunks_data:
            result_df = pd.DataFrame(chunks_data)
        else:
            result_df = pd.DataFrame()
        
        # 输出切分统计
        avg_chunk_length = total_chars_after / total_chunks if total_chunks > 0 else 0
        
        print(f"切分完成:")
        print(f"  原始记录数: {len(df)}")
        print(f"  切分后块数: {total_chunks}")
        print(f"  平均块长度: {avg_chunk_length:.1f} 字符")
        print(f"  切分器配置: {splitter.get_splitter_info()}")
        
        return result_df
    
    def compare_splitters(self, text: str, splitter_types: List[str] = None) -> Dict[str, Any]:
        """比较不同切分器的效果
        
        Args:
            text: 要切分的文本
            splitter_types: 要比较的切分器类型列表
            
        Returns:
            比较结果字典
        """
        if splitter_types is None:
            splitter_types = ['recursive', 'semantic']
        
        results = {}
        
        for splitter_type in splitter_types:
            try:
                splitter = self.create_splitter(splitter_type)
                chunks = splitter.split_text(text)
                
                # 计算统计信息
                chunk_lengths = [len(chunk) for chunk in chunks]
                avg_length = sum(chunk_lengths) / len(chunk_lengths) if chunk_lengths else 0
                
                results[splitter_type] = {
                    'chunk_count': len(chunks),
                    'avg_chunk_length': avg_length,
                    'min_chunk_length': min(chunk_lengths) if chunk_lengths else 0,
                    'max_chunk_length': max(chunk_lengths) if chunk_lengths else 0,
                    'chunks': chunks,
                    'splitter_info': splitter.get_splitter_info()
                }
                
            except Exception as e:
                results[splitter_type] = {
                    'error': str(e),
                    'chunk_count': 0,
                    'avg_chunk_length': 0
                }
        
        return results
