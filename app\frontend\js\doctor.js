// 医生工作台JavaScript功能
let currentSessionId = null;
let currentPatientName = null;

// 初始化消息输入功能
function initializeMessageInput() {
    const doctorMessageInput = document.getElementById('doctorMessageInput');
    const sendMessageBtn = document.getElementById('sendMessageBtn');
    
    if (doctorMessageInput) {
        // 自动调整文本框高度
        doctorMessageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            
            // 启用/禁用发送按钮
            if (sendMessageBtn) {
                sendMessageBtn.disabled = this.value.trim() === '';
            }
        });
        
        // 回车发送消息（Shift+Enter换行）
        doctorMessageInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                if (!sendMessageBtn.disabled) {
                    sendDoctorMessage();
                }
            }
        });
    }
    
    if (sendMessageBtn) {
        sendMessageBtn.addEventListener('click', sendDoctorMessage);
    }
}

// 初始化加入会话模态框
function initializeJoinSessionModal() {
    const joinSessionBtn = document.getElementById('joinSessionBtn');
    const modal = document.getElementById('joinSessionModal');
    const closeBtn = document.getElementById('closeJoinModalBtn');
    const cancelBtn = document.getElementById('cancelJoinBtn');
    const confirmBtn = document.getElementById('confirmJoinBtn');
    
    if (joinSessionBtn) {
        joinSessionBtn.addEventListener('click', () => {
            if (modal && currentPatientName) {
                modal.style.display = 'block';
                const patientNameSpan = document.getElementById('joinPatientName');
                if (patientNameSpan) {
                    patientNameSpan.textContent = currentPatientName;
                }
            }
        });
    }
    
    if (closeBtn) {
        closeBtn.addEventListener('click', () => {
            if (modal) modal.style.display = 'none';
        });
    }
    
    if (cancelBtn) {
        cancelBtn.addEventListener('click', () => {
            if (modal) modal.style.display = 'none';
        });
    }
    
    if (confirmBtn) {
        confirmBtn.addEventListener('click', handleJoinSession);
    }
    
    // 预设消息选择
    document.querySelectorAll('.join-message-option').forEach(option => {
        option.addEventListener('click', function() {
            document.querySelectorAll('.join-message-option').forEach(opt => opt.classList.remove('selected'));
            this.classList.add('selected');
            const customInput = document.getElementById('customJoinMessage');
            if (customInput) {
                customInput.value = this.getAttribute('data-message');
            }
        });
    });
}

// 选择患者
function selectPatient(sessionId, patientName, doctorInvolved) {
    currentSessionId = sessionId;
    currentPatientName = patientName;
    
    // 更新当前患者显示
    const currentPatientElement = document.getElementById('currentPatientName');
    const currentPatientContainer = document.getElementById('currentPatient');
    
    if (currentPatientElement) {
        currentPatientElement.textContent = patientName;
    }
    
    if (currentPatientContainer) {
        currentPatientContainer.style.display = 'block';
    }
    
    // 控制界面元素显示
    const joinSessionArea = document.getElementById('joinSessionArea');
    const messageInputArea = document.getElementById('messageInputArea');
    
    if (doctorInvolved) {
        // 医生已加入，显示消息输入框
        if (joinSessionArea) joinSessionArea.style.display = 'none';
        if (messageInputArea) messageInputArea.style.display = 'block';
    } else {
        // 医生未加入，显示加入按钮
        if (joinSessionArea) joinSessionArea.style.display = 'block';
        if (messageInputArea) messageInputArea.style.display = 'none';
    }
    
    // 加载聊天历史
    loadChatHistory(sessionId);
}

// 处理加入会话
async function handleJoinSession() {
    if (!currentSessionId) return;
    
    const customInput = document.getElementById('customJoinMessage');
    const selectedOption = document.querySelector('.join-message-option.selected');
    
    const joinMessage = (customInput && customInput.value.trim()) || 
                       (selectedOption && selectedOption.getAttribute('data-message')) ||
                       '您好，我是医生，现在为您提供专业咨询。';
    
    try {
        const response = await fetch('/api/chat/doctor', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                session_id: currentSessionId,
                message: joinMessage,
                doctor_name: '医生'
            })
        });
        
        const data = await response.json();
        
        if (data.status === 'success') {
            // 隐藏加入会话区域，显示消息输入框
            const joinSessionArea = document.getElementById('joinSessionArea');
            const messageInputArea = document.getElementById('messageInputArea');
            const modal = document.getElementById('joinSessionModal');
            
            if (joinSessionArea) joinSessionArea.style.display = 'none';
            if (messageInputArea) messageInputArea.style.display = 'block';
            if (modal) modal.style.display = 'none';
            
            // 重新加载聊天历史
            loadChatHistory(currentSessionId);
            
            showNotification('成功加入会话', 'success');
        } else {
            showNotification('加入会话失败', 'error');
        }
    } catch (error) {
        console.error('加入会话失败:', error);
        showNotification('加入会话失败', 'error');
    }
}

// 发送医生消息
async function sendDoctorMessage() {
    const messageInput = document.getElementById('doctorMessageInput');
    const message = messageInput.value.trim();
    
    if (!message || !currentSessionId) {
        return;
    }
    
    const sendBtn = document.getElementById('sendMessageBtn');
    if (sendBtn) {
        sendBtn.disabled = true;
    }
    
    try {
        const response = await fetch('/api/chat/doctor', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                session_id: currentSessionId,
                message: message,
                role: 'doctor'
            })
        });
        
        const data = await response.json();
        
        if (data.status === 'success') {
            // 清空输入框
            messageInput.value = '';
            messageInput.style.height = 'auto';
            
            // 重新加载聊天历史
            loadChatHistory(currentSessionId);
            
            showNotification('消息发送成功', 'success');
        } else {
            showNotification('发送消息失败，请重试', 'error');
        }
    } catch (error) {
        console.error('发送消息错误:', error);
        showNotification('发送消息失败，请重试', 'error');
    } finally {
        if (sendBtn) {
            sendBtn.disabled = messageInput.value.trim() === '';
        }
    }
}

// 加载患者列表
async function loadPatientList() {
    try {
        const response = await fetch('/api/doctor/patients');
        const data = await response.json();
        
        if (data.status === 'success') {
            displayPatientList(data.patients);
        }
    } catch (error) {
        console.error('加载患者列表失败:', error);
        // 显示默认的空状态
        const patientList = document.getElementById('patientList');
        if (patientList) {
            patientList.innerHTML = '<div class="empty-state-message">暂无患者咨询</div>';
        }
    }
}

// 显示患者列表
function displayPatientList(patients) {
    const patientList = document.getElementById('patientList');
    if (!patientList) return;

    if (!patients || patients.length === 0) {
        patientList.innerHTML = '<div class="empty-state-message">暂无患者咨询</div>';
        return;
    }

    patientList.innerHTML = '';

    patients.forEach(patient => {
        const patientItem = document.createElement('div');
        patientItem.className = 'patient-item';
        patientItem.setAttribute('data-session-id', patient.session_id);
        patientItem.onclick = () => selectPatient(patient.session_id, patient.patient_name, patient.doctor_involved);

        // 根据优先级和医生参与状态设置样式
        const priorityClass = patient.priority === 'high' ? 'high-priority' : '';
        const doctorInvolvedClass = patient.doctor_involved ? 'doctor-involved' : '';

        patientItem.innerHTML = `
            <div class="patient-info">
                <div class="patient-name">${patient.patient_name}</div>
                <div class="patient-last-message">${patient.last_message || '暂无消息'}</div>
                <div class="patient-meta">
                    <span class="message-count">${patient.message_count || 0}条消息</span>
                    <span class="patient-status ${priorityClass} ${doctorInvolvedClass}">
                        ${patient.priority === 'high' ? '高优先级' : ''}
                        ${patient.doctor_involved ? '医生已参与' : '等待医生'}
                    </span>
                </div>
            </div>
            <div class="patient-time">${formatTime(patient.last_message_time)}</div>
        `;

        patientList.appendChild(patientItem);
    });
}

// 加载聊天历史
async function loadChatHistory(sessionId) {
    try {
        const response = await fetch(`/api/doctor/patient/${sessionId}/messages`);
        const data = await response.json();

        if (data.status === 'success') {
            displayChatMessages(data.messages);

            // 更新会话信息显示
            if (data.session_info) {
                updateSessionInfo(data.session_info);
            }
        } else {
            console.error('加载聊天历史失败:', data.error);
            showNotification('加载聊天历史失败', 'error');
        }
    } catch (error) {
        console.error('加载聊天历史失败:', error);
        showNotification('加载聊天历史失败', 'error');
    }
}

// 显示聊天消息
function displayChatMessages(messages) {
    const chatMessages = document.getElementById('chatMessages');
    if (!chatMessages) return;
    
    chatMessages.innerHTML = '';
    
    if (!messages || messages.length === 0) {
        chatMessages.innerHTML = '<div class="empty-state-message">暂无聊天记录</div>';
        return;
    }
    
    messages.forEach(message => {
        const messageElement = createMessageElement(message);
        chatMessages.appendChild(messageElement);
    });
    
    // 滚动到底部
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// 创建消息元素
function createMessageElement(message) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${message.role}`;

    let senderName = '';
    if (message.role === 'user') {
        senderName = '患者';
    } else if (message.role === 'doctor') {
        senderName = message.doctor_name || '医生';
    } else if (message.role === 'bot') {
        senderName = 'AI助手';
    }

    messageDiv.innerHTML = `
        <div class="message-content">
            <div class="message-header">
                <span class="sender-name">${senderName}</span>
                <span class="message-time">${formatTime(message.timestamp)}</span>
            </div>
            <div class="message-text">${message.content}</div>
        </div>
    `;

    return messageDiv;
}

// 更新会话信息显示
function updateSessionInfo(sessionInfo) {
    // 更新当前患者名称
    const currentPatientName = document.getElementById('currentPatientName');
    if (currentPatientName) {
        currentPatientName.textContent = sessionInfo.patient_name;
    }

    // 更新患者状态
    const patientStatus = document.getElementById('patientStatus');
    if (patientStatus) {
        patientStatus.style.display = 'inline';
        patientStatus.textContent = sessionInfo.doctor_involved ? '医生已参与' : '等待医生';
        patientStatus.className = `patient-status ${sessionInfo.doctor_involved ? 'doctor-involved' : 'waiting'}`;
    }

    // 显示当前患者区域
    const currentPatient = document.getElementById('currentPatient');
    if (currentPatient) {
        currentPatient.style.display = 'block';
    }
}

// 格式化时间
function formatTime(timestamp) {
    if (!timestamp) return '';

    // 如果是ISO字符串，直接创建Date对象
    // 如果是数字时间戳，需要乘以1000
    const date = typeof timestamp === 'string' ? new Date(timestamp) : new Date(timestamp * 1000);

    if (isNaN(date.getTime())) return '';

    return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
    });
}

// 显示通知
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;

    // 添加到页面
    document.body.appendChild(notification);

    // 3秒后移除
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}

// 更新患者优先级
async function updatePatientPriority(sessionId, priority) {
    try {
        const response = await fetch(`/api/doctor/patient/${sessionId}/priority`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                priority: priority
            })
        });

        const data = await response.json();

        if (data.status === 'success') {
            showNotification('优先级更新成功', 'success');
            // 重新加载患者列表以反映更改
            loadPatientList();
        } else {
            showNotification('更新优先级失败', 'error');
        }
    } catch (error) {
        console.error('更新优先级失败:', error);
        showNotification('更新优先级失败', 'error');
    }
}
