/* 现代化基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
    background: var(--hero-gradient);
    color: var(--gray-800);
    line-height: 1.6;
    min-height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
}

/* 现代化背景装饰 */
body::before {
    content: '';
    position: fixed;
    top: -50%;
    right: -20%;
    width: 40%;
    height: 120%;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.03) 0%, rgba(16, 185, 129, 0.03) 100%);
    border-radius: 50%;
    filter: blur(80px);
    z-index: 1;
    pointer-events: none;
    animation: float 20s ease-in-out infinite;
}

body::after {
    content: '';
    position: fixed;
    bottom: -30%;
    left: -10%;
    width: 30%;
    height: 80%;
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.02) 0%, rgba(245, 158, 11, 0.02) 100%);
    border-radius: 50%;
    filter: blur(60px);
    z-index: 1;
    pointer-events: none;
    animation: float 25s ease-in-out infinite reverse;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(1deg); }
    66% { transform: translateY(10px) rotate(-1deg); }
}

/* 现代化滚动条 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--gray-300), var(--gray-400));
    border-radius: var(--radius-full);
    transition: background var(--duration-normal) var(--ease-out);
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--gray-400), var(--gray-500));
}

/* 选择文本样式 */
::selection {
    background: var(--primary-100);
    color: var(--primary-800);
}

::-moz-selection {
    background: var(--primary-100);
    color: var(--primary-800);
}
