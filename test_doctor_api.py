#!/usr/bin/env python3
"""
测试医生API的简单脚本
"""

import requests
import json
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def test_doctor_api():
    """测试医生API端点"""
    base_url = "http://localhost:5000"
    
    print("🏥 测试医生工作台API")
    print("=" * 50)
    
    # 测试获取患者列表
    print("\n1. 测试获取患者列表...")
    try:
        response = requests.get(f"{base_url}/api/doctor/patients")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if data.get('status') == 'success':
                patients = data.get('patients', [])
                print(f"✅ 成功获取 {len(patients)} 个患者")
                
                # 如果有患者，测试获取消息
                if patients:
                    session_id = patients[0]['session_id']
                    print(f"\n2. 测试获取患者消息 (session_id: {session_id})...")
                    
                    msg_response = requests.get(f"{base_url}/api/doctor/patient/{session_id}/messages")
                    print(f"状态码: {msg_response.status_code}")
                    
                    if msg_response.status_code == 200:
                        msg_data = msg_response.json()
                        print(f"响应: {json.dumps(msg_data, indent=2, ensure_ascii=False)}")
                        
                        if msg_data.get('status') == 'success':
                            messages = msg_data.get('messages', [])
                            print(f"✅ 成功获取 {len(messages)} 条消息")
                        else:
                            print(f"❌ 获取消息失败: {msg_data.get('error')}")
                    else:
                        print(f"❌ 请求失败: {msg_response.status_code}")
                        print(msg_response.text)
                        
                    # 测试更新优先级
                    print(f"\n3. 测试更新患者优先级...")
                    priority_response = requests.put(
                        f"{base_url}/api/doctor/patient/{session_id}/priority",
                        json={"priority": "high"},
                        headers={"Content-Type": "application/json"}
                    )
                    print(f"状态码: {priority_response.status_code}")
                    
                    if priority_response.status_code == 200:
                        priority_data = priority_response.json()
                        print(f"响应: {json.dumps(priority_data, indent=2, ensure_ascii=False)}")
                        
                        if priority_data.get('status') == 'success':
                            print("✅ 成功更新优先级")
                        else:
                            print(f"❌ 更新优先级失败: {priority_data.get('error')}")
                    else:
                        print(f"❌ 请求失败: {priority_response.status_code}")
                        print(priority_response.text)
                else:
                    print("ℹ️  当前没有患者数据，无法测试消息获取功能")
            else:
                print(f"❌ API返回错误: {data.get('error')}")
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(response.text)
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保Flask应用正在运行 (python run.py)")
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")

if __name__ == "__main__":
    test_doctor_api()
