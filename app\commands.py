import click
from flask.cli import with_appcontext
from app.models.database import db
from app.models.user import User

@click.command('init-db')
@with_appcontext
def init_db_command():
    """清空数据库并创建新表"""
    db.drop_all()
    db.create_all()
    click.echo('数据库已初始化')

@click.command('create-admin')
@click.argument('username')
@click.argument('password')
@with_appcontext
def create_admin_command(username, password):
    """创建管理员用户"""
    admin = User(username=username, password=password, role='doctor', license_number='ADMIN')
    db.session.add(admin)
    db.session.commit()
    click.echo(f'管理员 {username} 创建成功')

def register_commands(app):
    """向Flask应用注册命令"""
    app.cli.add_command(init_db_command)
    app.cli.add_command(create_admin_command) 