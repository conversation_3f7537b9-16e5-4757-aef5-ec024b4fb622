"""搜索引擎基类

定义关键词搜索引擎的统一接口
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
import pandas as pd


class BaseSearchEngine(ABC):
    """搜索引擎基类"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """初始化搜索引擎
        
        Args:
            config: 搜索引擎配置
        """
        self.config = config or {}
        self.index_name = self.config.get('index_name', 'medical_documents')
    
    @abstractmethod
    def create_index(self, index_name: str = None, mapping: Dict[str, Any] = None) -> bool:
        """创建索引
        
        Args:
            index_name: 索引名称
            mapping: 索引映射配置
            
        Returns:
            创建是否成功
        """
        pass
    
    @abstractmethod
    def delete_index(self, index_name: str = None) -> bool:
        """删除索引
        
        Args:
            index_name: 索引名称
            
        Returns:
            删除是否成功
        """
        pass
    
    @abstractmethod
    def index_exists(self, index_name: str = None) -> bool:
        """检查索引是否存在
        
        Args:
            index_name: 索引名称
            
        Returns:
            索引是否存在
        """
        pass
    
    @abstractmethod
    def add_documents(self, documents: List[Dict[str, Any]], index_name: str = None) -> bool:
        """批量添加文档
        
        Args:
            documents: 文档列表
            index_name: 索引名称
            
        Returns:
            添加是否成功
        """
        pass
    
    @abstractmethod
    def update_document(self, doc_id: str, document: Dict[str, Any], index_name: str = None) -> bool:
        """更新文档
        
        Args:
            doc_id: 文档ID
            document: 文档内容
            index_name: 索引名称
            
        Returns:
            更新是否成功
        """
        pass
    
    @abstractmethod
    def delete_document(self, doc_id: str, index_name: str = None) -> bool:
        """删除文档
        
        Args:
            doc_id: 文档ID
            index_name: 索引名称
            
        Returns:
            删除是否成功
        """
        pass
    
    @abstractmethod
    def search(self, query: str, filters: Dict[str, Any] = None, 
               size: int = 10, index_name: str = None) -> List[Dict[str, Any]]:
        """搜索文档
        
        Args:
            query: 搜索查询
            filters: 过滤条件
            size: 返回结果数量
            index_name: 索引名称
            
        Returns:
            搜索结果列表
        """
        pass
    
    @abstractmethod
    def get_document_count(self, index_name: str = None) -> int:
        """获取文档数量
        
        Args:
            index_name: 索引名称
            
        Returns:
            文档数量
        """
        pass
    
    def clear_index(self, index_name: str = None) -> bool:
        """清空索引
        
        Args:
            index_name: 索引名称
            
        Returns:
            清空是否成功
        """
        if self.index_exists(index_name):
            return self.delete_index(index_name) and self.create_index(index_name)
        return self.create_index(index_name)
    
    def add_dataframe(self, df: pd.DataFrame, content_column: str = 'content', 
                     metadata_column: str = 'metadata', index_name: str = None) -> bool:
        """从DataFrame添加文档
        
        Args:
            df: 输入DataFrame
            content_column: 内容列名
            metadata_column: 元数据列名
            index_name: 索引名称
            
        Returns:
            添加是否成功
        """
        if df.empty:
            return True
        
        documents = []
        for _, row in df.iterrows():
            content = row.get(content_column, '')
            metadata = row.get(metadata_column, {})
            
            if isinstance(metadata, dict):
                doc = {
                    'content': str(content),
                    **metadata
                }
                documents.append(doc)
        
        return self.add_documents(documents, index_name)
    
    def get_engine_info(self) -> Dict[str, Any]:
        """获取搜索引擎信息
        
        Returns:
            搜索引擎信息
        """
        return {
            'engine_type': self.__class__.__name__,
            'index_name': self.index_name,
            'config': self.config
        }
