# LLM and Embedding Model Settings
llm:
  provider: "openai"
  name: "huatuogpt-7b"
  temperature: 0.7

embedding:
  provider: "local"
  name: "BAAI/bge-small-zh"

# Vector Store Settings
vector_store:
  # Using local ChromaDB
  persist_directory: "chroma_db"
  collection_name: "llm_scaffold_collection"

# Text Splitter Settings
text_splitter:
  chunk_size: 1000
  chunk_overlap: 100

# Hybrid Search Settings (SRH-001)
hybrid_search:
  # 是否启用混合搜索
  enabled: true

  # 搜索权重配置
  weights:
    vector: 0.5      # 向量搜索权重
    keyword: 0.5     # 关键词搜索权重

  # RRF算法参数
  rrf:
    k: 60           # RRF算法的k参数，用于平滑排名影响

  # 搜索结果配置
  results:
    top_k: 10       # 返回的文档数量

  # 关键词搜索引擎配置
  search_engine:
    type: "simple"  # 搜索引擎类型: "simple" 或 "elasticsearch"

    # 简单搜索引擎配置
    simple:
      index_name: "medical_knowledge"
      persist_directory: "data/search_index"

    # Elasticsearch配置（如果使用）
    elasticsearch:
      host: "localhost"
      port: 9200
      index_name: "medical_knowledge"
      # username: ""
      # password: ""

# Reranker Settings (SRH-002)
retrieval:
  reranker:
    # 是否启用重排器
    enabled: true

    # 重排器类型: "bge_reranker", "cross_encoder", "sentence_transformer"
    type: "bge_reranker"

    # 重排器模型配置
    model:
      # 模型名称
      name: "BAAI/bge-reranker-base"

      # 设备选择: "cpu", "cuda", "mps"
      device: "cpu"

      # 最大序列长度
      max_length: 512

      # 是否标准化分数
      normalize_scores: true

    # 重排参数
    parameters:
      # 对Top-N个结果进行重排
      top_n: 20

      # 批处理大小
      batch_size: 32

      # 最大内容长度（超过会截断）
      max_content_length: 512

    # 性能配置
    performance:
      # 是否使用缓存
      use_cache: true

      # 是否启用批处理
      enable_batching: true

      # 超时时间（秒）
      timeout: 30