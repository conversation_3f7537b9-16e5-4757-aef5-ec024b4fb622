/*==============================================================*/
/* DBMS name:      MySQL 5.0                                    */
/* Created on:     2025/7/16 10:10:40                           */
/*==============================================================*/

drop table if exists chat_messages;
drop table if exists chat_sessions;
drop table if exists ChatMessage;

drop table if exists ConsultationSession;

drop table if exists Diagnosis;

drop table if exists DoctorProfile;

drop table if exists MedicalKnowledgeBase;

drop table if exists PatientProfile;

drop table if exists Prescription;

drop table if exists QuestionnaireResponse;

drop table if exists SymptomEntry;

drop table if exists users;

drop table if exists user_profiles;

drop table if exists `references_knowledge`;

drop table if exists `relates_symptom_knowledge`;

/*==============================================================*/
/* Table: chat_sessions                                         */
/*==============================================================*/
create table chat_sessions
(
   id                   varchar(50) not null comment '会话ID',
   user_id              int not null comment '用户ID',
   title                varchar(255) comment '会话标题',
   created_at           datetime default current_timestamp comment '创建时间',
   last_activity        datetime default current_timestamp on update current_timestamp comment '最后活动时间',
   status               varchar(20) default 'active' comment '状态：active, archived, deleted',
   doctor_involved      tinyint(1) default 0 comment '医生是否参与',
   priority             varchar(20) default 'normal' comment '优先级：normal, high',
   primary key (id)
);

alter table chat_sessions comment '聊天会话表';

/*==============================================================*/
/* Table: chat_messages                                         */
/*==============================================================*/
create table chat_messages
(
   id                   int not null auto_increment comment '消息ID',
   session_id           varchar(50) not null comment '会话ID',
   role                 varchar(20) not null comment '角色：user, bot, doctor',
   content              text not null comment '消息内容',
   timestamp            datetime default current_timestamp comment '发送时间',
   doctor_name          varchar(50) comment '医生名称（仅当role为doctor时使用）',
   primary key (id)
);

alter table chat_messages comment '聊天消息表';

/*==============================================================*/
/* Table: ChatMessage                                           */
/*==============================================================*/
create table ChatMessage
(
   message_id           int not null auto_increment comment '消息ID',
   Con_session_id       int comment '问诊会话_会话ID',
   session_id           int,
   sender_type          char(10) comment '发送方类型',
   content              text comment '内容',
   sent_at              datetime comment '发送时间',
   primary key (message_id)
);

alter table ChatMessage comment '对话消息';

/*==============================================================*/
/* Table: ConsultationSession                                   */
/*==============================================================*/
create table ConsultationSession
(
   session_id           int not null auto_increment comment '会话ID',
   prescription_id      int comment '处方ID',
   profile_id           int comment '档案ID',
   Doc_doctor_id        int comment '医生资料_医生ID',
   kb_id                int,
   patient_id           int,
   doctor_id            int,
   status               char(20) comment '会话状态',
   started_at           datetime comment '开始时间',
   ended_at             datetime comment '结束时间',
   primary key (session_id)
);

alter table ConsultationSession comment '问诊会话信息';

/*==============================================================*/
/* Table: Diagnosis                                             */
/*==============================================================*/
create table Diagnosis
(
   diagnosis_id         int not null auto_increment comment '诊断ID',
   Con_session_id       int comment '问诊会话_会话ID',
   session_id           int,
   icd_code             char(10) comment '诊断编码',
   diagnosis_name       varchar(255) comment '诊断名称',
   confidence           decimal(5,2) comment '置信度',
   created_at           datetime comment '诊断时间',
   primary key (diagnosis_id)
);

alter table Diagnosis comment '诊断记录';

/*==============================================================*/
/* Table: DoctorProfile                                         */
/*==============================================================*/
create table DoctorProfile
(
   doctor_id            int not null auto_increment comment '医生ID',
   Use_user_id          int comment '用户_用户ID',
   user_id              int,
   license              int comment '执照号',
   specialties          text comment '擅长科室',
   title                char(30) comment '职称',
   primary key (doctor_id)
);

alter table DoctorProfile comment '医生信息资料';

/*==============================================================*/
/* Table: MedicalKnowledgeBase                                  */
/*==============================================================*/
create table MedicalKnowledgeBase
(
   kb_id                int not null auto_increment comment '知识ID',
   doctor_id            int comment '审核人ID',
   title                varchar(255) comment '标题',
   category             char(50) comment '分类',
   tags                 text comment '主题标签',
   content              text comment '全文内容',
   source_type          char(30) comment '来源类型',
   version              char(20) comment '版本号',
   created_at           datetime comment '创建时间',
   updated_at           datetime comment '更新时间',
   primary key (kb_id)
);

alter table MedicalKnowledgeBase comment '医疗知识库';

/*==============================================================*/
/* Table: PatientProfile                                        */
/*==============================================================*/
create table PatientProfile
(
   profile_id           int not null auto_increment comment '档案ID',
   user_id              int comment '用户ID',
   nickname             varchar(50) comment '昵称',
   gender               char(10) comment '性别',
   age                  int comment '年龄',
   birth_date           date comment '出生日期',
   height               decimal(5,2) comment '身高(cm)',
   weight               decimal(5,2) comment '体重(kg)',
   blood_type           varchar(10) comment '血型',
   phone                varchar(20) comment '手机号',
   email                varchar(100) comment '邮箱',
   emergency_contact    varchar(50) comment '紧急联系人',
   emergency_phone      varchar(20) comment '紧急联系人电话',
   allergies            text comment '过敏史',
   medical_history      text comment '既往病史',
   introduction         text comment '个人简介',
   created_at           datetime default current_timestamp comment '创建时间',
   updated_at           datetime default current_timestamp on update current_timestamp comment '更新时间',
   primary key (profile_id)
);

alter table PatientProfile comment '患者档案信息';

/*==============================================================*/
/* Table: user_profiles                                         */
/*==============================================================*/
create table user_profiles
(
   profile_id           int not null auto_increment comment '资料ID',
   user_id              int not null comment '用户ID',
   avatar               varchar(255) comment '头像URL',
   nickname             varchar(50) comment '昵称',
   gender               varchar(10) comment '性别',
   age                  int comment '年龄',
   birth_date           date comment '出生日期',
   height               decimal(5,2) comment '身高(cm)',
   weight               decimal(5,2) comment '体重(kg)',
   blood_type           varchar(10) comment '血型',
   phone                varchar(20) comment '手机号',
   email                varchar(100) comment '邮箱',
   address              varchar(255) comment '地址',
   emergency_contact    varchar(50) comment '紧急联系人',
   emergency_phone      varchar(20) comment '紧急联系人电话',
   allergies            text comment '过敏史',
   medical_history      text comment '既往病史',
   introduction         text comment '个人简介',
   created_at           datetime default current_timestamp comment '创建时间',
   updated_at           datetime default current_timestamp on update current_timestamp comment '更新时间',
   primary key (profile_id),
   unique key UK_user_profile (user_id)
);

alter table user_profiles comment '用户个人资料表';

/*==============================================================*/
/* Table: Prescription                                          */
/*==============================================================*/
create table Prescription
(
   prescription_id      int not null auto_increment comment '处方ID',
   Con_session_id       int comment '问诊会话_会话ID',
   session_id           int,
   doctor_id            int comment '开方医生ID',
   medications          text comment '药品清单',
   instructions         text comment '医嘱',
   issued_at            datetime comment '签发时间',
   primary key (prescription_id)
);

alter table Prescription comment '医生开具的处方信息';

/*==============================================================*/
/* Table: QuestionnaireResponse                                 */
/*==============================================================*/
create table QuestionnaireResponse
(
   response_id          int not null auto_increment comment '答卷ID',
   Con_session_id       int comment '问诊会话_会话ID',
   session_id           int,
   questionnaire_version char(20) comment '问卷版本',
   answers              text comment '答案',
   completed_at         datetime comment '完成时间',
   primary key (response_id)
);

alter table QuestionnaireResponse comment '在问诊流程的关键节点（会话前、中、后）以结构化方式收集患者健康信息与反馈，为 AI 诊断推理、医生决策 和 疗效追踪 提';

/*==============================================================*/
/* Table: SymptomEntry                                          */
/*==============================================================*/
create table SymptomEntry
(
   symptom_id           int not null auto_increment comment '症状ID',
   Con_session_id       int comment '问诊会话_会话ID',
   session_id           int,
   symptom_name         varchar(100) comment '症状名称',
   description          text comment '描述',
   onset_date           date comment '起始日期',
   severity             char(10) comment '严重程度',
   primary key (symptom_id)
);

alter table SymptomEntry comment '症状条目信息';

/*==============================================================*/
/* Table: users                                                 */
/*==============================================================*/
DROP TABLE IF EXISTS users;
create table users
(
   id                   int not null auto_increment comment '用户ID',
   username             varchar(50) unique not null comment '用户名',
   password_hash        varchar(255) not null comment '密码哈希',
   role                 varchar(20) default 'patient' comment '角色类型：patient, doctor',
   license_number       varchar(50) comment '医生执照号（仅医生角色）',
   created_at           datetime(0) default current_timestamp comment '创建时间',
   last_login           datetime(0) null comment '最后登录时间',
   primary key (id),
   key idx_username (username)
);

alter table users comment '用户表';

/*==============================================================*/
/* Table: tokens                                                */
/*==============================================================*/
create table tokens
(
   id                   int not null auto_increment comment '令牌ID',
   token                varchar(255) unique not null comment '令牌字符串',
   user_id              int not null comment '用户ID',
   created_at           datetime default current_timestamp comment '创建时间',
   expires_at           datetime not null comment '过期时间',
   primary key (id),
   key idx_token (token),
   key idx_user_id (user_id)
);

alter table tokens comment '用户令牌表';

alter table tokens add constraint FK_token_belongs_to_user foreign key (user_id)
      references users (id) on delete cascade on update cascade;

/*==============================================================*/
/* Table: references_knowledge                                  */
/*==============================================================*/
create table `references_knowledge`
(
   diagnosis_id         int not null,
   kb_id                int not null comment '知识ID',
   primary key (diagnosis_id, kb_id)
);

alter table `references_knowledge` comment '参考知识';

/*==============================================================*/
/* Table: relates_symptom_knowledge                             */
/*==============================================================*/
create table `relates_symptom_knowledge`
(
   symptom_id           int not null comment '症状ID',
   kb_id                int not null comment '知识ID',
   primary key (symptom_id, kb_id)
);

alter table `relates_symptom_knowledge` comment '关联症状知识';

alter table ChatMessage add constraint `FK_contains_chat_message` foreign key (Con_session_id)
      references ConsultationSession (session_id) on delete restrict on update restrict;

alter table ConsultationSession add constraint `FK_attends_consultation_session` foreign key (Doc_doctor_id)
      references DoctorProfile (doctor_id) on delete restrict on update restrict;

alter table ConsultationSession add constraint `FK_initiates_consultation_session` foreign key (profile_id)
      references PatientProfile (profile_id) on delete restrict on update restrict;

alter table ConsultationSession add constraint `FK_issues_prescription` foreign key (prescription_id)
      references Prescription (prescription_id) on delete restrict on update restrict;

alter table ConsultationSession add constraint `FK_recommends_knowledge` foreign key (kb_id)
      references MedicalKnowledgeBase (kb_id) on delete restrict on update restrict;

alter table Diagnosis add constraint `FK_produces_diagnosis` foreign key (Con_session_id)
      references ConsultationSession (session_id) on delete restrict on update restrict;

alter table DoctorProfile add constraint `FK_has_doctor_profile` foreign key (Use_user_id)
      references users (id) on delete restrict on update restrict;

alter table PatientProfile add constraint `FK_has_patient_profile` foreign key (user_id)
      references users (id) on delete restrict on update restrict;

alter table Prescription add constraint `FK_issues_prescription2` foreign key (Con_session_id)
      references ConsultationSession (session_id) on delete restrict on update restrict;

alter table QuestionnaireResponse add constraint `FK_collects_questionnaire_response` foreign key (Con_session_id)
      references ConsultationSession (session_id) on delete restrict on update restrict;

alter table SymptomEntry add constraint `FK_records_symptom_entry` foreign key (Con_session_id)
      references ConsultationSession (session_id) on delete restrict on update restrict;

alter table user_profiles add constraint `FK_user_profile_belongs_to_user` foreign key (user_id)
      references users (id) on delete cascade on update cascade;

alter table `references_knowledge` add constraint `FK_references_knowledge` foreign key (diagnosis_id)
      references Diagnosis (diagnosis_id) on delete restrict on update restrict;

alter table `references_knowledge` add constraint `FK_references_knowledge2` foreign key (kb_id)
      references MedicalKnowledgeBase (kb_id) on delete restrict on update restrict;

alter table `relates_symptom_knowledge` add constraint `FK_relates_symptom_knowledge` foreign key (kb_id)
      references MedicalKnowledgeBase (kb_id) on delete restrict on update restrict;

alter table `relates_symptom_knowledge` add constraint `FK_relates_symptom_knowledge2` foreign key (symptom_id)
      references SymptomEntry (symptom_id) on delete restrict on update restrict;

alter table chat_messages add constraint FK_message_belongs_to_session foreign key (session_id)
      references chat_sessions (id) on delete cascade on update cascade;

alter table chat_sessions add constraint FK_session_belongs_to_user foreign key (user_id)
      references users (id) on delete cascade on update cascade;

alter table tokens add constraint FK_token_belongs_to_user foreign key (user_id)
      references users (id) on delete cascade on update cascade;

