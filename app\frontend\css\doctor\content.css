/* 现代化内容区域样式 */
.content-card {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: var(--radius-2xl);
    padding: 2rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--glass-border);
    transition: all var(--duration-normal) var(--ease-out);
    position: relative;
    overflow: hidden;
}

.content-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
}

.content-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
    border-color: rgba(255, 255, 255, 0.4);
}

/* 聊天消息区域 */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1.5rem;
    background: var(--gray-25);
    border-radius: var(--radius-xl);
    margin-bottom: 1.5rem;
    border: 1px solid var(--gray-200);
    min-height: 400px;
    position: relative;
}

.chat-messages::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 20px;
    background: linear-gradient(to bottom, var(--gray-25), transparent);
    pointer-events: none;
    z-index: 1;
}

.message {
    margin-bottom: 1.5rem;
    animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message.user .message-content {
    background: var(--primary-gradient);
    color: var(--white);
    margin-left: auto;
    max-width: 80%;
    border-radius: var(--radius-xl) var(--radius-xl) var(--radius-sm) var(--radius-xl);
    box-shadow: var(--shadow-md);
}

.message.assistant .message-content {
    background: var(--white);
    color: var(--gray-800);
    border: 1px solid var(--gray-200);
    max-width: 80%;
    border-radius: var(--radius-xl) var(--radius-xl) var(--radius-xl) var(--radius-sm);
    box-shadow: var(--shadow-sm);
}

.message.doctor .message-content {
    background: var(--secondary-gradient);
    color: var(--white);
    margin-left: auto;
    max-width: 80%;
    border-radius: var(--radius-xl) var(--radius-xl) var(--radius-sm) var(--radius-xl);
    box-shadow: var(--shadow-md);
}

.message.bot .message-content {
    background: var(--white);
    color: var(--gray-800);
    border: 1px solid var(--gray-200);
    max-width: 80%;
    border-radius: var(--radius-xl) var(--radius-xl) var(--radius-xl) var(--radius-sm);
    box-shadow: var(--shadow-sm);
}

.message-content {
    padding: 1rem 1.25rem;
    position: relative;
    overflow: hidden;
}

.message-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.8125rem;
    opacity: 0.9;
}

.sender-name {
    font-weight: 600;
    font-size: 0.8125rem;
}

.message-text {
    line-height: 1.6;
    font-size: 0.9375rem;
    font-weight: 500;
}

.message-time {
    font-size: 0.8125rem;
    opacity: 0.8;
    font-weight: 500;
}

/* AI诊断和处方区域 */
.ai-diagnosis,
.prescription {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: var(--radius-2xl);
    padding: 2rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--glass-border);
    transition: all var(--duration-normal) var(--ease-out);
    position: relative;
    overflow: hidden;
}

.ai-diagnosis::before,
.prescription::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
}

.ai-diagnosis:hover,
.prescription:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
    border-color: rgba(255, 255, 255, 0.4);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--gray-200);
}

.section-header h2 {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--gray-900);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    letter-spacing: -0.025em;
}

.section-header svg {
    color: var(--primary);
}

.diagnosis-confidence {
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;
}

.diagnosis-confidence strong {
    color: var(--success);
    font-weight: 700;
}

/* 空状态消息 */
.empty-state-message {
    text-align: center;
    color: var(--gray-500);
    font-size: 0.9375rem;
    padding: 3rem 2rem;
    font-weight: 500;
    background: var(--white);
    border-radius: var(--radius-xl);
    border: 2px dashed var(--gray-300);
    margin: 2rem 0;
}
