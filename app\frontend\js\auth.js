// DOM元素将在initializeAuth函数中获取
let loginBtn, registerBtn, registerNowBtn, loginAccountBtn, startConsultationBtn;
let loginModal, registerModal, closeBtns;
let loginForm, registerForm, switchToRegister, switchToLogin;
let registerRoleRadios, doctorFields;

// 初始化认证功能
function initializeAuth() {
    console.log('开始初始化认证功能...');
    
    // 获取DOM元素
    loginBtn = document.getElementById('loginBtn');
    registerBtn = document.getElementById('registerBtn');
    registerNowBtn = document.getElementById('registerNowBtn');
    loginAccountBtn = document.getElementById('loginAccountBtn');
    startConsultationBtn = document.getElementById('startConsultationBtn');

    loginModal = document.getElementById('loginModal');
    registerModal = document.getElementById('registerModal');
    closeBtns = document.querySelectorAll('.close-btn');

    loginForm = document.getElementById('loginForm');
    registerForm = document.getElementById('registerForm');
    switchToRegister = document.getElementById('switchToRegister');
    switchToLogin = document.getElementById('switchToLogin');

// 添加角色选择功能
    registerRoleRadios = document.getElementsByName('registerRole');
    doctorFields = document.querySelector('.doctor-fields');

    // 绑定所有事件
    bindAuthEvents();
    
    console.log('认证功能初始化完成');
}

// 绑定所有认证相关事件
function bindAuthEvents() {
// 当角色选择变化时显示/隐藏医生执照号字段
    if (registerRoleRadios && registerRoleRadios.length > 0) {
    registerRoleRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.value === 'doctor') {
                doctorFields.style.display = 'block';
                document.getElementById('licenseNumber').setAttribute('required', 'required');
            } else {
                doctorFields.style.display = 'none';
                document.getElementById('licenseNumber').removeAttribute('required');
            }
        });
    });
}

    // 绑定按钮事件
    if (loginBtn) {
        loginBtn.addEventListener('click', function() {
            // 检查用户是否已登录
            const token = localStorage.getItem('auth_token');
            const username = localStorage.getItem('username');
            if (token && username) {
                // 用户已登录，显示自定义提示信息
                showLoginStatusModal('您已登录，当前用户：' + username);
            } else {
                // 用户未登录，打开登录窗口
                openLoginModal();
            }
        });
    }
    
    if (registerBtn) {
        registerBtn.addEventListener('click', openRegisterModal);
    }
    
    if (registerNowBtn) {
        registerNowBtn.addEventListener('click', openRegisterModal);
    }
    
    if (loginAccountBtn) {
        loginAccountBtn.addEventListener('click', function() {
            // 检查用户是否已登录
            const token = localStorage.getItem('auth_token');
            const username = localStorage.getItem('username');
            if (token && username) {
                // 用户已登录，显示自定义提示信息
                showLoginStatusModal('您已登录，当前用户：' + username);
            } else {
                // 用户未登录，打开登录窗口
                openLoginModal();
            }
        });
    }
    
    if (startConsultationBtn) {
        startConsultationBtn.addEventListener('click', function() {
            const user = getCurrentUser();
            if (user) {
                window.location.href = './chat.html';
            } else {
                openLoginModal();
            }
        });
    }

    // 绑定模态框关闭按钮
    if (closeBtns) {
        closeBtns.forEach(btn => {
            btn.addEventListener('click', closeModals);
        });
    }

    // 绑定模态框切换
    if (switchToRegister) {
        switchToRegister.addEventListener('click', function(e) {
            e.preventDefault();
            closeModals();
            openRegisterModal();
        });
    }
    
    if (switchToLogin) {
        switchToLogin.addEventListener('click', function(e) {
            e.preventDefault();
            closeModals();
            openLoginModal();
        });
    }

    // 绑定表单提交
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }
    
    if (registerForm) {
        registerForm.addEventListener('submit', handleRegister);
    }

    // 点击模态框外部关闭
    window.addEventListener('click', function(e) {
        if (e.target === loginModal || e.target === registerModal) {
            closeModals();
        }
        // 点击登录状态提示框外部关闭
        const loginStatusModal = document.getElementById('loginStatusModal');
        if (e.target === loginStatusModal) {
            closeLoginStatusModal();
        }
        // 点击登录错误提示框外部关闭
        const loginErrorModal = document.getElementById('loginErrorModal');
        if (e.target === loginErrorModal) {
            loginErrorModal.style.display = 'none';
        }
    });

    // ESC键关闭模态框
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            closeModals();
            closeLoginStatusModal();
            // 关闭登录错误提示框
            const loginErrorModal = document.getElementById('loginErrorModal');
            if (loginErrorModal) {
                loginErrorModal.style.display = 'none';
            }
        }
    });
}

// 在模块化版本中，这个函数将被手动调用
// 当页面加载时，检查是否已登录
function initAuthCheck() {
    // 确保页面加载时关闭所有自定义模态框
    closeAllCustomModals();

    // 清理任何测试数据
    cleanupTestData();

    checkAuthStatus();
}

// 关闭所有自定义弹窗
function closeAllCustomModals() {
    document.querySelectorAll('.custom-modal').forEach(modal => {
        modal.classList.remove('show');
        modal.style.display = 'none';
    });
}

// 清理测试数据
function cleanupTestData() {
    // 检查是否存在测试数据并清理
    const token = localStorage.getItem('auth_token');
    const username = localStorage.getItem('username');

    // 如果存在明显的测试数据，清理它们
    if (token && (token.startsWith('test_token') || token.startsWith('token_'))) {
        console.log('检测到测试数据，正在清理...');
        localStorage.removeItem('auth_token');
        localStorage.removeItem('username');
        localStorage.removeItem('role');
        console.log('测试数据已清理');
        // 更新UI
        updateLoggedOutUI();
    }

    // 如果用户名是测试用户，也清理
    if (username === '测试用户') {
        console.log('检测到测试用户，正在清理...');
        localStorage.removeItem('auth_token');
        localStorage.removeItem('username');
        localStorage.removeItem('role');
        console.log('测试用户数据已清理');
        // 更新UI
        updateLoggedOutUI();
    }
}

// 手动清理所有登录数据（开发者工具使用）
function clearAllLoginData() {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('username');
    localStorage.removeItem('role');
    updateLoggedOutUI();
    console.log('所有登录数据已清理');
}

// 将清理函数暴露到全局作用域，方便开发者工具使用
window.clearAllLoginData = clearAllLoginData;
// 允许按钮或遮罩关闭弹窗
function closeCustomModal(id) {
    const modal = document.getElementById(id);
    if (modal) modal.classList.remove('show');
}
// 修改openLoginModal/openRegisterModal，先关闭所有自定义弹窗
function openLoginModal() {
    closeAllCustomModals();
    loginModal.classList.add('show');
}
function openRegisterModal() {
    closeAllCustomModals();
    registerModal.classList.add('show');
}
// 允许点击遮罩关闭弹窗
window.addEventListener('click', function(e) {
    document.querySelectorAll('.custom-modal.show').forEach(modal => {
        if (e.target === modal) {
            modal.classList.remove('show');
        }
    });
});

// 关闭所有模态框
function closeModals() {
    loginModal.classList.remove('show');
    registerModal.classList.remove('show');
}

// 检查认证状态
function checkAuthStatus() {
    const token = localStorage.getItem('auth_token');
    const username = localStorage.getItem('username');
    const role = localStorage.getItem('role');

    if (token && username && role) {
        // 用户已登录，更新UI（不显示提示框）
        updateLoggedInUI(username, role);
    } else {
        // 用户未登录，更新UI
        updateLoggedOutUI();
    }
}

// 更新已登录状态的UI
function updateLoggedInUI(username, role) {
    // 更新全局登录状态
    window.isLoggedIn = true;

    // 更新导航按钮
    if (loginBtn) {
        loginBtn.textContent = username;
        // 不需要重新设置onclick，因为bindAuthEvents中已经处理了登录状态检查
    }

    if (registerBtn) {
        registerBtn.textContent = '注销';
        // 移除原有的注册事件监听器
        registerBtn.removeEventListener('click', openRegisterModal);
        // 清除onclick事件
        registerBtn.onclick = null;
        // 设置新的注销事件
        registerBtn.onclick = logout;
        console.log('已设置注册按钮为注销功能');
    }

    if (startConsultationBtn) {
        // 根据角色修改按钮文本和跳转逻辑
        if (role === 'doctor') {
            startConsultationBtn.textContent = '进入医生工作台';
            startConsultationBtn.onclick = function() {
                window.location.href = '/doctor-dashboard';
            };
        } else {
            startConsultationBtn.textContent = '开始智能问诊';
            startConsultationBtn.onclick = function() {
                window.location.href = '/chat';
            };
        }
    }
}

// 更新未登录状态的UI
function updateLoggedOutUI() {
    // 更新全局登录状态
    window.isLoggedIn = false;

    // 恢复导航按钮
    if (loginBtn) {
        loginBtn.textContent = '登录';
    }

    if (registerBtn) {
        registerBtn.textContent = '注册';
        // 清除注销事件
        registerBtn.onclick = null;
        // 重新添加注册事件监听器（如果还没有的话）
        registerBtn.removeEventListener('click', openRegisterModal); // 先移除避免重复
        registerBtn.addEventListener('click', openRegisterModal);
        console.log('已恢复注册按钮为注册功能');
    }

    if (startConsultationBtn) {
        startConsultationBtn.textContent = '开始智能问诊';
        startConsultationBtn.onclick = function() {
            window.location.href = '/chat';
        };
    }
}

// 登录处理
function handleLogin(event) {
    event.preventDefault();
    
    const username = document.getElementById('loginUsername').value;
    const password = document.getElementById('loginPassword').value;
    const role = document.querySelector('input[name="loginRole"]:checked').value;
    
    if (!username || !password) {
        showLoginErrorModal('请填写用户名和密码');
        return;
    }
    
    // 调用登录API
    axios.post('/api/auth/login', {
        username: username,
        password: password
    })
    .then(response => {
        const data = response.data;
        if (data.success) {
            // 保存到本地存储
            localStorage.setItem('auth_token', data.token);
            localStorage.setItem('username', data.username);
            localStorage.setItem('role', data.role);
            
            // 更新UI
            updateLoggedInUI(data.username, data.role);
            
            // 关闭模态框
            closeModals();
            
            // 显示自定义弹窗
            const modal = document.getElementById('loginSuccessModal');
            if (modal) {
                modal.classList.add('show');
                setTimeout(() => {
                    modal.classList.remove('show');
                }, 1200);
            }
            
            // 根据角色跳转到不同页面
            if (data.role === 'doctor') {
                setTimeout(() => {
                    window.location.href = '/doctor-dashboard';
                }, 500);
            }
        } else {
            showLoginErrorModal(data.message || '用户名或密码错误');
        }
    })
    .catch(error => {
        console.error('登录错误:', error);
        if (error.response && error.response.data) {
            showLoginErrorModal(error.response.data.message || '用户名或密码错误');
        } else {
            showLoginErrorModal('登录失败，请稍后再试');
            // 使用模拟登录作为备用
            simulateLogin(username, password, role);
        }
    });
}

// 注册处理
function handleRegister(event) {
    event.preventDefault();
    
    const username = document.getElementById('registerUsername').value;
    const password = document.getElementById('registerPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const role = document.querySelector('input[name="registerRole"]:checked').value;
    const licenseNumber = document.getElementById('licenseNumber').value;
    
    if (!username || !password || !confirmPassword) {
        alert('请填写所有必填字段');
        return;
    }
    
    if (password !== confirmPassword) {
        alert('两次输入的密码不一致');
        return;
    }
    
    if (role === 'doctor' && !licenseNumber) {
        alert('医生注册需要提供执照号');
        return;
    }
    
    // 调用注册API
    axios.post('/api/auth/register', {
        username: username,
        password: password,
        role: role,
        license_number: licenseNumber
    })
    .then(response => {
        const data = response.data;
        if (data.success) {
            // 保存到本地存储
            localStorage.setItem('auth_token', data.token);
            localStorage.setItem('username', data.username);
            localStorage.setItem('role', data.role);
            
            // 更新UI
            updateLoggedInUI(data.username, data.role);
            
            // 关闭模态框
            closeModals();
            
            // 显示注册跳转问诊弹窗
            const modal = document.getElementById('registerToChatModal');
            if (modal) {
                modal.classList.add('show');
                setTimeout(() => {
                    modal.classList.remove('show');
                    window.location.href = '/chat';
                }, 1200);
            } else {
                window.location.href = '/chat';
            }
        } else {
            // alert(data.message || '注册失败');
            const failModal = document.getElementById('registerFailModal');
            const failText = document.getElementById('registerFailText');
            if (failModal && failText) {
                failText.textContent = data.message || '注册失败';
                failModal.classList.add('show');
                setTimeout(() => {
                    failModal.classList.remove('show');
                }, 1200);
            }
        }
    })
    .catch(error => {
        console.error('注册错误:', error);
        let msg = '注册失败，请稍后再试';
        if (error.response && error.response.data) {
            msg = error.response.data.message || msg;
        }
        // 使用自定义弹窗提示
        const failModal = document.getElementById('registerFailModal');
        const failText = document.getElementById('registerFailText');
        if (failModal && failText) {
            failText.textContent = msg;
            failModal.classList.add('show');
            setTimeout(() => {
                failModal.classList.remove('show');
            }, 1200);
        }
        // 使用模拟注册作为备用
        simulateRegister(username, password, role, licenseNumber);
    });
}

// 模拟登录
function simulateLogin(username, password, role) {
    // 模拟API请求延迟
    setTimeout(() => {
        // 简单验证，实际应用中应该使用后端验证
        if (username && password) {
            // 生成随机token
            const token = 'token_' + Math.random().toString(36).substr(2, 9);
            
            // 保存到本地存储
            localStorage.setItem('auth_token', token);
            localStorage.setItem('username', username);
            localStorage.setItem('role', role);
            
            // 更新UI
            updateLoggedInUI(username, role);
            
            // 关闭模态框
            closeModals();
            
            alert('登录成功！');
            
            // 根据角色跳转到不同页面
            if (role === 'doctor') {
                setTimeout(() => {
                    window.location.href = '/doctor-dashboard';
                }, 500);
            }
        } else {
            showLoginErrorModal('用户名或密码错误');
        }
    }, 500);
}

// 模拟注册
function simulateRegister(username, password, role, licenseNumber) {
    // 模拟API请求延迟
    setTimeout(() => {
        // 检查用户名是否已存在
        const existingUser = localStorage.getItem('registered_' + username);
        
        if (existingUser) {
            // alert('用户名已存在');
            const failModal = document.getElementById('registerFailModal');
            const failText = document.getElementById('registerFailText');
            if (failModal && failText) {
                failText.textContent = '用户名已存在';
                failModal.classList.add('show');
                setTimeout(() => {
                    failModal.classList.remove('show');
                }, 1200);
            }
            return;
        }
        
        // 医生角色需要执照号
        if (role === 'doctor' && !licenseNumber) {
            // alert('医生注册需要提供执照号');
            const failModal = document.getElementById('registerFailModal');
            const failText = document.getElementById('registerFailText');
            if (failModal && failText) {
                failText.textContent = '医生注册需要提供执照号';
                failModal.classList.add('show');
                setTimeout(() => {
                    failModal.classList.remove('show');
                }, 1200);
            }
            return;
        }
        
        // 保存用户信息
        localStorage.setItem('registered_' + username, JSON.stringify({
            username,
            password, // 注意：实际应用中不应该直接存储密码，这里仅作演示
            role,
            licenseNumber
        }));
        
        // 生成随机token
        const token = 'token_' + Math.random().toString(36).substr(2, 9);
        
        // 保存登录状态
        localStorage.setItem('auth_token', token);
        localStorage.setItem('username', username);
        localStorage.setItem('role', role);
        
        // 更新UI
        updateLoggedInUI(username, role);
        
        // 关闭模态框
        closeModals();
        
        alert('注册成功！');
        
        // 根据角色跳转到不同页面
        if (role === 'doctor') {
            setTimeout(() => {
                window.location.href = '/doctor-dashboard';
            }, 500);
        }
    }, 500);
}

// 注销
function logout() {
    const token = localStorage.getItem('auth_token');
    
    // 如果有token，调用注销API
    if (token) {
        axios.post('/api/auth/logout', {}, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        })
        .catch(error => {
            console.error('注销错误:', error);
        })
        .finally(() => {
            // 无论API调用成功与否，都清除本地存储
            localStorage.removeItem('auth_token');
            localStorage.removeItem('username');
            localStorage.removeItem('role');
            
            // 刷新页面以更新UI
            // 注销时解绑registerBtn的点击事件，防止注册弹窗闪现
            if (registerBtn) {
                registerBtn.onclick = null;
                registerBtn.removeEventListener('click', openRegisterModal);
            }
            // 注销时关闭所有模态框，防止注册弹窗闪现
            closeModals();
            // 显示注销弹窗
            const modal = document.getElementById('logoutSuccessModal');
            if (modal) {
                modal.classList.add('show');
                setTimeout(() => {
                    modal.classList.remove('show');
                    window.location.reload();
                }, 1200);
            } else {
                window.location.reload();
            }
        });
    } else {
        // 没有token，直接清除本地存储
        localStorage.removeItem('auth_token');
        localStorage.removeItem('username');
        localStorage.removeItem('role');
        
        // 刷新页面以更新UI
        window.location.reload();
    }
}

// 事件监听器现在在bindAuthEvents函数中处理

// 获取当前用户信息
function getCurrentUser() {
    const token = localStorage.getItem('auth_token');
    const username = localStorage.getItem('username');
    const role = localStorage.getItem('role');
    
    if (token && username && role) {
        return {
            username: username,
            role: role,
            token: token
        };
    }
    return null;
}

// 显示登录状态提示框
function showLoginStatusModal(message) {
    const modal = document.getElementById('loginStatusModal');
    const textElement = document.getElementById('loginStatusText');

    if (modal && textElement) {
        textElement.textContent = message;
        modal.style.display = 'flex';

        // 1.5秒后自动关闭，和其他提示框保持一致
        setTimeout(() => {
            modal.style.display = 'none';
        }, 1500);
    }
}

// 关闭登录状态提示框
function closeLoginStatusModal() {
    const modal = document.getElementById('loginStatusModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// 显示登录错误提示框
function showLoginErrorModal(message) {
    const modal = document.getElementById('loginErrorModal');
    const textElement = document.getElementById('loginErrorText');

    if (modal && textElement) {
        textElement.textContent = message;
        modal.style.display = 'flex';

        // 1.5秒后自动关闭，和其他提示框保持一致
        setTimeout(() => {
            modal.style.display = 'none';
        }, 1500);
    }
}

// 确保函数在全局范围内可用
window.closeLoginStatusModal = closeLoginStatusModal;
window.showLoginStatusModal = showLoginStatusModal;

 