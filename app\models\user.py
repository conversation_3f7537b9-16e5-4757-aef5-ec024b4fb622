import time
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash
from .database import db

class User(db.Model):
    """用户模型，包含患者和医生"""
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(50), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)  # 增加长度以容纳哈希值
    role = db.Column(db.String(20), nullable=False, default='patient')  # 'patient' 或 'doctor'
    license_number = db.Column(db.String(50), nullable=True)  # 医生执照号
    created_at = db.Column(db.DateTime(0), default=datetime.utcnow)  # 使用datetime(0)精度
    last_login = db.Column(db.DateTime(0), nullable=True)  # 使用datetime(0)精度
    
    def __init__(self, username, password, role='patient', license_number=None):
        self.username = username
        self.set_password(password)
        self.role = role
        self.license_number = license_number
        self.created_at = datetime.utcnow()
    
    def set_password(self, password):
        """设置密码哈希"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """验证密码"""
        return check_password_hash(self.password_hash, password)
    
    def update_last_login(self):
        """更新最后登录时间"""
        try:
            # 使用与数据库类型匹配的日期时间格式
            self.last_login = datetime.utcnow().replace(microsecond=0)
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            import logging
            logging.error(f"更新登录时间失败: {str(e)}")
            raise Exception(f"更新登录时间失败: {str(e)}")
    
    def to_dict(self):
        """将用户信息转换为字典"""
        return {
            'id': self.id,
            'username': self.username,
            'role': self.role,
            'license_number': self.license_number,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }

class UserProfile(db.Model):
    __tablename__ = 'user_profiles'

    profile_id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='资料ID')
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, comment='用户ID')
    avatar = db.Column(db.String(255), comment='头像URL')
    nickname = db.Column(db.String(50), comment='昵称')
    gender = db.Column(db.String(10), comment='性别')
    age = db.Column(db.Integer, comment='年龄')
    birth_date = db.Column(db.Date, comment='出生日期')
    height = db.Column(db.Numeric(5, 2), comment='身高(cm)')
    weight = db.Column(db.Numeric(5, 2), comment='体重(kg)')
    blood_type = db.Column(db.String(10), comment='血型')
    phone = db.Column(db.String(20), comment='手机号')
    email = db.Column(db.String(100), comment='邮箱')
    address = db.Column(db.String(255), comment='地址')
    emergency_contact = db.Column(db.String(50), comment='紧急联系人')
    emergency_phone = db.Column(db.String(20), comment='紧急联系人电话')
    allergies = db.Column(db.Text, comment='过敏史')
    medical_history = db.Column(db.Text, comment='既往病史')
    introduction = db.Column(db.Text, comment='个人简介')
    created_at = db.Column(db.DateTime, comment='创建时间')
    updated_at = db.Column(db.DateTime, comment='更新时间')

    def to_dict(self):
        return {
            'profile_id': self.profile_id,
            'user_id': self.user_id,
            'avatar': self.avatar,
            'nickname': self.nickname,
            'gender': self.gender,
            'age': self.age,
            'birth_date': str(self.birth_date) if self.birth_date else None,
            'height': float(self.height) if self.height else None,
            'weight': float(self.weight) if self.weight else None,
            'blood_type': self.blood_type,
            'phone': self.phone,
            'email': self.email,
            'address': self.address,
            'emergency_contact': self.emergency_contact,
            'emergency_phone': self.emergency_phone,
            'allergies': self.allergies,
            'medical_history': self.medical_history,
            'introduction': self.introduction,
            'created_at': str(self.created_at) if self.created_at else None,
            'updated_at': str(self.updated_at) if self.updated_at else None,
        }
