/* 聊天输入区域样式 */

/* 输入区域 */
.chat-input-container {
    border-top: 1px solid var(--gray-200);
    padding: 1rem 1.5rem;
    display: flex;
    background: var(--white);
    gap: 0.75rem;
    box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.05);
}

.chat-input {
    flex: 1;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-lg);
    padding: 0.875rem 1rem;
    resize: none;
    min-height: 44px;
    max-height: 120px;
    outline: none;
    background: var(--white);
    color: var(--gray-800);
    transition: all var(--duration-normal) ease;
    line-height: 1.5;
    font-family: inherit;
    font-size: 0.95rem;
}

.chat-input::placeholder {
    color: var(--gray-400);
}

.chat-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(44, 82, 130, 0.1);
}

.send-button {
    background: var(--primary-color);
    color: var(--white);
    border-radius: var(--radius-lg);
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--duration-normal) ease;
    box-shadow: var(--shadow-sm);
    flex-shrink: 0;
}

.send-button:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.send-button:active {
    transform: translateY(0) scale(0.95);
}

.send-button:disabled {
    background: var(--gray-300);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.send-button svg {
    width: 20px;
    height: 20px;
    fill: none;
    stroke: white;
    stroke-width: 2;
} 