/* 导航栏样式 */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--gray-200);
    transition: all var(--duration-normal) ease;
}

.navbar.scrolled {
    background: var(--white);
    box-shadow: var(--shadow-sm);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
}

.nav-logo {
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--primary-color);
    letter-spacing: -0.025em;
}

.nav-menu {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.nav-link {
    color: var(--gray-700);
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 0;
    position: relative;
    transition: color var(--duration-normal) ease;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-color);
    border-radius: 1px;
}

.nav-buttons {
    display: flex;
    gap: 1rem;
    position: relative;
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.mobile-menu-toggle span {
    width: 24px;
    height: 2px;
    background: var(--gray-700);
    transition: all var(--duration-normal) ease;
}

.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
} 

/* 个人资料下拉样式 */
.profile-dropdown-content {
  display: none;
  position: absolute;
  right: 0;
  top: 110%;
  background: #fff;
  border: 1px solid #e0e0e0;
  box-shadow: 0 8px 32px rgba(0,0,0,0.12);
  min-width: 320px;
  z-index: 9999;
  padding: 22px 24px 22px 24px;
  border-radius: 14px;
  font-size: 15px;
  transition: box-shadow 0.2s;
  animation: dropdown-fade-in 0.25s;
}
@keyframes dropdown-fade-in {
  from { opacity: 0; transform: translateY(-10px);}
  to   { opacity: 1; transform: translateY(0);}
}
#profile-entry {
  font-weight: bold;
  color: #1976d2;
  padding: 10px 0 14px 0;
  cursor: pointer;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 10px;
  transition: color 0.2s;
  font-size: 16px;
  letter-spacing: 0.5px;
}
#profile-entry:hover {
  color: #1565c0;
  background: #f5f7fa;
  border-radius: 6px;
}
#close-profile-table {
  color: #888;
  transition: color 0.2s;
  margin-left: 10px;
}
#close-profile-table:hover {
  color: #d32f2f;
}
.profile-dropdown-content table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 12px;
  background: #fafbfc;
  border-radius: 8px;
  overflow: hidden;
  font-size: 15px;
}
.profile-dropdown-content th, .profile-dropdown-content td {
  padding: 10px 12px;
  border-bottom: 1px solid #f0f0f0;
}
.profile-dropdown-content th {
  background: #f5f7fa;
  color: #333;
  font-weight: 600;
  text-align: left;
}
.profile-dropdown-content tr:last-child td {
  border-bottom: none;
}
.profile-dropdown-content tr:hover td {
  background: #f0f6ff;
}
.profile-dropdown-content input[type='text'],
.profile-dropdown-content textarea {
  width: 95%;
  padding: 6px 8px;
  border: 1px solid #d0d7de;
  border-radius: 5px;
  font-size: 15px;
  background: #fff;
  transition: border 0.2s;
}
.profile-input,
.profile-input.profile-readonly {
  width: 100%;
  min-height: 36px;
  height: 36px;
  font-size: 15px;
  font-family: inherit;
  line-height: 1.5;
  padding: 6px 10px;
  border: 1px solid #d0d7de;   /* 始终1px */
  border-radius: 5px;
  background: #fff;
  box-sizing: border-box;
  resize: none;
  transition: border-color 0.2s, background 0.2s;
  vertical-align: middle;
  display: block;
}
.profile-input:focus {
  border: 1px solid #1976d2;   /* 只变色，不变宽 */
  outline: none;
  background: #f5faff;
}
.profile-readonly {
  background: #fafbfc;
  color: #333;
  pointer-events: none;
  user-select: text;
  white-space: pre-wrap;
  overflow: auto;
}
.profile-input.profile-readonly:empty::before {
  content: ' ';
  display: inline-block;
  height: 100%;
}
.profile-dropdown-content input[type='text']:focus,
.profile-dropdown-content textarea:focus {
  border: 1.5px solid #1976d2;
  outline: none;
  background: #f5faff;
}
.profile-dropdown-content button {
  background: #1976d2;
  color: #fff;
  border: none;
  border-radius: 5px;
  padding: 6px 18px;
  margin-left: 8px;
  font-size: 15px;
  cursor: pointer;
  transition: background 0.2s;
}
.profile-dropdown-content button:disabled {
  background: #b0bec5;
  cursor: not-allowed;
}
.profile-dropdown-content button:hover:not(:disabled) {
  background: #1565c0;
} 

.custom-modal.show {
  display: flex !important;
  position: fixed;
  z-index: 99999;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.5);
  align-items: center;
  justify-content: center;
  animation: modal-fade-in 0.25s;
}
@keyframes modal-fade-in {
  from { opacity: 0; }
  to   { opacity: 1; }
}
.custom-modal-content {
  background: #fff;
  border-radius: 14px;
  padding: 36px 44px;
  min-width: 280px;
  min-height: 60px;
  font-size: 18px;
  color: #1976d2;
  box-shadow: 0 8px 32px rgba(0,0,0,0.18);
  margin: auto;
  text-align: center;
  font-family: 'Inter', 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
  letter-spacing: 0.5px;
  font-weight: 600;
  animation: modal-content-fade-in 0.25s;
}
@keyframes modal-content-fade-in {
  from { transform: scale(0.98); opacity: 0.7; }
  to   { transform: scale(1); opacity: 1; }
}
#notLoggedInText {
  color: #1976d2;
  font-weight: 600;
  font-size: 19px;
  letter-spacing: 1px;
} 