from flask import Blueprint, request, jsonify
import logging
from ..services.intent_recognition_service import IntentRecognitionService

# Setup logger
logger = logging.getLogger(__name__)

intent_bp = Blueprint('intent', __name__)

# 初始化意图识别服务
intent_service = IntentRecognitionService()

@intent_bp.route('/api/intent/recognize', methods=['POST'])
def recognize_intent():
    """
    意图识别API端点
    
    Request body:
    {
        "message": "用户输入的消息"
    }
    
    Response:
    {
        "intent": "knowledge_query|tool_use|general_chat",
        "confidence": 0.0-1.0,
        "reasoning": "判断理由",
        "description": "意图的中文描述",
        "status": "success"
    }
    """
    try:
        data = request.json
        user_message = data.get('message', '')
        
        if not user_message:
            return jsonify({
                'status': 'error',
                'error': 'Empty message'
            }), 400
        
        logger.info(f"Intent recognition request for message: {user_message[:50]}...")
        
        # 调用意图识别服务
        intent_result = intent_service.recognize_intent(user_message)
        
        # 添加意图描述
        intent_result['description'] = intent_service.get_intent_description(intent_result['intent'])
        intent_result['status'] = 'success'
        
        logger.info(f"Intent recognition result: {intent_result['intent']} (confidence: {intent_result['confidence']})")
        
        return jsonify(intent_result)
        
    except Exception as e:
        logger.error(f"Error in intent recognition endpoint: {str(e)}")
        return jsonify({
            'status': 'error',
            'error': str(e),
            'intent': 'general_chat',
            'confidence': 0.3,
            'reasoning': '意图识别服务出错，使用默认意图',
            'description': '一般聊天'
        }), 500
