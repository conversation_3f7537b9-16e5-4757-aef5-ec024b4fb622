<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天界面演示 - 慧问医答</title>
    <style>
        /* 导入优化后的聊天样式 */
        @import url('./components/chat/styles.html');
        
        /* 演示用的基础样式 */
        body {
            margin: 0;
            padding: 0;
            font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
        }
        
        .demo-container {
            width: 100%;
            height: 100vh;
            display: flex;
        }
        
        /* 演示消息样式 */
        .demo-messages {
            padding: 20px 0;
        }
        
        .demo-message {
            margin-bottom: 32px;
            max-width: 80%;
            animation: messageSlideIn 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
        }
        
        .demo-user-message {
            display: flex;
            flex-direction: row-reverse;
            margin-left: auto;
            align-items: flex-end;
        }
        
        .demo-bot-message {
            display: flex;
            flex-direction: row;
            align-items: flex-end;
        }
        
        .demo-avatar {
            width: 44px;
            height: 44px;
            border-radius: 50%;
            background: #3b82f6;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
            flex-shrink: 0;
        }
        
        .demo-bot-message .demo-avatar {
            background: #10b981;
            margin-right: 16px;
        }
        
        .demo-user-message .demo-avatar {
            background: #3b82f6;
            margin-left: 16px;
        }
        
        .demo-content {
            padding: 18px 24px;
            border-radius: 20px;
            position: relative;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
        }
        
        .demo-user-message .demo-content {
            background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
            color: white;
            border-top-right-radius: 8px;
        }
        
        .demo-bot-message .demo-content {
            background: white;
            color: #1e293b;
            border-top-left-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        
        .demo-input-area {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 24px 32px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 -8px 32px rgba(0, 0, 0, 0.08);
            border-radius: 24px 24px 0 0;
        }
        
        .demo-input-container {
            display: flex;
            align-items: flex-end;
            gap: 16px;
        }
        
        .demo-input {
            flex-grow: 1;
            border: 2px solid #e2e8f0;
            border-radius: 20px;
            padding: 16px 20px;
            font-size: 15px;
            font-weight: 500;
            outline: none;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
        }
        
        .demo-input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
            background: white;
        }
        
        .demo-send-btn {
            width: 52px;
            height: 52px;
            background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .demo-send-btn:hover {
            background: linear-gradient(135deg, #1d4ed8 0%, #3b82f6 100%);
            box-shadow: 0 12px 24px rgba(59, 130, 246, 0.4);
            transform: translateY(-3px) scale(1.05);
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>慧问医答</h2>
            </div>
            <div class="sidebar-actions">
                <button class="btn primary-btn full-width">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"/>
                    </svg>
                    新建对话
                </button>
                <button class="btn secondary-btn full-width">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9zm-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8H12z"/>
                    </svg>
                    历史记录
                </button>
            </div>
            <div class="sidebar-content">
                <div class="history-item active">
                    <div class="history-item-title">头痛症状咨询</div>
                    <div class="history-item-preview">医生您好，我最近经常头痛，想咨询一下可能的原因...</div>
                    <div class="history-item-meta">
                        <span>今天 14:30</span>
                        <span>5条消息</span>
                    </div>
                </div>
                <div class="history-item">
                    <div class="history-item-title">体检报告解读</div>
                    <div class="history-item-preview">请帮我看看这份体检报告，有什么需要注意的地方吗？</div>
                    <div class="history-item-meta">
                        <span>昨天 16:45</span>
                        <span>8条消息</span>
                    </div>
                </div>
                <div class="history-item">
                    <div class="history-item-title">用药咨询</div>
                    <div class="history-item-preview">关于高血压药物的服用时间和注意事项...</div>
                    <div class="history-item-meta">
                        <span>3天前</span>
                        <span>12条消息</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 主聊天区域 -->
        <div class="main-content">
            <div class="chat-page">
                <!-- 聊天头部 -->
                <div class="chat-header">
                    <div class="chat-header-title">
                        <h1>智能医疗助手</h1>
                        <p>为您提供专业的医疗咨询服务</p>
                    </div>
                    <div class="chat-actions">
                        <button class="btn secondary-btn">
                            <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                            </svg>
                            清空对话
                        </button>
                    </div>
                </div>
                
                <!-- 聊天内容区域 -->
                <div class="chat-container">
                    <div class="demo-messages">
                        <!-- 机器人消息 -->
                        <div class="demo-message demo-bot-message">
                            <div class="demo-avatar">AI</div>
                            <div class="demo-content">
                                <div class="message-text">您好！我是慧问医答智能助手，很高兴为您服务。我可以帮您解答医疗健康相关的问题，提供专业的建议和指导。请问有什么可以帮助您的吗？</div>
                                <div class="message-time">14:28</div>
                            </div>
                        </div>
                        
                        <!-- 用户消息 -->
                        <div class="demo-message demo-user-message">
                            <div class="demo-avatar">我</div>
                            <div class="demo-content">
                                <div class="message-text">医生您好，我最近经常头痛，特别是下午的时候，想咨询一下可能的原因和缓解方法。</div>
                                <div class="message-time">14:30</div>
                            </div>
                        </div>
                        
                        <!-- 机器人回复 -->
                        <div class="demo-message demo-bot-message">
                            <div class="demo-avatar">AI</div>
                            <div class="demo-content">
                                <div class="message-text">感谢您的咨询。下午头痛可能有多种原因：

1. **紧张性头痛** - 工作压力、长时间用眼等
2. **血糖波动** - 午餐后血糖变化
3. **脱水** - 下午水分摄入不足
4. **颈椎问题** - 长期低头工作导致

建议您：
• 保持规律作息，适当休息
• 多喝水，保持充足水分
• 做颈部放松运动
• 如症状持续，建议就医检查

请问头痛持续多长时间了？有其他伴随症状吗？</div>
                                <div class="message-time">14:32</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 输入区域 -->
                <div class="demo-input-area">
                    <div class="demo-input-container">
                        <input type="text" class="demo-input" placeholder="请输入您的问题..." />
                        <button class="demo-send-btn">
                            <svg width="22" height="22" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
