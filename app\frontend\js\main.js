// 主页功能脚本
// 主页无需加载动画，直接显示内容

// 初始化所有功能的主函数
function initializeApp() {
    console.log('开始初始化应用功能...');
    // 移动端菜单切换
    const mobileMenuToggle = document.getElementById('mobileMenuToggle');
    const navMenu = document.querySelector('.nav-menu');
    
    if (mobileMenuToggle) {
        mobileMenuToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            this.classList.toggle('active');
        });
    }

    // 导航栏滚动效果
    const navbar = document.querySelector('.navbar');
    let lastScrollY = window.scrollY;

    window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }

        // 更新活跃导航链接
        updateActiveNavLink();
    });

    // 平滑滚动到锚点
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const offsetTop = targetSection.offsetTop - 80;
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });

    // 数字动画
    animateNumbers();

    // FAQ功能
    initFAQ();

    // 评价轮播
    initTestimonialSlider();

    // 卡片动画观察器
    initScrollAnimations();

    // 个人资料下拉框
    document.addEventListener('DOMContentLoaded', function() {
      const dropdownContent = document.getElementById('profile-dropdown-content');
      const loginBtn = document.getElementById('loginBtn');
      if (!dropdownContent || !loginBtn) return;

      // 让下拉内容定位到登录按钮下方
      loginBtn.style.position = 'relative';
      dropdownContent.style.position = 'absolute';
      dropdownContent.style.right = '0';
      dropdownContent.style.top = (loginBtn.offsetHeight + 5) + 'px';

      // 悬停显示下拉
      loginBtn.addEventListener('mouseenter', function() {
        dropdownContent.style.display = 'block';
      });
      // 鼠标移出时隐藏
      loginBtn.addEventListener('mouseleave', function() {
        setTimeout(() => {
          dropdownContent.style.display = 'none';
        }, 200);
      });
      dropdownContent.addEventListener('mouseenter', function() {
        dropdownContent.style.display = 'block';
      });
      dropdownContent.addEventListener('mouseleave', function() {
        dropdownContent.style.display = 'none';
      });
    });
}

// 更新活跃导航链接
function updateActiveNavLink() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link');
    
    let currentSection = '';
    
    sections.forEach(section => {
        const sectionTop = section.offsetTop - 100;
        const sectionHeight = section.offsetHeight;
        
        if (window.scrollY >= sectionTop && window.scrollY < sectionTop + sectionHeight) {
            currentSection = section.getAttribute('id');
        }
    });

    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${currentSection}`) {
            link.classList.add('active');
        }
    });
}

// 数字动画
function animateNumbers() {
    const statNumbers = document.querySelectorAll('[data-target]');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const target = parseInt(entry.target.getAttribute('data-target'));
                animateNumber(entry.target, target);
                observer.unobserve(entry.target);
            }
        });
    });

    statNumbers.forEach(number => observer.observe(number));
}

function animateNumber(element, target) {
    let current = 0;
    const increment = target / 100;
    const duration = 2000;
    const stepTime = duration / 100;

    const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }
        
        element.textContent = Math.floor(current).toLocaleString();
    }, stepTime);
}

// FAQ功能
function initFAQ() {
    const faqItems = document.querySelectorAll('.faq-item');
    
    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        const answer = item.querySelector('.faq-answer');
        const toggle = item.querySelector('.faq-toggle');
        
        question.addEventListener('click', () => {
            const isActive = item.classList.contains('active');
            
            // 关闭所有其他FAQ
            faqItems.forEach(otherItem => {
                otherItem.classList.remove('active');
                otherItem.querySelector('.faq-toggle').textContent = '+';
            });
            
            // 切换当前FAQ
            if (!isActive) {
                item.classList.add('active');
                toggle.textContent = '−';
            }
        });
    });
}

// 评价轮播
function initTestimonialSlider() {
    const testimonials = document.querySelectorAll('.testimonial-card');
    const dots = document.querySelectorAll('.dot');
    const prevBtn = document.getElementById('prevTestimonial');
    const nextBtn = document.getElementById('nextTestimonial');
    let currentSlide = 0;

    function showSlide(index) {
        testimonials.forEach((testimonial, i) => {
            testimonial.classList.toggle('active', i === index);
        });
        
        dots.forEach((dot, i) => {
            dot.classList.toggle('active', i === index);
        });
        
        currentSlide = index;
    }

    // 点击导航点
    dots.forEach((dot, index) => {
        dot.addEventListener('click', () => showSlide(index));
    });

    // 上一个/下一个按钮
    if (prevBtn) {
        prevBtn.addEventListener('click', () => {
            const prevIndex = currentSlide > 0 ? currentSlide - 1 : testimonials.length - 1;
            showSlide(prevIndex);
        });
    }

    if (nextBtn) {
        nextBtn.addEventListener('click', () => {
            const nextIndex = currentSlide < testimonials.length - 1 ? currentSlide + 1 : 0;
            showSlide(nextIndex);
        });
    }

    // 自动轮播
    setInterval(() => {
        const nextIndex = currentSlide < testimonials.length - 1 ? currentSlide + 1 : 0;
        showSlide(nextIndex);
    }, 5000);
}

// 滚动动画
function initScrollAnimations() {
    const animatedElements = document.querySelectorAll('.feature-card, .doctor-card, .stat-card, .step-item');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    animatedElements.forEach(element => observer.observe(element));
    
    console.log('应用功能初始化完成！');
}