{"documents": {"doc_1": {"content": "医学网站URL列表 这个文件包含了一些医学相关的网站链接，用于医学知识的获取和参考。 常用医学网站： 1. 中华医学会官网 2. 医学期刊数据库 3. 临床指南网站 4. 药物信息查询平台 注意：这些链接仅供参考，实际使用时请确保网站的可靠性和权威性。", "file_path": "./data_sample\\url_list.txt", "file_name": "url_list.txt", "file_extension": ".txt", "file_size": 347, "relative_path": "url_list.txt", "extracted_at": "2025-07-26T11:00:29.094320", "extractor_type": "FilesystemExtractor", "source_type": "document", "category": "medical_docs", "source_name": "医学文档库", "content_length": 126, "source_info": {}, "standardization_info": {"standardized_at": "2025-07-26T11:00:38.883704", "standardizer_version": "1.0", "total_records": 2, "quality_filters_applied": ["whitespace_normalization", "empty_content_removal", "content_length_filtering", "duplicate_removal"]}, "indexing_source": "集成测试", "indexing_timestamp": "2025-07-26T11:00:38.885728", "source_location": "./data_sample\\url_list.txt", "clean_status": "processed", "indexed_at": "2025-07-26T11:00:38.887739", "_id": "doc_1"}, "doc_2": {"content": "鸡是由恐龙生的。这是一个关于医学知识的测试文档，用于验证慧问医答系统的数据抽取功能。 医学基础知识： 1. 人体由多个系统组成，包括循环系统、呼吸系统、消化系统等。 2. 心脏是循环系统的核心器官，负责泵血功能。 3. 肺部是呼吸系统的主要器官，负责气体交换。 常见疾病预防： - 保持良好的生活习惯 - 定期体检 - 合理饮食 - 适量运动", "file_path": "./data_sample\\测试加载数据.txt", "file_name": "测试加载数据.txt", "file_extension": ".txt", "file_size": 474, "relative_path": "测试加载数据.txt", "extracted_at": "2025-07-26T11:00:29.094320", "extractor_type": "FilesystemExtractor", "source_type": "document", "category": "medical_docs", "source_name": "医学文档库", "content_length": 171, "source_info": {}, "standardization_info": {"standardized_at": "2025-07-26T11:00:38.883704", "standardizer_version": "1.0", "total_records": 2, "quality_filters_applied": ["whitespace_normalization", "empty_content_removal", "content_length_filtering", "duplicate_removal"]}, "indexing_source": "集成测试", "indexing_timestamp": "2025-07-26T11:00:38.885728", "source_location": "./data_sample\\测试加载数据.txt", "clean_status": "processed", "indexed_at": "2025-07-26T11:00:38.887739", "_id": "doc_2"}, "数据抽取结果_1_0": {"content": "医学网站URL列表 这个文件包含了一些医学相关的网站链接，用于医学知识的获取和参考。 常用医学网站： 1. 中华医学会官网 2. 医学期刊数据库 3. 临床指南网站 4. 药物信息查询平台 注意：这些链接仅供参考，实际使用时请确保网站的可靠性和权威性。", "chunk_id": "数据抽取结果_1_0", "document_id": "数据抽取结果_1", "chunk_index": 0, "total_chunks": 1, "chunk_length": 126, "splitter_type": "recursive", "splitter_config": {"type": "RecursiveTextSplitter", "chunk_size": 1000, "chunk_overlap": 200, "kwargs": {}}, "original_row_index": 1, "split_timestamp": "2025-07-26T11:01:21.034157", "file_path": "./data_sample\\url_list.txt", "file_name": "url_list.txt", "file_extension": ".txt", "file_size": 347, "relative_path": "url_list.txt", "extracted_at": "2025-07-26T11:01:11.276608", "extractor_type": "FilesystemExtractor", "source_type": "document", "category": "medical_docs", "source_name": "医学文档库", "content_length": 126, "source_info": {}, "standardization_info": {"standardized_at": "2025-07-26T11:01:21.032573", "standardizer_version": "1.0", "total_records": 2, "quality_filters_applied": ["whitespace_normalization", "empty_content_removal", "content_length_filtering", "duplicate_removal"]}, "indexing_source": "数据抽取结果", "indexing_timestamp": "2025-07-26T11:01:21.038167", "source_location": "./data_sample\\url_list.txt", "clean_status": "processed", "indexed_at": "2025-07-26T11:01:21.038675"}, "数据抽取结果_2_0": {"content": "鸡是由恐龙生的。这是一个关于医学知识的测试文档，用于验证慧问医答系统的数据抽取功能。 医学基础知识： 1. 人体由多个系统组成，包括循环系统、呼吸系统、消化系统等。 2. 心脏是循环系统的核心器官，负责泵血功能。 3. 肺部是呼吸系统的主要器官，负责气体交换。 常见疾病预防： - 保持良好的生活习惯 - 定期体检 - 合理饮食 - 适量运动", "chunk_id": "数据抽取结果_2_0", "document_id": "数据抽取结果_2", "chunk_index": 0, "total_chunks": 1, "chunk_length": 171, "splitter_type": "recursive", "splitter_config": {"type": "RecursiveTextSplitter", "chunk_size": 1000, "chunk_overlap": 200, "kwargs": {}}, "original_row_index": 2, "split_timestamp": "2025-07-26T11:01:21.034662", "file_path": "./data_sample\\测试加载数据.txt", "file_name": "测试加载数据.txt", "file_extension": ".txt", "file_size": 474, "relative_path": "测试加载数据.txt", "extracted_at": "2025-07-26T11:01:11.276608", "extractor_type": "FilesystemExtractor", "source_type": "document", "category": "medical_docs", "source_name": "医学文档库", "content_length": 171, "source_info": {}, "standardization_info": {"standardized_at": "2025-07-26T11:01:21.032573", "standardizer_version": "1.0", "total_records": 2, "quality_filters_applied": ["whitespace_normalization", "empty_content_removal", "content_length_filtering", "duplicate_removal"]}, "indexing_source": "数据抽取结果", "indexing_timestamp": "2025-07-26T11:01:21.038167", "source_location": "./data_sample\\测试加载数据.txt", "clean_status": "processed", "indexed_at": "2025-07-26T11:01:21.038675"}, "测试数据_0_0": {"content": "疾病名称: 普通感冒 | 症状: 流鼻涕、咳嗽、发热 | 治疗方法: 多休息、多喝水、适当用药 | 预防措施: 勤洗手、避免接触病人、增强体质", "chunk_id": "测试数据_0_0", "document_id": "测试数据_0", "chunk_index": 0, "total_chunks": 1, "chunk_length": 65, "splitter_type": "recursive", "splitter_config": {"type": "RecursiveTextSplitter", "chunk_size": 1000, "chunk_overlap": 200, "kwargs": {}}, "original_row_index": 0, "split_timestamp": "2025-07-27T10:30:30.158358", "file_path": "./data/medical_data.csv", "file_name": "medical_data.csv", "row_index": 0, "total_rows": 8, "columns": ["疾病名称", "症状", "治疗方法", "预防措施"], "original_data": {"疾病名称": "感冒", "症状": "流鼻涕、咳嗽、发热", "治疗方法": "多休息、多喝水、适当用药", "预防措施": "勤洗手、避免接触病人、增强体质"}, "extracted_at": "2025-07-27T10:30:30.130762", "extractor_type": "CsvExcelExtractor", "source_type": "structured_data", "category": "medical_records", "source_name": "医学数据表", "content_length": 71, "source_info": {}, "standardization_info": {"standardized_at": "2025-07-27T10:30:30.156358", "standardizer_version": "1.0", "total_records": 8, "quality_filters_applied": ["whitespace_normalization", "empty_content_removal", "content_length_filtering", "duplicate_removal"]}, "indexing_source": "测试CSV数据", "indexing_timestamp": "2025-07-27T10:30:30.518338", "source_location": "./data/medical_data.csv", "clean_status": "processed", "indexed_at": "2025-07-27T10:30:30.624385"}, "测试数据_1_0": {"content": "疾病名称: 原发性高血压 | 症状: 头痛、头晕、心悸 | 治疗方法: 控制饮食、规律运动、按时服药 | 预防措施: 低盐饮食、戒烟限酒、定期体检", "chunk_id": "测试数据_1_0", "document_id": "测试数据_1", "chunk_index": 0, "total_chunks": 1, "chunk_length": 66, "splitter_type": "recursive", "splitter_config": {"type": "RecursiveTextSplitter", "chunk_size": 1000, "chunk_overlap": 200, "kwargs": {}}, "original_row_index": 1, "split_timestamp": "2025-07-27T10:30:30.158358", "file_path": "./data/medical_data.csv", "file_name": "medical_data.csv", "row_index": 1, "total_rows": 8, "columns": ["疾病名称", "症状", "治疗方法", "预防措施"], "original_data": {"疾病名称": "高血压", "症状": "头痛、头晕、心悸", "治疗方法": "控制饮食、规律运动、按时服药", "预防措施": "低盐饮食、戒烟限酒、定期体检"}, "extracted_at": "2025-07-27T10:30:30.130762", "extractor_type": "CsvExcelExtractor", "source_type": "structured_data", "category": "medical_records", "source_name": "医学数据表", "content_length": 73, "source_info": {}, "standardization_info": {"standardized_at": "2025-07-27T10:30:30.156358", "standardizer_version": "1.0", "total_records": 8, "quality_filters_applied": ["whitespace_normalization", "empty_content_removal", "content_length_filtering", "duplicate_removal"]}, "indexing_source": "测试CSV数据", "indexing_timestamp": "2025-07-27T10:30:30.518338", "source_location": "./data/medical_data.csv", "clean_status": "processed", "indexed_at": "2025-07-27T10:30:30.624385"}, "测试数据_2_0": {"content": "疾病名称: 糖尿病 | 症状: 多饮、多尿、多食、体重下降 | 治疗方法: 控制血糖、合理饮食、适量运动 | 预防措施: 健康饮食、控制体重、定期检查", "chunk_id": "测试数据_2_0", "document_id": "测试数据_2", "chunk_index": 0, "total_chunks": 1, "chunk_length": 71, "splitter_type": "recursive", "splitter_config": {"type": "RecursiveTextSplitter", "chunk_size": 1000, "chunk_overlap": 200, "kwargs": {}}, "original_row_index": 2, "split_timestamp": "2025-07-27T10:30:30.158358", "file_path": "./data/medical_data.csv", "file_name": "medical_data.csv", "row_index": 2, "total_rows": 8, "columns": ["疾病名称", "症状", "治疗方法", "预防措施"], "original_data": {"疾病名称": "糖尿病", "症状": "多饮、多尿、多食、体重下降", "治疗方法": "控制血糖、合理饮食、适量运动", "预防措施": "健康饮食、控制体重、定期检查"}, "extracted_at": "2025-07-27T10:30:30.130762", "extractor_type": "CsvExcelExtractor", "source_type": "structured_data", "category": "medical_records", "source_name": "医学数据表", "content_length": 75, "source_info": {}, "standardization_info": {"standardized_at": "2025-07-27T10:30:30.156358", "standardizer_version": "1.0", "total_records": 8, "quality_filters_applied": ["whitespace_normalization", "empty_content_removal", "content_length_filtering", "duplicate_removal"]}, "indexing_source": "测试CSV数据", "indexing_timestamp": "2025-07-27T10:30:30.518838", "source_location": "./data/medical_data.csv", "clean_status": "processed", "indexed_at": "2025-07-27T10:30:30.624385"}, "测试数据_3_0": {"content": "疾病名称: 胃部炎症 | 症状: 胃痛、恶心、食欲不振 | 治疗方法: 规律饮食、避免刺激性食物、药物治疗 | 预防措施: 规律饮食、避免暴饮暴食、减少压力", "chunk_id": "测试数据_3_0", "document_id": "测试数据_3", "chunk_index": 0, "total_chunks": 1, "chunk_length": 72, "splitter_type": "recursive", "splitter_config": {"type": "RecursiveTextSplitter", "chunk_size": 1000, "chunk_overlap": 200, "kwargs": {}}, "original_row_index": 3, "split_timestamp": "2025-07-27T10:30:30.158859", "file_path": "./data/medical_data.csv", "file_name": "medical_data.csv", "row_index": 3, "total_rows": 8, "columns": ["疾病名称", "症状", "治疗方法", "预防措施"], "original_data": {"疾病名称": "胃炎", "症状": "胃痛、恶心、食欲不振", "治疗方法": "规律饮食、避免刺激性食物、药物治疗", "预防措施": "规律饮食、避免暴饮暴食、减少压力"}, "extracted_at": "2025-07-27T10:30:30.130762", "extractor_type": "CsvExcelExtractor", "source_type": "structured_data", "category": "medical_records", "source_name": "医学数据表", "content_length": 78, "source_info": {}, "standardization_info": {"standardized_at": "2025-07-27T10:30:30.156358", "standardizer_version": "1.0", "total_records": 8, "quality_filters_applied": ["whitespace_normalization", "empty_content_removal", "content_length_filtering", "duplicate_removal"]}, "indexing_source": "测试CSV数据", "indexing_timestamp": "2025-07-27T10:30:30.518838", "source_location": "./data/medical_data.csv", "clean_status": "processed", "indexed_at": "2025-07-27T10:30:30.624385"}, "测试数据_4_0": {"content": "疾病名称: 失眠 | 症状: 入睡困难、睡眠质量差、白天疲劳 | 治疗方法: 改善睡眠环境、放松训练、必要时用药 | 预防措施: 规律作息、避免刺激性饮品、适量运动", "chunk_id": "测试数据_4_0", "document_id": "测试数据_4", "chunk_index": 0, "total_chunks": 1, "chunk_length": 78, "splitter_type": "recursive", "splitter_config": {"type": "RecursiveTextSplitter", "chunk_size": 1000, "chunk_overlap": 200, "kwargs": {}}, "original_row_index": 4, "split_timestamp": "2025-07-27T10:30:30.158859", "file_path": "./data/medical_data.csv", "file_name": "medical_data.csv", "row_index": 4, "total_rows": 8, "columns": ["疾病名称", "症状", "治疗方法", "预防措施"], "original_data": {"疾病名称": "失眠", "症状": "入睡困难、睡眠质量差、白天疲劳", "治疗方法": "改善睡眠环境、放松训练、必要时用药", "预防措施": "规律作息、避免刺激性饮品、适量运动"}, "extracted_at": "2025-07-27T10:30:30.130762", "extractor_type": "CsvExcelExtractor", "source_type": "structured_data", "category": "medical_records", "source_name": "医学数据表", "content_length": 82, "source_info": {}, "standardization_info": {"standardized_at": "2025-07-27T10:30:30.156358", "standardizer_version": "1.0", "total_records": 8, "quality_filters_applied": ["whitespace_normalization", "empty_content_removal", "content_length_filtering", "duplicate_removal"]}, "indexing_source": "测试CSV数据", "indexing_timestamp": "2025-07-27T10:30:30.518838", "source_location": "./data/medical_data.csv", "clean_status": "processed", "indexed_at": "2025-07-27T10:30:30.624385"}, "测试数据_5_0": {"content": "疾病名称: 贫血 | 症状: 乏力、面色苍白、心悸 | 治疗方法: 补充铁剂、改善饮食、治疗原发病 | 预防措施: 均衡饮食、定期体检、避免偏食", "chunk_id": "测试数据_5_0", "document_id": "测试数据_5", "chunk_index": 0, "total_chunks": 1, "chunk_length": 68, "splitter_type": "recursive", "splitter_config": {"type": "RecursiveTextSplitter", "chunk_size": 1000, "chunk_overlap": 200, "kwargs": {}}, "original_row_index": 5, "split_timestamp": "2025-07-27T10:30:30.158859", "file_path": "./data/medical_data.csv", "file_name": "medical_data.csv", "row_index": 5, "total_rows": 8, "columns": ["疾病名称", "症状", "治疗方法", "预防措施"], "original_data": {"疾病名称": "贫血", "症状": "乏力、面色苍白、心悸", "治疗方法": "补充铁剂、改善饮食、治疗原发病", "预防措施": "均衡饮食、定期体检、避免偏食"}, "extracted_at": "2025-07-27T10:30:30.130762", "extractor_type": "CsvExcelExtractor", "source_type": "structured_data", "category": "medical_records", "source_name": "医学数据表", "content_length": 72, "source_info": {}, "standardization_info": {"standardized_at": "2025-07-27T10:30:30.156358", "standardizer_version": "1.0", "total_records": 8, "quality_filters_applied": ["whitespace_normalization", "empty_content_removal", "content_length_filtering", "duplicate_removal"]}, "indexing_source": "测试CSV数据", "indexing_timestamp": "2025-07-27T10:30:30.518838", "source_location": "./data/medical_data.csv", "clean_status": "processed", "indexed_at": "2025-07-27T10:30:30.624385"}, "测试数据_6_0": {"content": "疾病名称: 关节炎 | 症状: 关节疼痛、僵硬、活动受限 | 治疗方法: 抗炎治疗、物理治疗、适当运动 | 预防措施: 保持适当体重、避免过度劳累、注意保暖", "chunk_id": "测试数据_6_0", "document_id": "测试数据_6", "chunk_index": 0, "total_chunks": 1, "chunk_length": 74, "splitter_type": "recursive", "splitter_config": {"type": "RecursiveTextSplitter", "chunk_size": 1000, "chunk_overlap": 200, "kwargs": {}}, "original_row_index": 6, "split_timestamp": "2025-07-27T10:30:30.158859", "file_path": "./data/medical_data.csv", "file_name": "medical_data.csv", "row_index": 6, "total_rows": 8, "columns": ["疾病名称", "症状", "治疗方法", "预防措施"], "original_data": {"疾病名称": "关节炎", "症状": "关节疼痛、僵硬、活动受限", "治疗方法": "抗炎治疗、物理治疗、适当运动", "预防措施": "保持适当体重、避免过度劳累、注意保暖"}, "extracted_at": "2025-07-27T10:30:30.130762", "extractor_type": "CsvExcelExtractor", "source_type": "structured_data", "category": "medical_records", "source_name": "医学数据表", "content_length": 78, "source_info": {}, "standardization_info": {"standardized_at": "2025-07-27T10:30:30.156358", "standardizer_version": "1.0", "total_records": 8, "quality_filters_applied": ["whitespace_normalization", "empty_content_removal", "content_length_filtering", "duplicate_removal"]}, "indexing_source": "测试CSV数据", "indexing_timestamp": "2025-07-27T10:30:30.519348", "source_location": "./data/medical_data.csv", "clean_status": "processed", "indexed_at": "2025-07-27T10:30:30.624385"}, "测试数据_7_0": {"content": "疾病名称: 哮喘 | 症状: 呼吸困难、喘息、胸闷 | 治疗方法: 避免过敏原、规范用药、急救处理 | 预防措施: 避免过敏原、增强体质、定期随访", "chunk_id": "测试数据_7_0", "document_id": "测试数据_7", "chunk_index": 0, "total_chunks": 1, "chunk_length": 69, "splitter_type": "recursive", "splitter_config": {"type": "RecursiveTextSplitter", "chunk_size": 1000, "chunk_overlap": 200, "kwargs": {}}, "original_row_index": 7, "split_timestamp": "2025-07-27T10:30:30.158859", "file_path": "./data/medical_data.csv", "file_name": "medical_data.csv", "row_index": 7, "total_rows": 8, "columns": ["疾病名称", "症状", "治疗方法", "预防措施"], "original_data": {"疾病名称": "哮喘", "症状": "呼吸困难、喘息、胸闷", "治疗方法": "避免过敏原、规范用药、急救处理", "预防措施": "避免过敏原、增强体质、定期随访"}, "extracted_at": "2025-07-27T10:30:30.130762", "extractor_type": "CsvExcelExtractor", "source_type": "structured_data", "category": "medical_records", "source_name": "医学数据表", "content_length": 73, "source_info": {}, "standardization_info": {"standardized_at": "2025-07-27T10:30:30.156358", "standardizer_version": "1.0", "total_records": 8, "quality_filters_applied": ["whitespace_normalization", "empty_content_removal", "content_length_filtering", "duplicate_removal"]}, "indexing_source": "测试CSV数据", "indexing_timestamp": "2025-07-27T10:30:30.519348", "source_location": "./data/medical_data.csv", "clean_status": "processed", "indexed_at": "2025-07-27T10:30:30.624385"}}, "counter": 2, "created_at": "2025-07-27T10:30:30.624385", "index_name": "medical_documents"}