#!/usr/bin/env python3
"""
数据抽取到DataFrame脚本

专门用于将数据抽取并保存为DataFrame格式，不进行向量存储摄取
"""

import argparse
import sys
import os
import pandas as pd
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.data_extraction_service import DataExtractionService
from app.core.etl.indexing.dual_index_manager import IndexMode


def save_dataframe(df: pd.DataFrame, output_path: str = None, format_type: str = "csv"):
    """保存DataFrame到文件"""
    if df.empty:
        print("没有数据可保存")
        return
    
    if output_path is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        if format_type == "csv":
            output_path = f"extracted_data_{timestamp}.csv"
        elif format_type == "json":
            output_path = f"extracted_data_{timestamp}.json"
        elif format_type == "pickle":
            output_path = f"extracted_data_{timestamp}.pkl"
    
    try:
        if format_type == "csv":
            # 对于CSV格式，需要处理metadata列（字典类型）
            df_copy = df.copy()
            df_copy['metadata'] = df_copy['metadata'].astype(str)
            df_copy.to_csv(output_path, index=False, encoding='utf-8')
        elif format_type == "json":
            df.to_json(output_path, orient='records', force_ascii=False, indent=2)
        elif format_type == "pickle":
            df.to_pickle(output_path)
        
        print(f"✅ 数据已保存到: {output_path}")
        print(f"   格式: {format_type.upper()}")
        print(f"   记录数: {len(df)}")
        
    except Exception as e:
        print(f"❌ 保存失败: {e}")


def main():
    parser = argparse.ArgumentParser(
        description="数据抽取到DataFrame - 不进行向量存储摄取",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 抽取所有启用的数据源并保存为CSV
  python scripts/extract_to_dataframe.py --all --format csv
  
  # 按数据源类型抽取并保存为JSON
  python scripts/extract_to_dataframe.py --type filesystem --format json
  
  # 按数据源名称抽取并保存为Pickle
  python scripts/extract_to_dataframe.py --name "医学文档库" --format pickle
  
  # 指定输出文件路径
  python scripts/extract_to_dataframe.py --all --output my_data.csv
        """
    )
    
    # 数据源选择（互斥）
    group = parser.add_mutually_exclusive_group(required=True)
    
    group.add_argument(
        "--all",
        action="store_true",
        help="抽取所有启用的数据源"
    )
    
    group.add_argument(
        "--type",
        type=str,
        choices=["filesystem", "csv_excel", "web_url", "confluence", "git"],
        help="按数据源类型抽取"
    )
    
    group.add_argument(
        "--name",
        type=str,
        help="按数据源名称抽取"
    )
    
    # 输出格式选项
    parser.add_argument(
        "--format",
        type=str,
        choices=["csv", "json", "pickle"],
        default="csv",
        help="输出格式 (默认: csv)"
    )
    
    parser.add_argument(
        "--output",
        type=str,
        help="输出文件路径 (可选，默认自动生成)"
    )
    
    parser.add_argument(
        "--show-sample",
        action="store_true",
        help="显示数据样例"
    )

    parser.add_argument(
        "--splitter",
        type=str,
        choices=["recursive", "semantic"],
        default="recursive",
        help="文本切分策略 (默认: recursive)"
    )

    parser.add_argument(
        "--enable-splitting",
        action="store_true",
        help="启用文本切分功能"
    )

    parser.add_argument(
        "--index-mode",
        type=str,
        choices=["both", "vector-only", "keyword-only", "none"],
        default="none",
        help="索引模式 (默认: none，不进行索引)"
    )
    
    args = parser.parse_args()
    
    try:
        # 初始化数据抽取服务
        print("初始化数据抽取服务...")
        service = DataExtractionService(splitter_type=args.splitter)
        
        # 根据参数抽取数据
        if args.all:
            print("=== 抽取所有启用的数据源 ===")
            df = service.extract_all_sources()
            
        elif args.type:
            print(f"=== 按类型抽取数据源: {args.type} ===")
            df = service.extract_by_source_type(args.type)
            
        elif args.name:
            print(f"=== 按名称抽取数据源: {args.name} ===")
            df = service.extract_by_source_name(args.name)
        
        # 应用文本切分（如果启用）
        if args.enable_splitting and not df.empty:
            print(f"\n=== 应用文本切分 (策略: {args.splitter}) ===")
            original_count = len(df)
            df = service.apply_text_splitting(df, f"数据抽取结果")
            print(f"切分前记录数: {original_count}")
            print(f"切分后记录数: {len(df)}")

        # 应用双重索引（如果启用）
        if args.index_mode != "none" and not df.empty:
            print(f"\n=== 应用双重索引 (模式: {args.index_mode}) ===")

            # 转换索引模式
            mode_mapping = {
                "both": IndexMode.BOTH,
                "vector-only": IndexMode.VECTOR_ONLY,
                "keyword-only": IndexMode.KEYWORD_ONLY
            }
            index_mode = mode_mapping[args.index_mode]

            # 执行索引
            index_result = service.index_to_dual_store(df, index_mode, "数据抽取结果")

            if index_result.get('success', False):
                print(f"✅ 索引完成:")
                print(f"  处理记录数: {index_result.get('processed_records', 0)}")

                vector_result = index_result.get('vector_result', {})
                if vector_result.get('success', False):
                    print(f"  向量索引: {vector_result.get('indexed_count', 0)} 条记录")

                keyword_result = index_result.get('keyword_result', {})
                if keyword_result.get('success', False):
                    print(f"  关键词索引: {keyword_result.get('indexed_count', 0)} 条记录")
            else:
                print(f"❌ 索引失败: {index_result.get('error', '未知错误')}")

        # 显示抽取结果
        if df.empty:
            print("❌ 没有抽取到任何数据")
            return

        print(f"\n✅ 数据抽取完成!")
        print(f"   总记录数: {len(df)}")
        print(f"   列名: {list(df.columns)}")
        if args.enable_splitting:
            print(f"   文本切分策略: {args.splitter}")
        if args.index_mode != "none":
            print(f"   索引模式: {args.index_mode}")
        
        # 显示数据样例
        if args.show_sample:
            print(f"\n📊 数据样例 (前3条):")
            for i, row in df.head(3).iterrows():
                print(f"\n记录 {i+1}:")
                print(f"  content: {str(row['content'])[:100]}...")
                if isinstance(row['metadata'], dict):
                    print(f"  metadata: {len(row['metadata'])} 个字段")
                    for key in sorted(list(row['metadata'].keys())[:5]):  # 只显示前5个字段
                        value = row['metadata'][key]
                        if isinstance(value, str) and len(value) > 30:
                            print(f"    {key}: {value[:30]}...")
                        else:
                            print(f"    {key}: {value}")
                else:
                    print(f"  metadata: {row['metadata']}")
        
        # 保存数据
        save_dataframe(df, args.output, args.format)
        
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
