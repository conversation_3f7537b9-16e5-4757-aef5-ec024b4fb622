/* 现代化布局样式 */
.dashboard {
    display: flex;
    min-height: 100vh;
    position: relative;
    z-index: 2;
    padding: 1.5rem;
    gap: 1.5rem;
}

/* 主内容区域 */
.content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    position: relative;
}

.content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
}

.main-content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
    background: var(--gray-25);
    position: relative;
}

/* 顶部导航 */
.top-nav {
    padding: 1.5rem 2rem;
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.current-patient {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.current-patient h2 {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--gray-900);
    letter-spacing: -0.025em;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* 状态指示器 */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-full);
    font-size: 0.8125rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-indicator.online {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.status-indicator.busy {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning);
}

.status-indicator.offline {
    background: rgba(107, 114, 128, 0.1);
    color: var(--gray-500);
}

.status-indicator::before {
    content: '';
    width: 6px;
    height: 6px;
    border-radius: var(--radius-full);
    background: currentColor;
    animation: pulse 2s infinite;
}

/* 医生徽章 */
.doctor-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--success);
    background: rgba(16, 185, 129, 0.1);
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-full);
    margin-left: 0.5rem;
    border: 1px solid rgba(16, 185, 129, 0.2);
}

/* 高亮症状关键词 */
.highlight-symptom {
    background: rgba(245, 158, 11, 0.15);
    padding: 0.125rem 0.375rem;
    border-radius: var(--radius-sm);
    font-weight: 600;
    color: var(--warning);
    border: 1px solid rgba(245, 158, 11, 0.2);
}

/* 处方消息特殊样式 */
.prescription-message .message-content {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(16, 185, 129, 0.1));
    border: 1px solid rgba(59, 130, 246, 0.2);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* 消息头部信息 */
.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.message-sender {
    font-weight: 700;
    font-size: 0.875rem;
    color: inherit;
    opacity: 0.9;
}

.message-time {
    font-size: 0.75rem;
    opacity: 0.7;
    font-weight: 500;
}
