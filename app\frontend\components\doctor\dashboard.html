<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>医生工作台 - 慧问医答</title>
    
    <!-- 引入组件化样式文件 -->
    <link rel="stylesheet" href="../../css/doctor/variables.css">
    <link rel="stylesheet" href="../../css/doctor/base.css">
    <link rel="stylesheet" href="../../css/doctor/layout.css">
    <link rel="stylesheet" href="../../css/doctor/sidebar.css">
    <link rel="stylesheet" href="../../css/doctor/components.css">
    <link rel="stylesheet" href="../../css/doctor/content.css">
    <link rel="stylesheet" href="../../css/doctor/responsive.css">
    
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.10/dayjs.min.js"></script>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
</head>
<body>
    <div class="dashboard">
        <!-- 引入侧边栏组件 -->
        <div id="sidebarComponent"></div>

        <!-- 主内容区域 -->
        <div class="content">
            <!-- 引入顶部导航组件 -->
        <div id="headerComponent"></div>
        
            <!-- 引入主内容组件 -->
            <div id="mainContentComponent"></div>
        </div>
    </div>

    <!-- 引入脚本组件 -->
    <div id="scriptsComponent"></div>

    <script>
        console.log('🚀 Dashboard 页面脚本开始执行');

        // 加载组件的辅助函数
        function loadComponent(url, targetId) {
            console.log(`📡 开始加载组件: ${url} -> ${targetId}`);

            return fetch(url)
                .then(response => {
                    console.log(`📥 收到响应: ${url}, 状态: ${response.status}`);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}`);
                    }
                    return response.text();
                })
                .then(html => {
                    console.log(`📋 获取HTML内容: ${url}, 长度: ${html.length}`);
                    const targetElement = document.getElementById(targetId);
                    if (targetElement) {
                        targetElement.innerHTML = html;

                        // 执行动态加载的脚本
                        const scripts = targetElement.querySelectorAll('script');
                        console.log(`🔧 找到 ${scripts.length} 个脚本标签`);

                        scripts.forEach((script, index) => {
                            console.log(`⚡ 执行脚本 ${index + 1}`);
                            const newScript = document.createElement('script');

                            if (script.src) {
                                console.log(`📥 加载外部脚本: ${script.src}`);
                                newScript.src = script.src;
                            } else {
                                console.log(`📝 执行内联脚本`);
                                newScript.textContent = script.textContent;
                            }

                            document.head.appendChild(newScript);
                            document.head.removeChild(newScript);
                        });

                        console.log(`✅ 成功加载组件: ${url}`);
                    } else {
                        console.error(`❌ 找不到目标元素: ${targetId}`);
                    }
                })
                .catch(error => {
                    console.error(`💥 加载组件 ${url} 失败:`, error);
                });
        }

        // 当页面加载完成时，加载所有组件
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 DOM 内容已加载，开始加载组件');

            // 逐个加载组件，而不是使用Promise.all
            loadComponent('/components/doctor/sidebar.html', 'sidebarComponent')
                .then(() => loadComponent('/components/doctor/header.html', 'headerComponent'))
                .then(() => loadComponent('/components/doctor/main-content.html', 'mainContentComponent'))
                .then(() => {
                    console.log('🎯 最后加载scripts组件');
                    return loadComponent('/components/doctor/scripts.html', 'scriptsComponent');
                })
                .then(() => {
                    console.log('🎉 所有组件加载完成');
                })
                .catch(error => {
                    console.error('💥 组件加载过程中出错:', error);
                });
        });
    </script>
</body>
</html> 
