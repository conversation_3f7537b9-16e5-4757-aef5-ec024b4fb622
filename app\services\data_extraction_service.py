"""数据抽取服务

统一管理多种数据源的抽取和标准化处理
"""

import os
import yaml
from typing import Dict, Any, List, Optional
import pandas as pd
from datetime import datetime

from app.core.extractors import (
    FilesystemExtractor,
    CsvExcelExtractor,
    WebUrlExtractor,
    ConfluenceExtractor,
    GitExtractor
)
from app.core.etl.data_processing.data_standardizer import DataStandardizer
from app.core.etl.data_processing.data_cleaner import DataCleaner
from app.core.etl.text_processing.row_to_text_converter import RowToTextConverter
from app.core.etl.text_processing.text_splitter import TextSplitterManager
from app.core.etl.indexing.dual_index_manager import DualIndexManager, IndexMode


class DataExtractionService:
    """数据抽取服务
    
    负责从sources.yaml配置中读取数据源配置，
    使用相应的抽取器抽取数据，并统一标准化处理
    """
    
    def __init__(self, sources_config_path: str = "app/configs/sources.yaml", splitter_type: str = 'recursive'):
        """初始化数据抽取服务

        Args:
            sources_config_path: 数据源配置文件路径
            splitter_type: 文本切分策略 ('recursive' 或 'semantic')
        """
        self.sources_config_path = sources_config_path
        self.config = self._load_config()
        self.standardizer = DataStandardizer(
            self.config.get('global_config', {}).get('standardization', {})
        )
        self.splitter_type = splitter_type
        self.text_splitter_manager = TextSplitterManager(
            self.config.get('global_config', {}).get('text_splitting', {})
        )
        self.dual_index_manager = DualIndexManager(
            self.config.get('global_config', {}).get('indexing', {})
        )
        
        # 抽取器映射
        self.extractor_mapping = {
            'filesystem': FilesystemExtractor,
            'csv_excel': CsvExcelExtractor,
            'web_url': WebUrlExtractor,
            'confluence': ConfluenceExtractor,
            'git': GitExtractor
        }
    
    def _load_config(self) -> Dict[str, Any]:
        """加载数据源配置"""
        try:
            with open(self.sources_config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            print(f"成功加载数据源配置: {self.sources_config_path}")
            return config
        except Exception as e:
            print(f"加载配置文件失败: {str(e)}")
            return {'data_sources': {}, 'global_config': {}}
    
    def extract_all_sources(self) -> pd.DataFrame:
        """抽取所有启用的数据源
        
        Returns:
            标准化后的DataFrame，包含所有数据源的数据
        """
        all_data = []
        extraction_summary = {
            'total_sources_processed': 0,
            'successful_extractions': 0,
            'failed_extractions': 0,
            'total_records': 0,
            'extraction_start_time': datetime.now().isoformat()
        }
        
        print("开始抽取所有数据源...")
        
        for source_type, sources in self.config.get('data_sources', {}).items():
            print(f"\n处理数据源类型: {source_type}")
            
            for source in sources:
                extraction_summary['total_sources_processed'] += 1
                
                if not source.get('enabled', False):
                    print(f"跳过已禁用的数据源: {source.get('name', 'Unknown')}")
                    continue
                
                try:
                    df = self.extract_single_source(source_type, source)
                    if not df.empty:
                        all_data.append(df)
                        extraction_summary['successful_extractions'] += 1
                        extraction_summary['total_records'] += len(df)
                        print(f"成功抽取 {len(df)} 条记录")
                    else:
                        print("数据源返回空结果")
                        
                except Exception as e:
                    print(f"抽取数据源 {source.get('name', 'Unknown')} 失败: {str(e)}")
                    extraction_summary['failed_extractions'] += 1
        
        # 合并所有数据
        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            print(f"\n合并数据完成，总计 {len(combined_df)} 条记录")
            
            # 标准化处理
            standardized_df = self.standardizer.standardize(combined_df)
            
            # 添加抽取摘要信息
            extraction_summary['extraction_end_time'] = datetime.now().isoformat()
            extraction_summary['final_record_count'] = len(standardized_df)
            
            # 将摘要信息添加到每条记录的元数据中
            for i, row in standardized_df.iterrows():
                if isinstance(standardized_df.at[i, 'metadata'], dict):
                    standardized_df.at[i, 'metadata']['extraction_summary'] = extraction_summary
            
            print(f"数据标准化完成，最终数据量: {len(standardized_df)} 条")
            return standardized_df
        else:
            print("没有成功抽取到任何数据")
            return pd.DataFrame(columns=['content', 'metadata'])
    
    def extract_single_source(self, source_type: str, source_config: Dict[str, Any]) -> pd.DataFrame:
        """抽取单个数据源
        
        Args:
            source_type: 数据源类型
            source_config: 数据源配置
            
        Returns:
            抽取的DataFrame数据
        """
        if source_type not in self.extractor_mapping:
            raise ValueError(f"不支持的数据源类型: {source_type}")
        
        extractor_class = self.extractor_mapping[source_type]
        
        # 提取配置和元数据
        config = source_config.get('config', {})
        metadata = source_config.get('metadata', {})
        metadata['source_name'] = source_config.get('name', 'Unknown')
        
        print(f"抽取数据源: {metadata['source_name']}")

        # 创建并执行抽取器
        extractor = extractor_class(config, metadata)
        df = extractor.extract()

        # 应用数据清洗步骤（如果配置了）
        cleaning_steps = source_config.get('cleaning_steps', [])
        if cleaning_steps and not df.empty:
            cleaner = DataCleaner(cleaning_steps)
            df = cleaner.clean(df, metadata['source_name'])

        # 应用行转文本转换（如果配置了且是结构化数据）
        row_to_text_config = source_config.get('row_to_text', {})
        if row_to_text_config.get('enabled', False) and not df.empty:
            # 检查是否是结构化数据源（CSV/Excel等）
            if source_type in ['csv_excel'] or self._is_structured_data(df):
                converter = RowToTextConverter(row_to_text_config)
                df = converter.convert(df, metadata['source_name'])

        return df

    def apply_text_splitting(self, df: pd.DataFrame, source_name: str = "Unknown",
                           text_column: str = None) -> pd.DataFrame:
        """应用文本切分

        Args:
            df: 输入DataFrame
            source_name: 数据源名称
            text_column: 文本列名，如果为None则自动检测

        Returns:
            切分后的DataFrame
        """
        if df.empty:
            return df

        # 自动检测文本列
        if text_column is None:
            # 优先使用natural_language_description列（来自行转文本）
            if 'natural_language_description' in df.columns:
                text_column = 'natural_language_description'
            elif 'content' in df.columns:
                text_column = 'content'
            else:
                print(f"数据源 {source_name}: 未找到合适的文本列，跳过文本切分")
                return df

        # 应用文本切分
        return self.text_splitter_manager.split_dataframe(
            df, self.splitter_type, text_column, source_name
        )

    def index_to_dual_store(self, df: pd.DataFrame, mode: IndexMode = IndexMode.BOTH,
                           source_name: str = "Unknown") -> Dict[str, Any]:
        """将DataFrame索引到双重存储

        Args:
            df: 输入DataFrame
            mode: 索引模式
            source_name: 数据源名称

        Returns:
            索引结果
        """
        return self.dual_index_manager.index_dataframe(df, mode, source_name=source_name)

    def _is_structured_data(self, df: pd.DataFrame) -> bool:
        """判断DataFrame是否包含结构化数据

        Args:
            df: 要检查的DataFrame

        Returns:
            如果是结构化数据返回True，否则返回False
        """
        # 检查是否有多个列（除了content和metadata）
        if len(df.columns) > 2:
            return True

        # 检查是否有原始数据信息（来自CSV等结构化源）
        if 'metadata' in df.columns:
            sample_metadata = df['metadata'].iloc[0] if len(df) > 0 else {}
            if isinstance(sample_metadata, dict):
                # 如果元数据中包含原始数据，说明是结构化数据
                if 'original_data' in sample_metadata:
                    return True
                # 如果有columns信息，也说明是结构化数据
                if 'columns' in sample_metadata:
                    return True

        return False

    def extract_by_source_type(self, source_type: str) -> pd.DataFrame:
        """按数据源类型抽取数据
        
        Args:
            source_type: 数据源类型 (filesystem, csv_excel, web_url, confluence, git)
            
        Returns:
            该类型所有启用数据源的标准化DataFrame
        """
        if source_type not in self.config.get('data_sources', {}):
            print(f"配置中没有找到数据源类型: {source_type}")
            return pd.DataFrame(columns=['content', 'metadata'])
        
        sources = self.config['data_sources'][source_type]
        all_data = []
        
        for source in sources:
            if source.get('enabled', False):
                try:
                    df = self.extract_single_source(source_type, source)
                    if not df.empty:
                        all_data.append(df)
                except Exception as e:
                    print(f"抽取数据源失败: {str(e)}")
        
        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            return self.standardizer.standardize(combined_df)
        else:
            return pd.DataFrame(columns=['content', 'metadata'])
    
    def extract_by_source_name(self, source_name: str) -> pd.DataFrame:
        """按数据源名称抽取数据
        
        Args:
            source_name: 数据源名称
            
        Returns:
            指定数据源的标准化DataFrame
        """
        for source_type, sources in self.config.get('data_sources', {}).items():
            for source in sources:
                if source.get('name') == source_name:
                    if not source.get('enabled', False):
                        print(f"数据源 {source_name} 已禁用")
                        return pd.DataFrame(columns=['content', 'metadata'])
                    
                    try:
                        df = self.extract_single_source(source_type, source)
                        return self.standardizer.standardize(df)
                    except Exception as e:
                        print(f"抽取数据源 {source_name} 失败: {str(e)}")
                        return pd.DataFrame(columns=['content', 'metadata'])
        
        print(f"未找到数据源: {source_name}")
        return pd.DataFrame(columns=['content', 'metadata'])
    
    def get_available_sources(self) -> Dict[str, List[Dict[str, Any]]]:
        """获取所有可用的数据源信息
        
        Returns:
            按类型分组的数据源信息
        """
        available_sources = {}
        
        for source_type, sources in self.config.get('data_sources', {}).items():
            available_sources[source_type] = []
            
            for source in sources:
                source_info = {
                    'name': source.get('name', 'Unknown'),
                    'enabled': source.get('enabled', False),
                    'type': source_type,
                    'category': source.get('metadata', {}).get('category', 'unknown')
                }
                available_sources[source_type].append(source_info)
        
        return available_sources
    
    def enable_source(self, source_name: str) -> bool:
        """启用指定的数据源
        
        Args:
            source_name: 数据源名称
            
        Returns:
            是否成功启用
        """
        return self._toggle_source(source_name, True)
    
    def disable_source(self, source_name: str) -> bool:
        """禁用指定的数据源
        
        Args:
            source_name: 数据源名称
            
        Returns:
            是否成功禁用
        """
        return self._toggle_source(source_name, False)
    
    def _toggle_source(self, source_name: str, enabled: bool) -> bool:
        """切换数据源启用状态"""
        for source_type, sources in self.config.get('data_sources', {}).items():
            for i, source in enumerate(sources):
                if source.get('name') == source_name:
                    self.config['data_sources'][source_type][i]['enabled'] = enabled
                    # 这里可以选择性地保存配置到文件
                    # self._save_config()
                    print(f"数据源 {source_name} 已{'启用' if enabled else '禁用'}")
                    return True
        
        print(f"未找到数据源: {source_name}")
        return False
    
    def get_extraction_statistics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """获取抽取统计信息
        
        Args:
            df: 抽取的DataFrame
            
        Returns:
            统计信息字典
        """
        if df.empty:
            return {"error": "没有数据"}
        
        # 基本统计
        stats = self.standardizer.get_statistics(df)
        
        # 添加数据源特定统计
        source_names = []
        categories = []
        
        for _, row in df.iterrows():
            metadata = row['metadata']
            if isinstance(metadata, dict):
                source_names.append(metadata.get('source_name', 'unknown'))
                categories.append(metadata.get('category', 'unknown'))
        
        stats['source_name_distribution'] = pd.Series(source_names).value_counts().to_dict()
        stats['category_distribution'] = pd.Series(categories).value_counts().to_dict()
        
        return stats
    
    def test_source_connection(self, source_name: str) -> Dict[str, Any]:
        """测试数据源连接
        
        Args:
            source_name: 数据源名称
            
        Returns:
            测试结果
        """
        for source_type, sources in self.config.get('data_sources', {}).items():
            for source in sources:
                if source.get('name') == source_name:
                    try:
                        # 创建抽取器进行连接测试
                        extractor_class = self.extractor_mapping.get(source_type)
                        if not extractor_class:
                            return {"success": False, "error": f"不支持的数据源类型: {source_type}"}
                        
                        config = source.get('config', {})
                        metadata = source.get('metadata', {})
                        
                        extractor = extractor_class(config, metadata)
                        
                        # 如果抽取器有测试连接方法，使用它
                        if hasattr(extractor, 'test_connection'):
                            result = extractor.test_connection()
                            return {"success": result, "source_name": source_name, "type": source_type}
                        else:
                            # 否则尝试抽取少量数据作为测试
                            test_df = extractor.extract()
                            return {
                                "success": True,
                                "source_name": source_name,
                                "type": source_type,
                                "test_data_count": len(test_df)
                            }
                            
                    except Exception as e:
                        return {
                            "success": False,
                            "source_name": source_name,
                            "type": source_type,
                            "error": str(e)
                        }
        
        return {"success": False, "error": f"未找到数据源: {source_name}"} 