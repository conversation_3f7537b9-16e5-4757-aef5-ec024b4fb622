"""简化版搜索引擎

基于内存的简单关键词搜索引擎，作为Elasticsearch的备用方案
"""

import re
import json
import os
from typing import List, Dict, Any, Optional
from datetime import datetime
import pandas as pd

from .base_search_engine import BaseSearchEngine


class SimpleSearchEngine(BaseSearchEngine):
    """简化版搜索引擎
    
    基于内存的简单实现，支持基本的关键词搜索和过滤功能
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """初始化简化版搜索引擎
        
        Args:
            config: 搜索引擎配置
        """
        super().__init__(config)
        
        # 内存中的文档存储
        self.documents = {}  # {index_name: {doc_id: document}}
        self.document_counters = {}  # {index_name: counter}
        
        # 持久化配置
        self.data_dir = self.config.get('data_dir', './data/search_index')
        self.enable_persistence = self.config.get('enable_persistence', True)
        
        # 确保数据目录存在
        if self.enable_persistence:
            os.makedirs(self.data_dir, exist_ok=True)
            self._load_indices()
        
        print(f"✅ 简化版搜索引擎初始化完成 (数据目录: {self.data_dir})")
    
    def _get_index_file_path(self, index_name: str) -> str:
        """获取索引文件路径"""
        return os.path.join(self.data_dir, f"{index_name}.json")
    
    def _load_indices(self):
        """从磁盘加载索引"""
        if not os.path.exists(self.data_dir):
            return
        
        for filename in os.listdir(self.data_dir):
            if filename.endswith('.json'):
                index_name = filename[:-5]  # 移除.json后缀
                file_path = self._get_index_file_path(index_name)
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        self.documents[index_name] = data.get('documents', {})
                        self.document_counters[index_name] = data.get('counter', 0)
                    print(f"✅ 加载索引: {index_name} ({len(self.documents[index_name])} 个文档)")
                except Exception as e:
                    print(f"⚠️  加载索引 {index_name} 失败: {e}")
    
    def _save_index(self, index_name: str):
        """保存索引到磁盘"""
        if not self.enable_persistence:
            return
        
        file_path = self._get_index_file_path(index_name)
        
        try:
            data = {
                'documents': self.documents.get(index_name, {}),
                'counter': self.document_counters.get(index_name, 0),
                'created_at': datetime.now().isoformat(),
                'index_name': index_name
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"⚠️  保存索引 {index_name} 失败: {e}")
    
    def create_index(self, index_name: str = None, mapping: Dict[str, Any] = None) -> bool:
        """创建索引"""
        index_name = index_name or self.index_name
        
        if index_name not in self.documents:
            self.documents[index_name] = {}
            self.document_counters[index_name] = 0
            self._save_index(index_name)
            print(f"✅ 创建索引: {index_name}")
        else:
            print(f"索引 {index_name} 已存在")
        
        return True
    
    def delete_index(self, index_name: str = None) -> bool:
        """删除索引"""
        index_name = index_name or self.index_name
        
        if index_name in self.documents:
            del self.documents[index_name]
            del self.document_counters[index_name]
            
            # 删除磁盘文件
            if self.enable_persistence:
                file_path = self._get_index_file_path(index_name)
                if os.path.exists(file_path):
                    os.remove(file_path)
            
            print(f"✅ 删除索引: {index_name}")
        else:
            print(f"索引 {index_name} 不存在")
        
        return True
    
    def index_exists(self, index_name: str = None) -> bool:
        """检查索引是否存在"""
        index_name = index_name or self.index_name
        return index_name in self.documents
    
    def add_documents(self, documents: List[Dict[str, Any]], index_name: str = None) -> bool:
        """批量添加文档"""
        index_name = index_name or self.index_name
        
        # 确保索引存在
        if not self.index_exists(index_name):
            self.create_index(index_name)
        
        added_count = 0
        
        for doc in documents:
            doc_copy = doc.copy()
            doc_copy['indexed_at'] = datetime.now().isoformat()
            
            # 生成文档ID
            doc_id = doc_copy.get('chunk_id')
            if not doc_id:
                self.document_counters[index_name] += 1
                doc_id = f"doc_{self.document_counters[index_name]}"
                doc_copy['_id'] = doc_id
            
            self.documents[index_name][doc_id] = doc_copy
            added_count += 1
        
        # 保存到磁盘
        self._save_index(index_name)
        
        print(f"✅ 成功添加 {added_count} 个文档到索引 {index_name}")
        return added_count > 0
    
    def update_document(self, doc_id: str, document: Dict[str, Any], index_name: str = None) -> bool:
        """更新文档"""
        index_name = index_name or self.index_name
        
        if not self.index_exists(index_name):
            return False
        
        if doc_id in self.documents[index_name]:
            document_copy = document.copy()
            document_copy['indexed_at'] = datetime.now().isoformat()
            self.documents[index_name][doc_id].update(document_copy)
            self._save_index(index_name)
            return True
        
        return False
    
    def delete_document(self, doc_id: str, index_name: str = None) -> bool:
        """删除文档"""
        index_name = index_name or self.index_name
        
        if not self.index_exists(index_name):
            return False
        
        if doc_id in self.documents[index_name]:
            del self.documents[index_name][doc_id]
            self._save_index(index_name)
            return True
        
        return True  # 文档不存在也认为删除成功
    
    def search(self, query: str, filters: Dict[str, Any] = None, 
               size: int = 10, index_name: str = None) -> List[Dict[str, Any]]:
        """搜索文档"""
        index_name = index_name or self.index_name
        
        if not self.index_exists(index_name):
            return []
        
        # 简单的关键词搜索
        query_terms = query.lower().split()
        results = []
        
        for doc_id, doc in self.documents[index_name].items():
            # 计算匹配分数
            content = str(doc.get('content', '')).lower()
            score = 0
            
            for term in query_terms:
                # 简单的词频计算
                score += content.count(term)
            
            if score > 0:
                # 应用过滤条件
                if filters:
                    match = True
                    for key, value in filters.items():
                        doc_value = doc.get(key)
                        if isinstance(value, list):
                            if doc_value not in value:
                                match = False
                                break
                        else:
                            if doc_value != value:
                                match = False
                                break
                    
                    if not match:
                        continue
                
                result = doc.copy()
                result['_score'] = score
                result['_id'] = doc_id
                results.append(result)
        
        # 按分数排序并限制结果数量
        results.sort(key=lambda x: x['_score'], reverse=True)
        return results[:size]
    
    def get_document_count(self, index_name: str = None) -> int:
        """获取文档数量"""
        index_name = index_name or self.index_name
        
        if not self.index_exists(index_name):
            return 0
        
        return len(self.documents[index_name])
    
    def get_all_documents(self, index_name: str = None) -> List[Dict[str, Any]]:
        """获取所有文档（调试用）"""
        index_name = index_name or self.index_name
        
        if not self.index_exists(index_name):
            return []
        
        return list(self.documents[index_name].values())
