<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>慧问医答——智能导诊解答机器人</title>
    <link rel="stylesheet" href="./css/style.css">
    <link rel="stylesheet" href="./css/landing.css">
    <!-- 预加载现代字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
</head>
<body>
    <div id="app">
        <!-- 导航栏组件将插入这里 -->
        <div id="navigation-container"></div>
        <!-- 已删除测试用登录按钮 -->
        <main>
            <!-- Hero区域组件将插入这里 -->
            <div id="hero-container"></div>

            <!-- 数据统计组件将插入这里 -->
            <div id="stats-container"></div>

            <!-- 服务流程组件将插入这里 -->
            <div id="process-container"></div>

            <!-- 特色功能组件将插入这里 -->
            <div id="features-container"></div>

            <!-- 其他区域组件容器 -->
            <div id="team-container"></div>
            <div id="testimonials-container"></div>
            <div id="faq-container"></div>
            <div id="cta-container"></div>
        </main>

        <!-- 页脚组件将插入这里 -->
        <div id="footer-container"></div>

        <!-- 模态框组件将插入这里 -->
        <div id="modals-container"></div>
    </div>

    <!-- 登录成功弹窗 -->
    <div id="loginSuccessModal" class="custom-modal" style="display:none;">
        <div class="custom-modal-content">
            <span id="loginSuccessText">登录成功！</span>
        </div>
    </div>

    <!-- 注销成功弹窗 -->
    <div id="logoutSuccessModal" class="custom-modal" style="display:none;">
        <div class="custom-modal-content">
            <span id="logoutSuccessText">账号已注销</span>
        </div>
    </div>

    <!-- 注册跳转问诊弹窗 -->
    <div id="registerToChatModal" class="custom-modal" style="display:none;">
        <div class="custom-modal-content">
            <span id="registerToChatText">注册成功，正在为你跳转问诊界面</span>
        </div>
    </div>

    <!-- 注册失败弹窗 -->
    <div id="registerFailModal" class="custom-modal" style="display:none;">
        <div class="custom-modal-content">
            <span id="registerFailText">用户名已存在</span>
        </div>
    </div>

    <!-- 登录状态提示弹窗 -->
    <div id="loginStatusModal" class="custom-modal" style="display:none;">
        <div class="custom-modal-content">
            <span id="loginStatusText">您已登录</span>
        </div>
    </div>

    <!-- 登录错误提示弹窗 -->
    <div id="loginErrorModal" class="custom-modal" style="display:none;">
        <div class="custom-modal-content">
            <span id="loginErrorText">用户名或密码错误</span>
        </div>
    </div>

    <!-- 依赖库 -->
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <script src="./js/auth.js"></script>
    
    <!-- 组件加载器 -->
    <script>
        window.isLoggedIn = false; // 未登录，登录后设为 true
        // 组件加载函数
        async function loadComponent(componentPath, containerId) {
            try {
                const response = await fetch(componentPath);
                const html = await response.text();
                document.getElementById(containerId).innerHTML = html;
            } catch (error) {
                console.error(`加载组件 ${componentPath} 失败:`, error);
            }
        }

        // 页面加载完成后加载所有组件
        document.addEventListener('DOMContentLoaded', async function() {
            await loadComponent('./components/index/navigation.html', 'navigation-container');
            // navigation.html 加载完后绑定下拉事件
            const dropdownContent = document.getElementById('profile-dropdown-content');
            const loginBtn = document.getElementById('loginBtn');
            if (dropdownContent && loginBtn) {
                let hideTimeout = null;

                function showDropdown() {
                    clearTimeout(hideTimeout);
                    dropdownContent.style.display = 'block';

                    // 如果下拉框内容为空或者没有个人资料表，就渲染入口
                    if (!dropdownContent.innerHTML.trim() || !dropdownContent.querySelector('#profile-entry')) {
                        renderProfileEntry();
                    }
                }

                function hideDropdown() {
                    // 检查是否正在编辑个人资料表
                    const isEditingProfile = window.isEditingProfile || false;

                    if (isEditingProfile) {
                        // 如果正在编辑，不自动关闭下拉框
                        return;
                    }

                    hideTimeout = setTimeout(() => {
                        dropdownContent.style.display = 'none';
                        // 关闭下拉时只需要隐藏，下次显示时会自动渲染入口
                    }, 200);
                }

                loginBtn.addEventListener('mouseenter', showDropdown);
                loginBtn.addEventListener('mouseleave', hideDropdown);
                dropdownContent.addEventListener('mouseenter', showDropdown);
                dropdownContent.addEventListener('mouseleave', hideDropdown);

                // 渲染入口
                function renderProfileEntry() {
                    dropdownContent.innerHTML = `
                        <div id="profile-entry" style="cursor:pointer;">个人资料表</div>
                        <div id="profile-table-container"></div>
                    `;
                    bindProfileEntry();
                }

                // 绑定入口点击事件
                function bindProfileEntry() {
                    const profileEntry = document.getElementById('profile-entry');
                    console.log('绑定个人资料表事件，元素:', profileEntry);
                    if (profileEntry && !profileEntry.hasAttribute('data-bound')) {
                        // 标记已绑定，避免重复绑定
                        profileEntry.setAttribute('data-bound', 'true');

                        profileEntry.onclick = function(event) {
                            // 阻止事件冒泡
                            event.stopPropagation();
                            event.preventDefault();

                            if (!window.isLoggedIn) {
                                showNotLoggedInModal();
                                return;
                            }
                            renderProfileTable();
                        };
                        console.log('个人资料表点击事件绑定成功');
                    } else if (profileEntry) {
                        console.log('个人资料表事件已经绑定过了，跳过');
                    } else {
                        console.log('找不到个人资料表元素！');
                    }
                }

                // 渲染个人资料表
                async function renderProfileTable() {
                    // 获取下拉框容器和表格容器
                    const dropdownContent = document.getElementById('profile-dropdown-content');
                    const tableContainer = document.getElementById('profile-table-container');

                    if (!dropdownContent || !tableContainer) {
                        console.error('找不到下拉框容器或表格容器！');
                        return;
                    }

                    // 获取用户数据
                    const username = localStorage.getItem('username') || '用户';
                    const role = localStorage.getItem('role') || 'patient';

                    // 初始化用户数据
                    let currentData = {
                        nickname: username,
                        gender: '未设置',
                        phone: '未设置',
                        email: '未设置',
                        introduction: '这位用户很懒，什么都没有留下...',
                        role: role === 'doctor' ? '医生' : '患者',
                        // 医疗相关信息
                        age: '',
                        height: '',
                        weight: '',
                        bloodType: '未设置',
                        allergies: '',
                        emergencyContact: '',
                        emergencyPhone: '',
                        medicalHistory: ''
                    };

                    // 异步加载用户的个人资料数据
                    try {
                        const savedProfileData = await loadUserProfile(username);
                        if (savedProfileData) {
                            currentData = { ...currentData, ...savedProfileData };
                        }
                    } catch (error) {
                        console.error('加载个人资料时出错:', error);
                    }

                    let initialData = { ...currentData };
                    let isEditing = false;
                    window.isEditingProfile = false;

                    console.log('当前数据:', currentData);
                    console.log('开始渲染HTML...');

                    // 将表格内容渲染到专门的表格容器中，使用现代浅色系风格
                    tableContainer.innerHTML = `
                        <!-- 标题栏 -->
                        <div style="display:flex; justify-content:space-between; align-items:center; margin-bottom:20px; padding-bottom:16px; border-bottom:1px solid #f1f5f9;">
                            <div style="display:flex; align-items:center; gap:8px;">
                                <div style="width:4px; height:20px; background:linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%); border-radius:2px;"></div>
                                <span style="font-weight:600; color:#1e293b; font-size:16px;">个人资料</span>
                            </div>
                            <span id="close-profile-table" style="cursor:pointer; font-size:20px; color:#64748b; width:28px; height:28px; display:flex; align-items:center; justify-content:center; border-radius:6px; transition:all 0.2s ease;"
                                  onmouseover="this.style.background='#f1f5f9'; this.style.color='#334155';"
                                  onmouseout="this.style.background='transparent'; this.style.color='#64748b';">&times;</span>
                        </div>

                        <!-- 个人资料表格 - 横向舒适布局 -->
                        <div style="background:linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); border:1px solid #e2e8f0; border-radius:12px; padding:20px; margin-bottom:20px;">
                            <!-- 主要信息区域 - 3列布局 -->
                            <div style="display:grid; grid-template-columns:1fr 1fr 1fr; gap:24px; margin-bottom:20px;">
                                <!-- 第一列：基本信息 -->
                                <div>
                                    <h3 style="margin:0 0 14px 0; color:#1e293b; font-size:15px; font-weight:600; display:flex; align-items:center; gap:8px;">
                                        <span style="color:#3b82f6;">👤</span> 基本信息
                                    </h3>
                                    <div style="display:grid; gap:14px;">
                                        <!-- 昵称 -->
                                        <div style="display:grid; grid-template-columns:60px 1fr; gap:10px; align-items:center;">
                                            <label style="font-weight:500; color:#475569; font-size:13px;">昵称</label>
                                            <input type="text" id="edit-nickname" value="${currentData.nickname}" readonly
                                                   style="width:100%; border:1px solid #e2e8f0; background:#ffffff; outline:none; padding:8px 10px; border-radius:6px; font-size:13px; color:#1e293b; transition:all 0.2s ease;">
                                        </div>

                                        <!-- 性别年龄 -->
                                        <div style="display:grid; grid-template-columns:60px 1fr 1fr; gap:8px; align-items:center;">
                                            <label style="font-weight:500; color:#475569; font-size:13px;">性别/年龄</label>
                                            <select id="edit-gender" disabled style="width:100%; border:1px solid #e2e8f0; background:#ffffff; outline:none; padding:8px 10px; border-radius:6px; font-size:13px; color:#1e293b; transition:all 0.2s ease;">
                                                <option value="男" ${currentData.gender === '男' ? 'selected' : ''}>男</option>
                                                <option value="女" ${currentData.gender === '女' ? 'selected' : ''}>女</option>
                                                <option value="未设置" ${currentData.gender === '未设置' ? 'selected' : ''}>未设置</option>
                                            </select>
                                            <input type="number" id="edit-age" value="${currentData.age}" readonly placeholder="年龄"
                                                   style="width:100%; border:1px solid #e2e8f0; background:#ffffff; outline:none; padding:8px 10px; border-radius:6px; font-size:13px; color:#1e293b; transition:all 0.2s ease;" min="0" max="150">
                                        </div>

                                        <!-- 身高体重 -->
                                        <div style="display:grid; grid-template-columns:60px 1fr 1fr; gap:8px; align-items:center;">
                                            <label style="font-weight:500; color:#475569; font-size:13px;">身高/体重</label>
                                            <input type="number" id="edit-height" value="${currentData.height}" readonly placeholder="身高(cm)"
                                                   style="width:100%; border:1px solid #e2e8f0; background:#ffffff; outline:none; padding:8px 10px; border-radius:6px; font-size:13px; color:#1e293b; transition:all 0.2s ease;" min="0" max="300">
                                            <input type="number" id="edit-weight" value="${currentData.weight}" readonly placeholder="体重(kg)"
                                                   style="width:100%; border:1px solid #e2e8f0; background:#ffffff; outline:none; padding:8px 10px; border-radius:6px; font-size:13px; color:#1e293b; transition:all 0.2s ease;" min="0" max="500" step="0.1">
                                        </div>

                                        <!-- 血型角色 -->
                                        <div style="display:grid; grid-template-columns:60px 1fr 1fr; gap:8px; align-items:center;">
                                            <label style="font-weight:500; color:#475569; font-size:13px;">血型/角色</label>
                                            <select id="edit-blood-type" disabled style="width:100%; border:1px solid #e2e8f0; background:#ffffff; outline:none; padding:8px 10px; border-radius:6px; font-size:13px; color:#1e293b; transition:all 0.2s ease;">
                                                <option value="未设置" ${currentData.bloodType === '未设置' ? 'selected' : ''}>未设置</option>
                                                <option value="A型" ${currentData.bloodType === 'A型' ? 'selected' : ''}>A型</option>
                                                <option value="B型" ${currentData.bloodType === 'B型' ? 'selected' : ''}>B型</option>
                                                <option value="AB型" ${currentData.bloodType === 'AB型' ? 'selected' : ''}>AB型</option>
                                                <option value="O型" ${currentData.bloodType === 'O型' ? 'selected' : ''}>O型</option>
                                            </select>
                                            <input type="text" value="${currentData.role}" readonly
                                                   style="width:100%; border:1px solid #e2e8f0; background:#f8fafc; outline:none; padding:8px 10px; border-radius:6px; font-size:13px; color:#64748b; cursor:not-allowed;">
                                        </div>
                                    </div>
                                </div>

                                <!-- 第二列：联系信息 -->
                                <div>
                                    <h3 style="margin:0 0 14px 0; color:#1e293b; font-size:15px; font-weight:600; display:flex; align-items:center; gap:8px;">
                                        <span style="color:#10b981;">📞</span> 联系信息
                                    </h3>
                                    <div style="display:grid; gap:14px;">
                                        <!-- 手机 -->
                                        <div style="display:grid; grid-template-columns:60px 1fr; gap:10px; align-items:center;">
                                            <label style="font-weight:500; color:#475569; font-size:13px;">手机</label>
                                            <input type="tel" id="edit-phone" value="${currentData.phone}" readonly placeholder="手机号码"
                                                   style="width:100%; border:1px solid #e2e8f0; background:#ffffff; outline:none; padding:8px 10px; border-radius:6px; font-size:13px; color:#1e293b; transition:all 0.2s ease;">
                                        </div>

                                        <!-- 邮箱 -->
                                        <div style="display:grid; grid-template-columns:60px 1fr; gap:10px; align-items:center;">
                                            <label style="font-weight:500; color:#475569; font-size:13px;">邮箱</label>
                                            <input type="email" id="edit-email" value="${currentData.email}" readonly placeholder="邮箱地址"
                                                   style="width:100%; border:1px solid #e2e8f0; background:#ffffff; outline:none; padding:8px 10px; border-radius:6px; font-size:13px; color:#1e293b; transition:all 0.2s ease;">
                                        </div>

                                        <!-- 紧急联系人 -->
                                        <div style="display:grid; grid-template-columns:60px 1fr; gap:10px; align-items:center;">
                                            <label style="font-weight:500; color:#475569; font-size:13px;">紧急联系人</label>
                                            <input type="text" id="edit-emergency-contact" value="${currentData.emergencyContact}" readonly placeholder="联系人姓名"
                                                   style="width:100%; border:1px solid #e2e8f0; background:#ffffff; outline:none; padding:8px 10px; border-radius:6px; font-size:13px; color:#1e293b; transition:all 0.2s ease;">
                                        </div>

                                        <!-- 紧急电话 -->
                                        <div style="display:grid; grid-template-columns:60px 1fr; gap:10px; align-items:center;">
                                            <label style="font-weight:500; color:#475569; font-size:13px;">紧急电话</label>
                                            <input type="tel" id="edit-emergency-phone" value="${currentData.emergencyPhone}" readonly placeholder="紧急电话"
                                                   style="width:100%; border:1px solid #e2e8f0; background:#ffffff; outline:none; padding:8px 10px; border-radius:6px; font-size:13px; color:#1e293b; transition:all 0.2s ease;">
                                        </div>
                                    </div>
                                </div>

                                <!-- 第三列：医疗信息 -->
                                <div>
                                    <h3 style="margin:0 0 14px 0; color:#1e293b; font-size:15px; font-weight:600; display:flex; align-items:center; gap:8px;">
                                        <span style="color:#ef4444;">🏥</span> 医疗信息
                                    </h3>
                                    <div style="display:grid; gap:14px;">
                                        <!-- 过敏史 -->
                                        <div>
                                            <label style="font-weight:500; color:#475569; font-size:13px; display:block; margin-bottom:8px;">过敏史</label>
                                            <textarea id="edit-allergies" readonly placeholder="药物过敏、食物过敏等"
                                                      style="width:100%; height:60px; border:1px solid #e2e8f0; background:#ffffff; outline:none; resize:none; padding:8px 10px; border-radius:6px; font-size:13px; color:#1e293b; line-height:1.4; transition:all 0.2s ease;">${currentData.allergies}</textarea>
                                        </div>

                                        <!-- 既往病史 -->
                                        <div>
                                            <label style="font-weight:500; color:#475569; font-size:13px; display:block; margin-bottom:8px;">既往病史</label>
                                            <textarea id="edit-medical-history" readonly placeholder="既往病史、手术史等"
                                                      style="width:100%; height:60px; border:1px solid #e2e8f0; background:#ffffff; outline:none; resize:none; padding:8px 10px; border-radius:6px; font-size:13px; color:#1e293b; line-height:1.4; transition:all 0.2s ease;">${currentData.medicalHistory}</textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 底部简介区域 -->
                            <div style="border-top:1px solid #e2e8f0; padding-top:16px;">
                                <label style="font-weight:500; color:#475569; font-size:13px; display:block; margin-bottom:8px;">个人简介</label>
                                <textarea id="edit-introduction" readonly placeholder="个人简介"
                                          style="width:100%; height:50px; border:1px solid #e2e8f0; background:#ffffff; outline:none; resize:none; padding:8px 10px; border-radius:6px; font-size:13px; color:#1e293b; line-height:1.4; transition:all 0.2s ease;">${currentData.introduction}</textarea>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div style="display:flex; justify-content:flex-end; gap:12px;">
                            <button id="edit-btn" style="padding:10px 20px; border:none; background:linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%); color:#ffffff; border-radius:8px; cursor:pointer; font-weight:500; font-size:14px; transition:all 0.2s ease; box-shadow:0 2px 4px rgba(59, 130, 246, 0.2);"
                                    onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 8px rgba(59, 130, 246, 0.3)';"
                                    onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(59, 130, 246, 0.2)';">
                                编辑
                            </button>
                            <button id="reset-btn" style="padding:10px 20px; border:1px solid #e2e8f0; background:#ffffff; color:#64748b; border-radius:8px; cursor:pointer; font-weight:500; font-size:14px; transition:all 0.2s ease;" disabled
                                    onmouseover="if(!this.disabled) { this.style.background='#f8fafc'; this.style.borderColor='#cbd5e1'; }"
                                    onmouseout="if(!this.disabled) { this.style.background='#ffffff'; this.style.borderColor='#e2e8f0'; }">
                                重置
                            </button>
                            <button id="save-btn" style="padding:10px 20px; border:1px solid #e2e8f0; background:#f1f5f9; color:#94a3b8; border-radius:8px; cursor:not-allowed; font-weight:500; font-size:14px; transition:all 0.2s ease;" disabled>
                                保存
                            </button>
                        </div>

                        <!-- 消息提示 -->
                        <div id="profile-msg" style="margin-top:16px; min-height:20px; font-size:14px; text-align:center; font-weight:500;"></div>
                    `;

                    // 强制显示下拉框，使用现代浅色系风格
                    dropdownContent.style.cssText = `
                        display: block !important;
                        visibility: visible !important;
                        opacity: 1 !important;
                        position: absolute !important;
                        top: 100% !important;
                        right: 0 !important;
                        z-index: 99999 !important;
                        background: rgba(255, 255, 255, 0.98) !important;
                        backdrop-filter: blur(12px) !important;
                        border: 1px solid #e2e8f0 !important;
                        min-width: 750px !important;
                        max-height: none !important;
                        overflow: visible !important;
                        padding: 24px !important;
                        border-radius: 12px !important;
                        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
                    `;



                    // 按钮状态更新函数
                    function updateButtonStates() {
                        const editBtn = document.getElementById('edit-btn');
                        const resetBtn = document.getElementById('reset-btn');
                        const saveBtn = document.getElementById('save-btn');

                        if (isEditing) {
                            // 编辑按钮变为取消按钮
                            editBtn.textContent = '取消';
                            editBtn.style.background = 'linear-gradient(135deg, #ef4444 0%, #f87171 100%)';
                            editBtn.style.border = 'none';
                            editBtn.style.color = '#ffffff';
                            editBtn.style.boxShadow = '0 2px 4px rgba(239, 68, 68, 0.2)';

                            // 启用重置按钮
                            resetBtn.disabled = false;
                            resetBtn.style.cursor = 'pointer';
                            resetBtn.style.background = '#ffffff';
                            resetBtn.style.color = '#64748b';
                            resetBtn.style.borderColor = '#e2e8f0';

                            // 启用保存按钮
                            saveBtn.disabled = false;
                            saveBtn.style.background = 'linear-gradient(135deg, #10b981 0%, #34d399 100%)';
                            saveBtn.style.color = '#ffffff';
                            saveBtn.style.cursor = 'pointer';
                            saveBtn.style.border = 'none';
                            saveBtn.style.boxShadow = '0 2px 4px rgba(16, 185, 129, 0.2)';

                            // 启用输入框并添加现代样式
                            const inputs = [
                                'edit-nickname', 'edit-phone', 'edit-email', 'edit-introduction',
                                'edit-age', 'edit-height', 'edit-weight', 'edit-emergency-contact',
                                'edit-emergency-phone', 'edit-allergies', 'edit-medical-history'
                            ];
                            inputs.forEach(id => {
                                const element = document.getElementById(id);
                                if (element) {
                                    element.readOnly = false;
                                    element.style.borderColor = '#cbd5e1';
                                    element.style.background = '#ffffff';
                                    element.onfocus = function() {
                                        this.style.borderColor = '#3b82f6';
                                        this.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                                    };
                                    element.onblur = function() {
                                        this.style.borderColor = '#cbd5e1';
                                        this.style.boxShadow = 'none';
                                    };
                                }
                            });

                            // 启用下拉框
                            const selects = ['edit-gender', 'edit-blood-type'];
                            selects.forEach(id => {
                                const element = document.getElementById(id);
                                if (element) {
                                    element.disabled = false;
                                    element.style.borderColor = '#cbd5e1';
                                    element.style.background = '#ffffff';
                                }
                            });
                        } else {
                            // 恢复编辑按钮
                            editBtn.textContent = '编辑';
                            editBtn.style.background = 'linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%)';
                            editBtn.style.border = 'none';
                            editBtn.style.color = '#ffffff';
                            editBtn.style.boxShadow = '0 2px 4px rgba(59, 130, 246, 0.2)';

                            // 禁用重置按钮
                            resetBtn.disabled = true;
                            resetBtn.style.cursor = 'not-allowed';
                            resetBtn.style.background = '#f8fafc';
                            resetBtn.style.color = '#94a3b8';
                            resetBtn.style.borderColor = '#e2e8f0';

                            // 禁用保存按钮
                            saveBtn.disabled = true;
                            saveBtn.style.background = '#f1f5f9';
                            saveBtn.style.color = '#94a3b8';
                            saveBtn.style.cursor = 'not-allowed';
                            saveBtn.style.border = '1px solid #e2e8f0';
                            saveBtn.style.boxShadow = 'none';

                            // 禁用输入框并移除事件
                            const inputs = [
                                'edit-nickname', 'edit-phone', 'edit-email', 'edit-introduction',
                                'edit-age', 'edit-height', 'edit-weight', 'edit-emergency-contact',
                                'edit-emergency-phone', 'edit-allergies', 'edit-medical-history'
                            ];
                            inputs.forEach(id => {
                                const element = document.getElementById(id);
                                if (element) {
                                    element.readOnly = true;
                                    element.style.borderColor = '#e2e8f0';
                                    element.style.background = '#ffffff';
                                    element.style.boxShadow = 'none';
                                    element.onfocus = null;
                                    element.onblur = null;
                                }
                            });

                            // 禁用下拉框
                            const selects = ['edit-gender', 'edit-blood-type'];
                            selects.forEach(id => {
                                const element = document.getElementById(id);
                                if (element) {
                                    element.disabled = true;
                                    element.style.borderColor = '#e2e8f0';
                                    element.style.background = '#ffffff';
                                }
                            });
                        }
                    }

                    // 编辑按钮
                    document.getElementById('edit-btn').onclick = function() {
                        isEditing = !isEditing;
                        window.isEditingProfile = isEditing;
                        updateButtonStates();

                        const msgElement = document.getElementById('profile-msg');
                        if (isEditing) {
                            msgElement.textContent = '✏️ 现在可以编辑个人信息';
                            msgElement.style.color = '#3b82f6';
                            msgElement.style.background = 'rgba(59, 130, 246, 0.1)';
                            msgElement.style.padding = '8px 12px';
                            msgElement.style.borderRadius = '6px';
                            msgElement.style.border = '1px solid rgba(59, 130, 246, 0.2)';
                        } else {
                            msgElement.textContent = '';
                            msgElement.style.background = 'transparent';
                            msgElement.style.padding = '0';
                            msgElement.style.border = 'none';
                        }
                    };

                    // 重置按钮
                    document.getElementById('reset-btn').onclick = function() {
                        if (!isEditing) return;

                        // 重置所有字段
                        const resetFields = [
                            { id: 'edit-nickname', value: initialData.nickname },
                            { id: 'edit-gender', value: initialData.gender },
                            { id: 'edit-phone', value: initialData.phone },
                            { id: 'edit-email', value: initialData.email },
                            { id: 'edit-introduction', value: initialData.introduction },
                            { id: 'edit-age', value: initialData.age },
                            { id: 'edit-height', value: initialData.height },
                            { id: 'edit-weight', value: initialData.weight },
                            { id: 'edit-blood-type', value: initialData.bloodType },
                            { id: 'edit-emergency-contact', value: initialData.emergencyContact },
                            { id: 'edit-emergency-phone', value: initialData.emergencyPhone },
                            { id: 'edit-allergies', value: initialData.allergies },
                            { id: 'edit-medical-history', value: initialData.medicalHistory }
                        ];

                        resetFields.forEach(field => {
                            const element = document.getElementById(field.id);
                            if (element) {
                                element.value = field.value || '';
                            }
                        });

                        const msgElement = document.getElementById('profile-msg');
                        msgElement.textContent = '🔄 已重置为初始值';
                        msgElement.style.color = '#10b981';
                        msgElement.style.background = 'rgba(16, 185, 129, 0.1)';
                        msgElement.style.padding = '8px 12px';
                        msgElement.style.borderRadius = '6px';
                        msgElement.style.border = '1px solid rgba(16, 185, 129, 0.2)';

                        setTimeout(() => {
                            const msgElement = document.getElementById('profile-msg');
                            if (msgElement) {
                                msgElement.textContent = '';
                                msgElement.style.background = 'transparent';
                                msgElement.style.padding = '0';
                                msgElement.style.border = 'none';
                            }
                        }, 3000);
                    };

                    // 保存按钮
                    document.getElementById('save-btn').onclick = function() {
                        if (!isEditing) return;

                        // 获取所有字段的值
                        const nickname = document.getElementById('edit-nickname').value.trim();
                        const gender = document.getElementById('edit-gender').value;
                        const phone = document.getElementById('edit-phone').value.trim();
                        const email = document.getElementById('edit-email').value.trim();
                        const introduction = document.getElementById('edit-introduction').value.trim();
                        const age = document.getElementById('edit-age').value.trim();
                        const height = document.getElementById('edit-height').value.trim();
                        const weight = document.getElementById('edit-weight').value.trim();
                        const bloodType = document.getElementById('edit-blood-type').value;
                        const emergencyContact = document.getElementById('edit-emergency-contact').value.trim();
                        const emergencyPhone = document.getElementById('edit-emergency-phone').value.trim();
                        const allergies = document.getElementById('edit-allergies').value.trim();
                        const medicalHistory = document.getElementById('edit-medical-history').value.trim();

                        // 验证必填字段
                        if (!nickname) {
                            const msgElement = document.getElementById('profile-msg');
                            msgElement.textContent = '❌ 昵称不能为空';
                            msgElement.style.color = '#ef4444';
                            msgElement.style.background = 'rgba(239, 68, 68, 0.1)';
                            msgElement.style.padding = '8px 12px';
                            msgElement.style.borderRadius = '6px';
                            msgElement.style.border = '1px solid rgba(239, 68, 68, 0.2)';
                            return;
                        }

                        // 验证年龄
                        if (age && (isNaN(age) || age < 0 || age > 150)) {
                            const msgElement = document.getElementById('profile-msg');
                            msgElement.textContent = '❌ 请输入有效的年龄（0-150）';
                            msgElement.style.color = '#ef4444';
                            msgElement.style.background = 'rgba(239, 68, 68, 0.1)';
                            msgElement.style.padding = '8px 12px';
                            msgElement.style.borderRadius = '6px';
                            msgElement.style.border = '1px solid rgba(239, 68, 68, 0.2)';
                            return;
                        }

                        // 验证身高
                        if (height && (isNaN(height) || height < 0 || height > 300)) {
                            const msgElement = document.getElementById('profile-msg');
                            msgElement.textContent = '❌ 请输入有效的身高（0-300cm）';
                            msgElement.style.color = '#ef4444';
                            msgElement.style.background = 'rgba(239, 68, 68, 0.1)';
                            msgElement.style.padding = '8px 12px';
                            msgElement.style.borderRadius = '6px';
                            msgElement.style.border = '1px solid rgba(239, 68, 68, 0.2)';
                            return;
                        }

                        // 验证体重
                        if (weight && (isNaN(weight) || weight < 0 || weight > 500)) {
                            const msgElement = document.getElementById('profile-msg');
                            msgElement.textContent = '❌ 请输入有效的体重（0-500kg）';
                            msgElement.style.color = '#ef4444';
                            msgElement.style.background = 'rgba(239, 68, 68, 0.1)';
                            msgElement.style.padding = '8px 12px';
                            msgElement.style.borderRadius = '6px';
                            msgElement.style.border = '1px solid rgba(239, 68, 68, 0.2)';
                            return;
                        }

                        // 更新数据
                        currentData = {
                            ...currentData,
                            nickname,
                            gender,
                            phone: phone || '未设置',
                            email: email || '未设置',
                            introduction: introduction || '这位用户很懒，什么都没有留下...',
                            age: age || '',
                            height: height || '',
                            weight: weight || '',
                            bloodType,
                            emergencyContact: emergencyContact || '',
                            emergencyPhone: emergencyPhone || '',
                            allergies: allergies || '',
                            medicalHistory: medicalHistory || ''
                        };

                        // 显示保存中状态
                        const msgElement = document.getElementById('profile-msg');
                        msgElement.textContent = '💾 保存中...';
                        msgElement.style.color = '#3b82f6';
                        msgElement.style.background = 'rgba(59, 130, 246, 0.1)';
                        msgElement.style.padding = '8px 12px';
                        msgElement.style.borderRadius = '6px';
                        msgElement.style.border = '1px solid rgba(59, 130, 246, 0.2)';

                        // 异步保存到本地存储和数据库
                        saveUserProfile(username, currentData).then(saveSuccess => {
                            if (saveSuccess) {
                                isEditing = false;
                                window.isEditingProfile = false;
                                updateButtonStates();

                                msgElement.textContent = '✅ 保存成功！';
                                msgElement.style.color = '#10b981';
                                msgElement.style.background = 'rgba(16, 185, 129, 0.1)';
                                msgElement.style.padding = '8px 12px';
                                msgElement.style.borderRadius = '6px';
                                msgElement.style.border = '1px solid rgba(16, 185, 129, 0.2)';

                                setTimeout(() => {
                                    if (msgElement) {
                                        msgElement.textContent = '';
                                        msgElement.style.background = 'transparent';
                                        msgElement.style.padding = '0';
                                        msgElement.style.border = 'none';
                                    }
                                }, 3000);

                                // 重新渲染表格以显示更新后的数据
                                renderProfileTable();
                            } else {
                                msgElement.textContent = '❌ 保存失败，请重试';
                                msgElement.style.color = '#ef4444';
                                msgElement.style.background = 'rgba(239, 68, 68, 0.1)';
                                msgElement.style.padding = '8px 12px';
                                msgElement.style.borderRadius = '6px';
                                msgElement.style.border = '1px solid rgba(239, 68, 68, 0.2)';
                            }
                        }).catch(error => {
                            console.error('保存过程中发生错误:', error);
                            msgElement.textContent = '❌ 保存失败，请重试';
                            msgElement.style.color = '#ef4444';
                            msgElement.style.background = 'rgba(239, 68, 68, 0.1)';
                            msgElement.style.padding = '8px 12px';
                            msgElement.style.borderRadius = '6px';
                            msgElement.style.border = '1px solid rgba(239, 68, 68, 0.2)';
                        });
                    };

                    // 绑定关闭按钮
                    document.getElementById('close-profile-table').onclick = function() {
                        // 清除编辑状态
                        window.isEditingProfile = false;
                        // 隐藏下拉框而不是重新渲染入口
                        dropdownContent.style.display = 'none';
                    };
                }

                // 初始只渲染入口
                renderProfileEntry();

                // 添加点击外部关闭功能（仅在非编辑状态下）
                document.addEventListener('click', function(event) {
                    // 检查点击是否在下拉框外部
                    if (!dropdownContent.contains(event.target) && !loginBtn.contains(event.target)) {
                        // 如果不在编辑状态，允许点击外部关闭
                        if (!window.isEditingProfile) {
                            clearTimeout(hideTimeout);
                            dropdownContent.style.display = 'none';
                            // 不需要重新渲染入口，只需要隐藏下拉框
                        }
                    }
                });
            }

            try {
                // 先加载所有组件
                await Promise.all([
                    loadComponent('./components/index/hero.html', 'hero-container'),
                    loadComponent('./components/index/stats.html', 'stats-container'),
                    loadComponent('./components/index/process.html', 'process-container'),
                    loadComponent('./components/index/features.html', 'features-container'),
                    loadComponent('./components/index/team.html', 'team-container'),
                    loadComponent('./components/index/testimonials.html', 'testimonials-container'),
                    loadComponent('./components/index/faq.html', 'faq-container'),
                    loadComponent('./components/index/cta.html', 'cta-container'),
                    loadComponent('./components/index/footer.html', 'footer-container'),
                    loadComponent('./components/index/modals.html', 'modals-container')
                ]);

                console.log('所有组件加载完成');

                // 等待一小段时间确保DOM完全渲染
                setTimeout(() => {
                    // 加载主要功能脚本
                    const mainScript = document.createElement('script');
                    mainScript.onload = function() {
                        console.log('main.js 加载完成');
                        // 手动触发初始化函数
                        if (typeof initializeApp === 'function') {
                            initializeApp();
                        }
                        // 初始化认证功能
                        if (typeof initializeAuth === 'function') {
                            initializeAuth();
                        }
                        // 检查认证状态
                        if (typeof initAuthCheck === 'function') {
                            initAuthCheck();
                        }

                        // 认证检查完成后，确保登录状态正确更新
                        setTimeout(() => {
                            console.log('认证检查完成，当前登录状态:', window.isLoggedIn);
                            // 不需要重新绑定事件，只需要确保状态正确
                        }, 200);
                    };
                    mainScript.src = './js/main.js';
                    document.body.appendChild(mainScript);
                }, 100);

            } catch (error) {
                console.error('组件加载失败:', error);
            }
        });

        function showNotLoggedInModal() {
            const modal = document.getElementById('notLoggedInModal');
            if (modal) {
                modal.style.display = 'flex';
                setTimeout(() => {
                    modal.style.display = 'none';
                }, 1500);
            }
        }

        function showLoginSuccessModal() {
            const modal = document.getElementById('loginSuccessModal');
            if (modal) {
                modal.style.display = 'flex';
                setTimeout(() => {
                    modal.style.display = 'none';
                }, 1500);
            }
        }

        // 手机号验证函数
        function validatePhoneNumber(phone) {
            // 移除所有空格和特殊字符，只保留数字
            const cleanPhone = phone.replace(/\D/g, '');

            // 检查长度
            if (cleanPhone.length === 0) {
                return {
                    isValid: false,
                    message: '手机号不能为空'
                };
            }

            if (cleanPhone.length !== 11) {
                return {
                    isValid: false,
                    message: `手机号必须是11位数字，当前输入了${cleanPhone.length}位`
                };
            }

            // 检查是否以1开头
            if (!cleanPhone.startsWith('1')) {
                return {
                    isValid: false,
                    message: '手机号必须以1开头'
                };
            }

            // 检查第二位数字是否有效
            const secondDigit = cleanPhone.charAt(1);
            const validSecondDigits = ['3', '4', '5', '6', '7', '8', '9'];

            if (!validSecondDigits.includes(secondDigit)) {
                return {
                    isValid: false,
                    message: `手机号第二位必须是3-9之间的数字，当前是${secondDigit}`
                };
            }

            // 检查是否全是相同数字
            if (new Set(cleanPhone).size === 1) {
                return {
                    isValid: false,
                    message: '手机号不能是相同的数字'
                };
            }

            // 更详细的号段验证
            const prefix = cleanPhone.substring(0, 3);
            const validPrefixes = [
                // 中国移动
                '134', '135', '136', '137', '138', '139', '147', '148', '150', '151', '152', '157', '158', '159',
                '172', '178', '182', '183', '184', '187', '188', '195', '198',
                // 中国联通
                '130', '131', '132', '145', '146', '155', '156', '166', '167', '171', '175', '176', '185', '186', '196',
                // 中国电信
                '133', '149', '153', '173', '174', '177', '180', '181', '189', '190', '191', '193', '199',
                // 虚拟运营商
                '170'
            ];

            if (!validPrefixes.includes(prefix)) {
                return {
                    isValid: false,
                    message: `手机号段${prefix}不是有效的中国大陆手机号段，请检查输入`
                };
            }

            return {
                isValid: true,
                message: '手机号格式正确'
            };
        }

        // 邮箱验证函数
        function validateEmail(email) {
            // 移除首尾空格
            const cleanEmail = email.trim();

            if (cleanEmail.length === 0) {
                return {
                    isValid: false,
                    message: '邮箱地址不能为空'
                };
            }

            // 基本格式检查
            if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(cleanEmail)) {
                return {
                    isValid: false,
                    message: '邮箱格式不正确，正确格式如：<EMAIL>'
                };
            }

            // 检查@符号数量
            const atCount = (cleanEmail.match(/@/g) || []).length;
            if (atCount !== 1) {
                return {
                    isValid: false,
                    message: '邮箱地址只能包含一个@符号'
                };
            }

            // 分割邮箱地址
            const [localPart, domainPart] = cleanEmail.split('@');

            // 检查本地部分（@前面的部分）
            if (localPart.length === 0) {
                return {
                    isValid: false,
                    message: '邮箱地址@符号前不能为空'
                };
            }

            if (localPart.length > 64) {
                return {
                    isValid: false,
                    message: '邮箱地址@符号前的部分不能超过64个字符'
                };
            }

            // 检查域名部分（@后面的部分）
            if (domainPart.length === 0) {
                return {
                    isValid: false,
                    message: '邮箱地址@符号后不能为空'
                };
            }

            if (!domainPart.includes('.')) {
                return {
                    isValid: false,
                    message: '邮箱域名必须包含至少一个点号'
                };
            }

            // 检查域名格式
            const domainParts = domainPart.split('.');
            if (domainParts.some(part => part.length === 0)) {
                return {
                    isValid: false,
                    message: '邮箱域名格式不正确，不能有连续的点号'
                };
            }

            // 检查顶级域名
            const topLevelDomain = domainParts[domainParts.length - 1];
            if (topLevelDomain.length < 2) {
                return {
                    isValid: false,
                    message: '邮箱顶级域名至少需要2个字符'
                };
            }

            return {
                isValid: true,
                message: '邮箱格式正确'
            };
        }

        // 用户资料数据持久化函数
        async function saveUserProfile(username, profileData) {
            try {
                // 创建包含所有字段的数据副本
                const dataToSave = {
                    nickname: profileData.nickname,
                    gender: profileData.gender,
                    age: profileData.age ? parseInt(profileData.age) : null,
                    height: profileData.height ? parseFloat(profileData.height) : null,
                    weight: profileData.weight ? parseFloat(profileData.weight) : null,
                    blood_type: profileData.bloodType,
                    phone: profileData.phone,
                    email: profileData.email,
                    emergency_contact: profileData.emergencyContact,
                    emergency_phone: profileData.emergencyPhone,
                    allergies: profileData.allergies,
                    medical_history: profileData.medicalHistory,
                    introduction: profileData.introduction
                };

                // 首先保存到localStorage作为备份
                const storageKey = `userProfile_${username}`;
                const localData = {
                    ...dataToSave,
                    lastUpdated: new Date().toISOString()
                };
                localStorage.setItem(storageKey, JSON.stringify(localData));

                // 调用后端API保存到数据库
                try {
                    const token = localStorage.getItem('auth_token');
                    if (token) {
                        const response = await fetch('/api/profile', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${token}`
                            },
                            body: JSON.stringify(dataToSave)
                        });

                        if (response.ok) {
                            const result = await response.json();
                            console.log('个人资料已保存到数据库:', result);
                        } else {
                            console.warn('保存到数据库失败，但已保存到本地存储');
                        }
                    } else {
                        console.warn('未找到认证令牌，仅保存到本地存储');
                    }
                } catch (apiError) {
                    console.warn('API调用失败，但已保存到本地存储:', apiError);
                }

                console.log(`用户 ${username} 的个人资料已保存`);
                return true;
            } catch (error) {
                console.error('保存用户资料失败:', error);
                return false;
            }
        }

        async function loadUserProfile(username) {
            try {
                // 首先尝试从后端API加载
                const token = localStorage.getItem('auth_token');
                if (token) {
                    try {
                        const response = await fetch('/api/profile', {
                            method: 'GET',
                            headers: {
                                'Authorization': `Bearer ${token}`
                            }
                        });

                        if (response.ok) {
                            const result = await response.json();
                            if (result.success && result.data) {
                                console.log(`从数据库加载用户 ${username} 的个人资料`);
                                // 转换字段名以匹配前端格式
                                const profileData = {
                                    nickname: result.data.nickname,
                                    gender: result.data.gender,
                                    age: result.data.age,
                                    height: result.data.height,
                                    weight: result.data.weight,
                                    bloodType: result.data.blood_type,
                                    phone: result.data.phone,
                                    email: result.data.email,
                                    emergencyContact: result.data.emergency_contact,
                                    emergencyPhone: result.data.emergency_phone,
                                    allergies: result.data.allergies,
                                    medicalHistory: result.data.medical_history,
                                    introduction: result.data.introduction
                                };
                                return profileData;
                            }
                        }
                    } catch (apiError) {
                        console.warn('从API加载个人资料失败，尝试从本地存储加载:', apiError);
                    }
                }

                // 如果API加载失败，从localStorage加载
                const storageKey = `userProfile_${username}`;
                const savedData = localStorage.getItem(storageKey);

                if (savedData) {
                    const profileData = JSON.parse(savedData);
                    console.log(`从本地存储加载用户 ${username} 的个人资料`);
                    return profileData;
                }

                console.log(`用户 ${username} 暂无保存的个人资料`);
                return null;
            } catch (error) {
                console.error('加载用户资料失败:', error);
                return null;
            }
        }

        // 清理用户资料数据（可选功能）
        function clearUserProfile(username) {
            try {
                const storageKey = `userProfile_${username}`;
                localStorage.removeItem(storageKey);
                console.log(`用户 ${username} 的个人资料已清除`);
                return true;
            } catch (error) {
                console.error('清除用户资料失败:', error);
                return false;
            }
        }

        // 获取所有保存的用户资料（调试用）
        function getAllUserProfiles() {
            const profiles = {};
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith('userProfile_')) {
                    const username = key.replace('userProfile_', '');
                    try {
                        profiles[username] = JSON.parse(localStorage.getItem(key));
                    } catch (error) {
                        console.error(`解析用户 ${username} 的资料失败:`, error);
                    }
                }
            }
            return profiles;
        }

        // 将函数暴露到全局作用域，方便调试
        window.saveUserProfile = saveUserProfile;
        window.loadUserProfile = loadUserProfile;
        window.clearUserProfile = clearUserProfile;
        window.getAllUserProfiles = getAllUserProfiles;

        // 示例：登录成功后调用 showLoginSuccessModal();
        // showLoginSuccessModal();
    </script>

    <!-- 页面加载动画样式 -->
    <style>
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #f8fafc;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: all 0.3s ease;
        }
        
        .loading-content {
            text-align: center;
            color: #2c5282;
        }
        
        .loading-logo {
            position: relative;
            margin-bottom: 2rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1.5rem;
        }
        
        .loading-logo h2 {
            font-size: 2rem;
            font-weight: 800;
            margin: 0;
            letter-spacing: -0.025em;
            color: #2c5282;
        }
    </style>
    <!-- 未登录提示弹窗 -->
    <div id="notLoggedInModal" class="custom-modal" style="display:none;">
        <div class="custom-modal-content">
            <span id="notLoggedInText">请先登录后查看个人资料表</span>
        </div>
    </div>
    

</body>
</html> 