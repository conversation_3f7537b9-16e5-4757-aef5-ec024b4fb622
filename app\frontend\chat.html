<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能问诊 - 慧问医答</title>
    <link rel="stylesheet" href="./css/style.css?v=4">
    <link rel="stylesheet" href="./css/chat.css?v=4">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
</head>
<body>
    <!-- 加载动画容器 -->
    <div id="loading-container"></div>

    <div class="chat-layout" id="chatApp" style="display: none; opacity: 0; transition: opacity 0.5s ease;">
        <!-- 侧边栏容器 -->
        <div id="sidebar-container"></div>
        
        <!-- 主内容区 -->
        <div class="main-content">
            <div class="chat-page">
                <!-- 聊天头部容器 -->
                <div id="header-container"></div>
                
                <div id="app" class="chat-container">
                    <!-- 聊天内容将由Vue动态渲染 -->
                </div>
                
                <!-- 输入区域容器 -->
                <div id="input-area-container"></div>
            </div>
        </div>
    </div>

    <!-- 样式和脚本将动态加载到head和body中 -->

    <!-- 先加载Vue和Axios -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <script>
        // 确保Vue和axios正确加载
        if (!window.Vue) {
            window.Vue = { createApp: function(options) { return { mount: function() { console.error('Vue未正确加载'); } }; } };
            console.error('Vue CDN加载失败，使用备用方案');
        }
        if (!window.axios) {
            window.axios = { 
                get: function() { return Promise.reject('Axios未正确加载'); },
                post: function() { return Promise.reject('Axios未正确加载'); }
            };
            console.error('Axios CDN加载失败，使用备用方案');
        }
    </script>

    <!-- 聊天功能已集成到本页面中，不再需要外部chat.js -->

    <!-- 组件加载器 -->
    <script>
        // 初始化聊天功能
        function initializeChatFunctions() {
            console.log('🔧 初始化聊天功能...');
            
            // 检查用户是否已登录
            const token = localStorage.getItem('auth_token');
            const username = localStorage.getItem('username');
            
            if (!token || !username) {
                // 如果未登录，重定向到首页
                console.log('❌ 用户未登录，重定向到首页');
                window.location.href = './index.html';
                alert('请先登录再进行问诊');
                return;
            }
            
            console.log('✅ 用户已登录:', username);

            // 返回首页按钮点击事件
            const backBtn = document.getElementById('backBtn');
            if (backBtn) {
                backBtn.addEventListener('click', function() {
                    console.log('🏠 返回首页');
                    window.location.href = './index.html';
                });
                console.log('✅ 返回首页按钮事件绑定成功');
            }




                         // 绑定文本框事件（自动调整高度、回车发送）
             const messageInput = document.getElementById('messageInput');
             if (messageInput) {
                 // 自动调整文本框高度
                 messageInput.addEventListener('input', function() {
                     this.style.height = 'auto';
                     this.style.height = (this.scrollHeight) + 'px';
                     
                     // 启用/禁用发送按钮
                     const sendBtn = document.getElementById('sendBtn');
                     if (sendBtn) {
                         sendBtn.disabled = this.value.trim() === '';
                     }
                 });
                 
                 // 回车键发送消息
                 messageInput.addEventListener('keypress', function(e) {
                     if (e.key === 'Enter' && !e.shiftKey) {
                         e.preventDefault();
                         if (this.value.trim() && window.chatAppInstance) {
                             window.chatAppInstance.sendMessage(this.value.trim());
                             this.value = '';
                             this.style.height = 'auto';
                             const sendBtn = document.getElementById('sendBtn');
                             if (sendBtn) {
                                 sendBtn.disabled = true;
                             }
                         }
                     }
                 });
                 
                 console.log('✅ 文本框事件绑定成功（自动调整+回车发送）');
             }
            
            // 新建会话按钮
            const newChatBtn = document.getElementById('newChatBtn');
            if (newChatBtn) {
                newChatBtn.addEventListener('click', function() {
                    console.log('🆕 新建会话');
                    // 这里可以添加新建会话的逻辑
                });
                console.log('✅ 新建会话按钮事件绑定成功');
            }
            
            // 历史会话按钮
            const historyBtn = document.getElementById('historyBtn');
            if (historyBtn) {
                historyBtn.addEventListener('click', function() {
                    console.log('📜 查看历史会话');
                    // 这里可以添加查看历史会话的逻辑
                });
                console.log('✅ 历史会话按钮事件绑定成功');
            }
            
            // 重载历史对话按钮
            const clearChatBtn = document.getElementById('clearChatBtn');
            if (clearChatBtn) {
                clearChatBtn.addEventListener('click', function() {
                    console.log('🔄 重载历史对话');
                    if (window.chatAppInstance && window.chatAppInstance.reloadHistorySessions) {
                        window.chatAppInstance.reloadHistorySessions();
                    }
                });
                console.log('✅ 重载历史对话按钮事件绑定成功');
            }
            
                         // 发送按钮
             const sendBtn = document.getElementById('sendBtn');
             if (sendBtn) {
                 sendBtn.addEventListener('click', function() {
                     const messageInput = document.getElementById('messageInput');
                     if (messageInput && messageInput.value.trim() && window.chatAppInstance) {
                         console.log('📤 发送消息:', messageInput.value.trim());
                         window.chatAppInstance.sendMessage(messageInput.value.trim());
                         messageInput.value = '';
                         messageInput.style.height = 'auto';
                         sendBtn.disabled = true;
                     }
                 });
                 console.log('✅ 发送按钮事件绑定成功');
             }
             
             // 回车键发送消息
             // 注意：避免重复声明 messageInput 变量
             if (messageInput) {
                 messageInput.addEventListener('keypress', function(e) {
                     if (e.key === 'Enter' && !e.shiftKey) {
                         e.preventDefault();
                         if (this.value.trim() && window.chatAppInstance) {
                             window.chatAppInstance.sendMessage(this.value.trim());
                             this.value = '';
                             this.style.height = 'auto';
                             sendBtn.disabled = true;
                         }
                     }
                 });
                 console.log('✅ 回车发送消息事件绑定成功');
             }
            
            // 初始化Vue聊天应用
            initializeChatApp();
            
            console.log('🎉 聊天功能初始化完成！');
        }

        // 初始化Vue聊天应用
        function initializeChatApp() {
            console.log('🎭 初始化Vue聊天应用...');
            
            const { createApp } = Vue;
            
            const chatApp = createApp({
                data() {
                    return {
                        messages: [
                            {
                                id: 1,
                                type: 'bot',
                                content: '您好！我是慧问医答智能助手。请描述您的症状，我将为您提供专业的健康建议。',
                                time: new Date().toLocaleString(),
                                avatar: './assets/bot-avatar.svg'
                            },
                            {
                                id: 2,
                                type: 'bot',
                                content: '您可以：\n• 直接在输入框中描述您的症状或健康问题\n• 详细说明症状的持续时间和严重程度\n• 提及任何相关的病史或用药情况',
                                time: new Date().toLocaleString(),
                                avatar: './assets/bot-avatar.svg'
                            }
                        ],
                        isTyping: false,
                        currentUser: localStorage.getItem('username') || '用户'
                    }
                },
                methods: {
                    sendMessage(content) {
                        if (!content.trim()) return;

                        // 添加用户消息
                        this.messages.push({
                            id: Date.now(),
                            type: 'user',
                            content: content.trim(),
                            time: new Date().toLocaleString(),
                            avatar: './assets/user-avatar.svg'
                        });

                        // 滚动到底部
                        this.$nextTick(() => {
                            this.scrollToBottom();
                        });

                        // 先进行意图识别，然后发送消息
                        this.recognizeIntentAndSend(content.trim());
                    },

                    async recognizeIntentAndSend(message) {
                        try {
                            console.log('🔍 开始意图识别...');
                            console.log('用户输入:', message);

                            // 设置较短的超时时间，避免影响用户体验
                            const controller = new AbortController();
                            const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时

                            // 调用意图识别API
                            const response = await fetch('/api/intent/recognize', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify({
                                    message: message
                                }),
                                signal: controller.signal
                            });

                            clearTimeout(timeoutId);

                            if (response.ok) {
                                const intentResult = await response.json();

                                // 在控制台显示意图识别结果
                                console.log('🎯 意图识别结果:');
                                console.log('  意图类型:', intentResult.intent);
                                console.log('  置信度:', intentResult.confidence);
                                console.log('  推理过程:', intentResult.reasoning);
                                console.log('  意图描述:', intentResult.description);

                                // 根据意图类型在控制台显示不同的信息
                                switch(intentResult.intent) {
                                    case 'knowledge_query':
                                        console.log('📚 检测到知识问答意图 - 将路由到知识库查询');
                                        break;
                                    case 'tool_use':
                                        console.log('🔧 检测到工具调用意图 - 将路由到工具处理链');
                                        break;
                                    case 'general_chat':
                                        console.log('💬 检测到一般聊天意图 - 将路由到对话处理链');
                                        break;
                                    default:
                                        console.log('❓ 未知意图类型');
                                }

                            } else {
                                console.warn('⚠️ 意图识别API调用失败:', response.status, '- 继续正常聊天流程');
                            }

                        } catch (error) {
                            if (error.name === 'AbortError') {
                                console.warn('⚠️ 意图识别超时 - 继续正常聊天流程');
                            } else {
                                console.warn('⚠️ 意图识别过程出错:', error.message, '- 继续正常聊天流程');
                            }
                        }

                        // 无论意图识别是否成功，都继续发送消息到原有的处理流程
                        this.sendMessageStream(message);
                    },

                    scrollToBottom() {
                        const container = this.$el;
                        container.scrollTop = container.scrollHeight;
                    },
                    
                    clearMessages() {
                        this.messages = [
                            {
                                id: 1,
                                type: 'bot',
                                content: '对话已清除。请问有什么可以帮助您的吗？',
                                time: new Date().toLocaleString(),
                                avatar: './assets/bot-avatar.svg'
                            }
                        ];
                    },

                    sendMessageStream(message) {
                        // 创建机器人消息占位符
                        const botMsg = {
                            id: Date.now() + 1,
                            type: 'bot',
                            content: '',
                            time: new Date().toLocaleString(),
                            avatar: './assets/bot-avatar.svg',
                            isStreaming: true
                        };
                        this.messages.push(botMsg);
                        this.scrollToBottom();

                        // 创建请求数据
                        const requestData = {
                            message: message,
                            session_id: this.sessionId,
                            username: this.currentUser || '用户'
                        };

                        // 获取身份验证token
                        const token = localStorage.getItem('auth_token');
                        const headers = {
                            'Content-Type': 'application/json',
                        };

                        // 如果有token，添加到请求头
                        if (token) {
                            headers['Authorization'] = `Bearer ${token}`;
                        }

                        // 使用fetch发送POST请求到流式端点
                        fetch('/api/chat/stream', {
                            method: 'POST',
                            headers: headers,
                            body: JSON.stringify(requestData)
                        })
                        .then(response => {
                            if (!response.ok) {
                                throw new Error(`HTTP error! status: ${response.status}`);
                            }

                            const reader = response.body.getReader();
                            const decoder = new TextDecoder();

                            const readStream = () => {
                                reader.read().then(({ done, value }) => {
                                    if (done) {
                                        // 流结束
                                        this.isTyping = false;
                                        botMsg.isStreaming = false;
                                        return;
                                    }

                                    // 解析SSE数据
                                    const chunk = decoder.decode(value);
                                    const lines = chunk.split('\n');

                                    for (const line of lines) {
                                        if (line.startsWith('data: ')) {
                                            try {
                                                const data = JSON.parse(line.slice(6));

                                                if (data.type === 'start') {
                                                    this.sessionId = data.session_id;
                                                    this.isTyping = false;
                                                } else if (data.type === 'chunk') {
                                                    botMsg.content += data.content;
                                                    this.$forceUpdate();
                                                    this.$nextTick(() => {
                                                        this.scrollToBottom();
                                                    });
                                                } else if (data.type === 'end') {
                                                    botMsg.content = data.full_response;
                                                    botMsg.isStreaming = false;
                                                    this.isTyping = false;
                                                } else if (data.type === 'error') {
                                                    botMsg.content = data.content;
                                                    botMsg.isStreaming = false;
                                                    this.isTyping = false;
                                                }
                                            } catch (e) {
                                                console.error('解析SSE数据出错:', e);
                                            }
                                        }
                                    }

                                    readStream(); // 继续读取
                                });
                            };

                            readStream();
                        })
                        .catch(error => {
                            console.error('流式请求出错:', error);
                            this.isTyping = false;
                            botMsg.content = '抱歉，发送消息时出现了网络错误。请检查您的网络连接并重试。';
                            botMsg.isStreaming = false;
                            this.scrollToBottom();
                        });
                    },

                    loadChatHistory(sessionId) {
                        // 获取身份验证token
                        const token = localStorage.getItem('auth_token');
                        const headers = {};

                        // 如果有token，添加到请求头
                        if (token) {
                            headers['Authorization'] = `Bearer ${token}`;
                        }

                        axios.get(`/api/chat/history?session_id=${sessionId}`, { headers })
                            .then(response => {
                                if (response.data.status === 'success') {
                                    this.messages = response.data.history.map((msg, index) => ({
                                        ...msg,
                                        id: index + 1,
                                        type: msg.role,
                                        time: new Date(msg.timestamp).toLocaleString(),
                                        avatar: msg.role === 'user'
                                            ? './assets/user-avatar.svg'
                                            : msg.role === 'doctor'
                                                ? './assets/doctor-avatar.svg'
                                                : './assets/bot-avatar.svg'
                                    }));

                                    this.$nextTick(() => {
                                        this.scrollToBottom();
                                    });
                                }
                            })
                            .catch(error => {
                                console.error('获取聊天历史出错:', error);
                            });
                    },

                    showHistorySessions() {
                        // 获取身份验证token
                        const token = localStorage.getItem('auth_token');
                        const headers = {};

                        // 如果有token，添加到请求头
                        if (token) {
                            headers['Authorization'] = `Bearer ${token}`;
                        }

                        axios.get('/api/chat/active_sessions', { headers })
                            .then(response => {
                                if (response.data.status === 'success') {
                                    this.renderSidebarSessions(response.data.sessions);
                                }
                            })
                            .catch(error => {
                                console.error('获取历史会话出错:', error);
                            });
                    },

                    renderSidebarSessions(sessions) {
                        const sessionsList = document.getElementById('sessionsList');

                        if (!sessionsList) {
                            return;
                        }

                        if (sessions.length === 0) {
                            sessionsList.innerHTML = '<div class="history-placeholder">暂无历史会话</div>';
                            return;
                        }

                        // 清空现有内容
                        sessionsList.innerHTML = '<div class="history-section-title">历史会话</div>';

                        // 排序：最近活动的在前面
                        const sortedSessions = [...sessions].sort((a, b) => b.last_activity - a.last_activity);

                        // 渲染每个会话项
                        sortedSessions.forEach(session => {
                            const sessionItem = document.createElement('div');
                            sessionItem.className = 'history-item';

                            // 格式化日期
                            const dateStr = new Date(session.last_activity).toLocaleString();

                            // 使用last_message作为标题
                            const displayTitle = session.title || session.last_message || '新会话';

                            sessionItem.innerHTML = `
                                <div class="history-content">
                                    <div class="history-title">
                                        ${displayTitle}
                                        ${session.doctor_involved ? '<span class="doctor-tag">医生已参与</span>' : ''}
                                    </div>
                                    <div class="history-meta">
                                        <span class="history-time">${dateStr}</span>
                                        <span class="session-id-preview">#${session.session_id.substring(0, 6)}</span>
                                    </div>
                                </div>
                                <div class="history-actions">
                                    <button class="delete-session-btn" onclick="event.stopPropagation(); window.chatAppInstance.deleteSession('${session.session_id}')" title="删除会话">
                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <polyline points="3,6 5,6 21,6"></polyline>
                                            <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                                            <line x1="10" y1="11" x2="10" y2="17"></line>
                                            <line x1="14" y1="11" x2="14" y2="17"></line>
                                        </svg>
                                    </button>
                                </div>
                            `;

                            sessionItem.addEventListener('click', () => {
                                this.loadChatHistory(session.session_id);
                            });

                            sessionsList.appendChild(sessionItem);
                        });
                    },

                    newChat() {
                        // 清空当前会话
                        this.messages = [
                            {
                                id: 1,
                                type: 'bot',
                                content: '您好！我是慧问医答，您的专业医疗咨询助手。请问有什么可以帮助您的吗？',
                                time: new Date().toLocaleString(),
                                avatar: './assets/bot-avatar.svg'
                            },
                            {
                                id: 2,
                                type: 'bot',
                                content: '您可以：\n• 直接在输入框中描述您的症状或健康问题\n• 详细说明症状的持续时间和严重程度\n• 提及任何相关的病史或用药情况',
                                time: new Date().toLocaleString(),
                                avatar: './assets/bot-avatar.svg'
                            }
                        ];

                        // 重置会话ID
                        this.sessionId = null;

                        // 滚动到底部
                        this.$nextTick(() => {
                            this.scrollToBottom();
                        });
                    },

                    deleteSession(sessionId) {
                        if (!confirm('确定要删除这个会话吗？删除后无法恢复。')) {
                            return;
                        }

                        axios.delete('/api/chat/delete_session', {
                            data: { session_id: sessionId }
                        })
                        .then(response => {
                            if (response.data.status === 'success') {
                                // 删除成功，重新加载历史会话列表
                                this.showHistorySessions();

                                // 如果删除的是当前会话，清空聊天界面
                                if (this.sessionId === sessionId) {
                                    this.newChat();
                                }

                                // 显示成功提示
                                this.showMessage('会话删除成功', 'success');
                            } else {
                                this.showMessage('删除失败：' + response.data.message, 'error');
                            }
                        })
                        .catch(error => {
                            console.error('删除会话出错:', error);
                            this.showMessage('删除失败，请重试', 'error');
                        });
                    },

                    showMessage(message, type = 'info') {
                        // 简单的消息提示功能
                        const messageDiv = document.createElement('div');
                        messageDiv.className = `message-toast message-${type}`;
                        messageDiv.textContent = message;
                        messageDiv.style.cssText = `
                            position: fixed;
                            top: 20px;
                            right: 20px;
                            padding: 12px 20px;
                            border-radius: 4px;
                            color: white;
                            font-size: 14px;
                            z-index: 10000;
                            background-color: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
                            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                        `;

                        document.body.appendChild(messageDiv);

                        // 3秒后自动移除
                        setTimeout(() => {
                            if (messageDiv.parentNode) {
                                messageDiv.parentNode.removeChild(messageDiv);
                            }
                        }, 3000);
                    },

                    reloadHistorySessions() {
                        // 重新加载历史会话
                        this.showHistorySessions();
                        this.showMessage('历史会话已刷新', 'success');
                    },

                    sendMessageStream(message) {
                        // 创建机器人消息占位符
                        const botMsg = {
                            id: Date.now() + 1,
                            type: 'bot',
                            content: '',
                            time: new Date().toLocaleTimeString(),
                            avatar: './assets/bot-avatar.svg',
                            isStreaming: true
                        };
                        this.messages.push(botMsg);
                        this.scrollToBottom();

                        // 创建请求数据
                        const requestData = {
                            message: message,
                            session_id: this.sessionId,
                            username: this.currentUser || '用户'
                        };

                        // 获取身份验证token
                        const token = localStorage.getItem('auth_token');
                        const headers = {
                            'Content-Type': 'application/json',
                        };

                        // 如果有token，添加到请求头
                        if (token) {
                            headers['Authorization'] = `Bearer ${token}`;
                        }

                        // 使用fetch发送POST请求到流式端点
                        fetch('/api/chat/stream', {
                            method: 'POST',
                            headers: headers,
                            body: JSON.stringify(requestData)
                        })
                        .then(response => {
                            if (!response.ok) {
                                throw new Error(`HTTP error! status: ${response.status}`);
                            }

                            const reader = response.body.getReader();
                            const decoder = new TextDecoder();

                            const readStream = () => {
                                reader.read().then(({ done, value }) => {
                                    if (done) {
                                        // 流结束
                                        this.isTyping = false;
                                        botMsg.isStreaming = false;
                                        return;
                                    }

                                    // 解析SSE数据
                                    const chunk = decoder.decode(value);
                                    const lines = chunk.split('\n');

                                    for (const line of lines) {
                                        if (line.startsWith('data: ')) {
                                            try {
                                                const data = JSON.parse(line.slice(6));

                                                if (data.type === 'start') {
                                                    this.sessionId = data.session_id;
                                                    this.isTyping = false;
                                                } else if (data.type === 'chunk') {
                                                    botMsg.content += data.content;
                                                    this.$forceUpdate();
                                                    this.$nextTick(() => {
                                                        this.scrollToBottom();
                                                    });
                                                } else if (data.type === 'end') {
                                                    botMsg.content = data.full_response;
                                                    botMsg.isStreaming = false;
                                                    this.isTyping = false;
                                                } else if (data.type === 'error') {
                                                    botMsg.content = data.content;
                                                    botMsg.isStreaming = false;
                                                    this.isTyping = false;
                                                }
                                            } catch (e) {
                                                console.error('解析SSE数据出错:', e);
                                            }
                                        }
                                    }

                                    readStream(); // 继续读取
                                });
                            };

                            readStream();
                        })
                        .catch(error => {
                            console.error('流式请求出错:', error);
                            this.isTyping = false;
                            botMsg.content = '抱歉，发送消息时出现了网络错误。请检查您的网络连接并重试。';
                            botMsg.isStreaming = false;
                            this.scrollToBottom();
                        });
                    }
                },
                
                mounted() {
                    // 滚动到底部
                    this.$nextTick(() => {
                        this.scrollToBottom();
                    });

                    // 延迟加载历史会话，确保DOM已完全加载
                    setTimeout(() => {
                        this.showHistorySessions();
                    }, 500);

                    // 绑定新建会话按钮点击事件
                    setTimeout(() => {
                        const newChatBtn = document.getElementById('newChatBtn');
                        if (newChatBtn) {
                            newChatBtn.addEventListener('click', () => {
                                this.newChat();
                            });
                        }
                    }, 100);
                },
                
                template: `
                    <div class="chat-messages">
                        <div v-for="message in messages" :key="message.id" :class="message.type + '-message'">
                            <div class="message-avatar">
                                <img :src="message.avatar" :alt="message.type" />
                            </div>
                            <div class="message-content">
                                <div class="message-text">{{ message.content }}</div>
                                <div class="message-time">{{ message.time }}</div>
                            </div>
                        </div>
                        
                        <div v-if="isTyping" class="bot-message">
                            <div class="message-avatar">
                                <img src="./assets/bot-avatar.svg" alt="AI助手" />
                            </div>
                            <div class="message-content">
                                <div class="loading-indicator">
                                    <div class="dot"></div>
                                    <div class="dot"></div>
                                    <div class="dot"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                `
            });
            
            const appInstance = chatApp.mount('#app');

            // 将Vue应用实例保存到全局，以便其他函数使用
            window.chatAppInstance = appInstance;
            
            console.log('✅ Vue聊天应用初始化成功');
        }

        // 加载动画控制函数
        function startLoadingAnimation() {
            const loadingScreen = document.getElementById('chatLoadingScreen');
            const chatApp = document.getElementById('chatApp');
            
            if (!loadingScreen || !chatApp) {
                console.error('找不到加载界面或聊天界面元素');
                return;
            }
            
            let progress = 0;
            const progressBar = document.querySelector('.progress-bar');
            const loadingText = document.querySelector('.loading-text');
            
            const loadingSteps = [
                '正在加载智能问诊系统...',
                '正在连接AI助手...',
                '正在准备对话界面...',
                '加载完成，开始问诊'
            ];
            
            const loadingInterval = setInterval(() => {
                progress += Math.random() * 30 + 15;
                
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(loadingInterval);
                    
                    console.log('🎯 加载动画完成，显示聊天界面');
                    setTimeout(() => {
                        loadingScreen.style.opacity = '0';
                        setTimeout(() => {
                            loadingScreen.style.display = 'none';
                            chatApp.style.display = 'flex';
                            setTimeout(() => {
                                chatApp.style.opacity = '1';
                            }, 50);
                        }, 300);
                    }, 400);
                }
                
                if (progressBar) {
                    progressBar.style.width = progress + '%';
                }
                if (loadingText) {
                    const stepIndex = Math.min(Math.floor(progress / 25), loadingSteps.length - 1);
                    loadingText.textContent = loadingSteps[stepIndex];
                }
            }, 80);
        }

        // 组件加载函数
        async function loadComponent(componentPath, containerId) {
            try {
                const response = await fetch(componentPath);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                const html = await response.text();
                document.getElementById(containerId).innerHTML = html;
                console.log(`✅ 组件 ${componentPath} 加载成功`);
            } catch (error) {
                console.error(`❌ 加载组件 ${componentPath} 失败:`, error);
                throw error;
            }
        }

        // 样式加载函数
        async function loadStyles(stylesPath) {
            try {
                const response = await fetch(stylesPath);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                const cssText = await response.text();
                // 提取<style>标签中的内容
                const styleContent = cssText.replace(/<\/?style[^>]*>/g, '');
                const styleElement = document.createElement('style');
                styleElement.textContent = styleContent;
                document.head.appendChild(styleElement);
                console.log(`✅ 样式 ${stylesPath} 加载成功`);
            } catch (error) {
                console.error(`❌ 加载样式 ${stylesPath} 失败:`, error);
                throw error;
            }
        }

        // 脚本加载函数
        async function loadScripts(scriptsPath) {
            try {
                const response = await fetch(scriptsPath);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                const scriptsHtml = await response.text();
                // 创建临时容器来解析脚本
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = scriptsHtml;
                
                // 执行所有script标签
                const scripts = tempDiv.querySelectorAll('script');
                scripts.forEach(script => {
                    // 跳过Vue和Axios的CDN加载（已在主文件中加载）
                    if (script.src && (script.src.includes('vue') || script.src.includes('axios'))) {
                        console.log(`⏭️ 跳过已加载的CDN: ${script.src}`);
                        return;
                    }
                    
                    // 跳过Vue和Axios的检查代码（已在主文件中处理）
                    if (script.textContent && script.textContent.includes('window.Vue') && script.textContent.includes('window.axios')) {
                        console.log(`⏭️ 跳过Vue/Axios检查代码`);
                        return;
                    }
                    
                    // 跳过事件监听器（改为手动触发）
                    if (script.textContent && (script.textContent.includes("window.addEventListener('load'") || script.textContent.includes("document.addEventListener('DOMContentLoaded'"))) {
                        console.log(`⏭️ 跳过事件监听器，改为手动初始化`);
                        return;
                    }
                    
                    if (script.src) {
                        // 外部脚本
                        const newScript = document.createElement('script');
                        newScript.src = script.src;
                        document.body.appendChild(newScript);
                    } else {
                        // 内联脚本
                        const newScript = document.createElement('script');
                        newScript.textContent = script.textContent;
                        document.body.appendChild(newScript);
                    }
                });
                console.log(`✅ 脚本 ${scriptsPath} 加载成功`);
            } catch (error) {
                console.error(`❌ 加载脚本 ${scriptsPath} 失败:`, error);
                throw error;
            }
        }

        // 页面加载完成后加载所有组件
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                console.log('🚀 开始加载聊天界面组件...');
                
                // 第一步：加载样式
                console.log('📦 加载样式...');
                await loadStyles('./components/chat/styles.html');
                
                // 第二步：并行加载HTML组件
                console.log('📦 加载HTML组件...');
                await Promise.all([
                    loadComponent('./components/chat/loading.html', 'loading-container'),
                    loadComponent('./components/chat/sidebar.html', 'sidebar-container'),
                    loadComponent('./components/chat/header.html', 'header-container'),
                    loadComponent('./components/chat/input-area.html', 'input-area-container')
                ]);

                // 第三步：最后加载脚本
                console.log('📦 加载脚本...');
                await loadScripts('./components/chat/scripts.html');

                console.log('🎉 所有聊天组件加载完成！');
                
                // 手动触发加载动画完成
                setTimeout(function() {
                    console.log('🎬 开始加载动画...');
                    startLoadingAnimation();
                    
                    // 初始化聊天功能
                    setTimeout(function() {
                        initializeChatFunctions();
                    }, 500);
                }, 100);

            } catch (error) {
                console.error('💥 组件加载失败:', error);
                // 显示错误信息给用户
                document.body.innerHTML = `
                    <div style="text-align: center; padding: 50px; color: red;">
                        <h2>页面加载失败</h2>
                        <p>错误信息: ${error.message}</p>
                        <p>请检查网络连接或联系管理员</p>
                        <button onclick="location.reload()">重新加载</button>
                    </div>
                `;
            }
        });
    </script>
</body>
</html> 