/* Hero区域样式 */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background: var(--hero-gradient);
}

.hero-section::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 40%;
    height: 120%;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 51, 234, 0.05) 100%);
    border-radius: 50%;
    filter: blur(60px);
    z-index: 1;
}

.hero-section::after {
    content: '';
    position: absolute;
    bottom: -30%;
    left: -10%;
    width: 30%;
    height: 60%;
    background: radial-gradient(circle, rgba(16, 185, 129, 0.08) 0%, transparent 70%);
    z-index: 1;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--hero-gradient);
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 2;
    min-height: 80vh;
}

.hero-content {
    color: var(--gray-900);
    position: relative;
    animation: heroFadeIn 1s ease-out;
}

@keyframes heroFadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(59, 130, 246, 0.15);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: var(--radius-full);
    padding: 0.75rem 1.25rem;
    font-size: 0.875rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
    color: var(--primary-dark);
}

.badge-icon {
    font-size: 1rem;
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 3.5rem);
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    letter-spacing: -0.025em;
    text-shadow: 0 2px 4px rgba(255, 255, 255, 0.8);
    color: var(--gray-900);
}

.highlight {
    background: linear-gradient(135deg, #f6d365 0%, #fda085 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    display: inline-block;
    text-shadow: none;
}

.hero-subtitle {
    font-size: 1.25rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    opacity: 1;
    max-width: 500px;
    color: var(--gray-700);
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
}

.hero-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.hero-primary-btn,
.hero-secondary-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 1.5rem;
    font-size: 1rem;
    font-weight: 700;
    border-radius: var(--radius-md);
    transition: all var(--duration-normal) ease;
    cursor: pointer;
    border: none;
    text-decoration: none;
    box-shadow: 0 4px 14px rgba(0, 0, 0, 0.1);
}

.hero-primary-btn .btn-icon,
.hero-secondary-btn .btn-icon {
    font-size: 1.25rem;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.hero-primary-btn .btn-icon svg,
.hero-secondary-btn .btn-icon svg {
    width: 1em;
    height: 1em;
    fill: currentColor;
    display: block;
}

.hero-primary-btn {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%) !important;
    color: var(--white) !important;
    border: none !important;
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
    font-weight: 700;
    position: relative;
    overflow: hidden;
}

.hero-primary-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.hero-primary-btn:hover::before {
    left: 100%;
}

.hero-primary-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(59, 130, 246, 0.5) !important;
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary) 100%) !important;
}

/* 确保Hero主按钮文字颜色正确 */
.hero-primary-btn,
.hero-primary-btn:hover,
.hero-primary-btn:focus,
.hero-primary-btn:active {
    color: var(--white) !important;
}

.hero-primary-btn .btn-icon,
.hero-primary-btn .btn-icon svg {
    color: var(--white) !important;
    fill: var(--white) !important;
}

/* 使用ID选择器确保样式优先级最高 */
#startConsultationBtn {
    background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%) !important;
    color: #ffffff !important;
    border: none !important;
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4) !important;
}

#startConsultationBtn:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #3b82f6 100%) !important;
    box-shadow: 0 10px 30px rgba(59, 130, 246, 0.5) !important;
}

.hero-secondary-btn {
    background: rgba(255, 255, 255, 0.9);
    color: var(--gray-800);
    border: 2px solid var(--gray-400);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    font-weight: 600;
}

.hero-secondary-btn:hover {
    background: var(--white);
    border-color: var(--gray-500);
    color: var(--gray-900);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.hero-stats {
    display: flex;
    gap: 2rem;
}

.stat-item {
    text-align: center;
}

.hero-stats .stat-number {
    font-size: 1.5rem;
    font-weight: 900;
    color: var(--gray-900) !important;
    display: block;
    text-shadow: 0 2px 4px rgba(255, 255, 255, 0.8);
}

.hero-stats .stat-label {
    font-size: 0.875rem;
    color: var(--gray-700) !important;
    opacity: 1;
    margin-top: 0.25rem;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.6);
    font-weight: 600;
}

/* Hero视觉元素 */
.hero-visual {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-visual::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
    border-radius: 50%;
    filter: blur(40px);
    z-index: 1;
}

.visual-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: var(--radius-xl);
    padding: 1.5rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.08), 0 4px 16px rgba(59, 130, 246, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.8);
    position: relative;
    z-index: 2;
}

.main-card {
    position: relative;
    z-index: 3;
    width: 320px;
    background: rgba(255, 255, 255, 0.95);
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.avatar-group {
    display: flex;
    gap: 0.5rem;
}

.avatar {
    width: 36px;
    height: 36px;
    border-radius: var(--radius-full);
    border: 2px solid var(--white);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.online-status {
    font-size: 0.75rem;
    color: var(--success-color);
    font-weight: 700;
    background: rgba(56, 161, 105, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-full);
}

.chat-preview {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.message {
    padding: 0.875rem 1.125rem;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    line-height: 1.4;
    font-weight: 500;
}

.user-msg {
    background: linear-gradient(135deg, #2c5282 0%, #3182ce 100%);
    color: var(--white);
    align-self: flex-end;
    max-width: 85%;
    box-shadow: 0 2px 8px rgba(44, 82, 130, 0.2);
}

.ai-msg {
    background: var(--gray-100);
    color: var(--gray-800);
    align-self: flex-start;
    max-width: 90%;
    border: 1px solid var(--gray-200);
}

.floating-card-1 {
    position: absolute;
    top: -2rem;
    right: -1rem;
    z-index: 2;
    width: 160px;
    animation: float 3s ease-in-out infinite;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.4);
}

.floating-card-2 {
    position: absolute;
    bottom: -1rem;
    left: -2rem;
    z-index: 2;
    width: 130px;
    animation: float 3s ease-in-out infinite reverse;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.4);
}

.mini-chart {
    display: flex;
    align-items: end;
    gap: 6px;
    height: 50px;
    margin-bottom: 0.75rem;
    padding: 0.5rem;
}

.chart-bar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    width: 10px;
    border-radius: 5px;
    transition: height var(--duration-normal) ease;
    box-shadow: 0 2px 4px rgba(44, 82, 130, 0.2);
}

.notification-dot {
    width: 14px;
    height: 14px;
    background: var(--danger-color);
    border-radius: var(--radius-full);
    margin-bottom: 0.75rem;
    animation: pulse 2s infinite;
    box-shadow: 0 0 10px rgba(229, 62, 62, 0.5);
}

.card-label {
    font-size: 0.8rem;
    color: var(--gray-700);
    font-weight: 600;
    text-align: center;
}

/* 强制确保Hero区域统计数据在浅色背景上清晰可见 */
.hero-section .hero-stats .stat-number,
.hero-section .hero-stats .stat-label,
.hero-section .stat-number,
.hero-section .stat-label {
    color: var(--gray-900) !important;
    text-shadow: 0 2px 4px rgba(255, 255, 255, 0.8) !important;
} 