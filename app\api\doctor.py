from flask import Blueprint, request, jsonify
import logging
from datetime import datetime
from ..models.chat_session import ChatSession, ChatMessage
from ..models.user import User
from ..models.database import db
from sqlalchemy import desc, func

# 设置日志记录器
logger = logging.getLogger(__name__)

# 创建蓝图
doctor_bp = Blueprint('doctor', __name__)

@doctor_bp.route('/api/doctor/patients', methods=['GET'])
def get_patients():
    """
    获取所有有活跃会话的患者列表，供医生工作台使用
    
    Response:
    {
        "status": "success",
        "patients": [
            {
                "session_id": "session_id",
                "patient_name": "患者姓名",
                "last_message": "最后一条消息内容",
                "last_message_time": "2024-01-01T12:00:00",
                "message_count": 5,
                "doctor_involved": false,
                "priority": "normal",
                "status": "active"
            }
        ]
    }
    """
    try:
        # 查询所有活跃的聊天会话，按最后活动时间排序
        sessions_query = db.session.query(
            ChatSession,
            User.username.label('patient_name'),
            func.count(ChatMessage.id).label('message_count')
        ).join(
            User, ChatSession.user_id == User.id
        ).outerjoin(
            ChatMessage, ChatSession.id == ChatMessage.session_id
        ).filter(
            ChatSession.status == 'active'
        ).group_by(
            ChatSession.id, User.username
        ).order_by(
            desc(ChatSession.last_activity)
        )
        
        sessions_data = sessions_query.all()
        
        patients = []
        for session, patient_name, message_count in sessions_data:
            # 获取最后一条消息
            last_message_obj = ChatMessage.query.filter_by(
                session_id=session.id
            ).order_by(desc(ChatMessage.timestamp)).first()
            
            last_message = ""
            last_message_time = session.last_activity
            
            if last_message_obj:
                last_message = last_message_obj.content[:50] + "..." if len(last_message_obj.content) > 50 else last_message_obj.content
                last_message_time = last_message_obj.timestamp
            
            patient_info = {
                "session_id": session.id,
                "patient_name": patient_name or "匿名患者",
                "last_message": last_message,
                "last_message_time": last_message_time.isoformat() if last_message_time else None,
                "message_count": message_count or 0,
                "doctor_involved": session.doctor_involved,
                "priority": session.priority,
                "status": session.status
            }
            patients.append(patient_info)
        
        return jsonify({
            'status': 'success',
            'patients': patients
        })
        
    except Exception as e:
        logger.error(f"Error in get_patients endpoint: {str(e)}")
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@doctor_bp.route('/api/doctor/patient/<session_id>/messages', methods=['GET'])
def get_patient_messages(session_id):
    """
    获取特定患者的聊天消息历史
    
    Response:
    {
        "status": "success",
        "session_info": {
            "session_id": "session_id",
            "patient_name": "患者姓名",
            "created_at": "2024-01-01T12:00:00",
            "doctor_involved": false
        },
        "messages": [
            {
                "id": 1,
                "role": "user",
                "content": "消息内容",
                "timestamp": "2024-01-01T12:00:00",
                "doctor_name": null
            }
        ]
    }
    """
    try:
        # 验证会话是否存在
        session = ChatSession.query.get(session_id)
        if not session:
            return jsonify({
                'status': 'error',
                'error': 'Session not found'
            }), 404
        
        # 获取患者信息
        user = User.query.get(session.user_id)
        patient_name = user.username if user else "匿名患者"
        
        # 获取会话的所有消息，按时间排序
        messages = ChatMessage.query.filter_by(
            session_id=session_id
        ).order_by(ChatMessage.timestamp).all()
        
        # 转换消息格式
        message_list = []
        for msg in messages:
            message_data = {
                "id": msg.id,
                "role": msg.role,
                "content": msg.content,
                "timestamp": msg.timestamp.isoformat() if msg.timestamp else None
            }
            
            # 如果是医生消息，添加医生名称
            if msg.role == 'doctor' and msg.doctor_name:
                message_data["doctor_name"] = msg.doctor_name
                
            message_list.append(message_data)
        
        session_info = {
            "session_id": session.id,
            "patient_name": patient_name,
            "created_at": session.created_at.isoformat() if session.created_at else None,
            "doctor_involved": session.doctor_involved,
            "priority": session.priority,
            "status": session.status
        }
        
        return jsonify({
            'status': 'success',
            'session_info': session_info,
            'messages': message_list
        })
        
    except Exception as e:
        logger.error(f"Error in get_patient_messages endpoint: {str(e)}")
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@doctor_bp.route('/api/doctor/patient/<session_id>/priority', methods=['PUT'])
def update_patient_priority(session_id):
    """
    更新患者会话的优先级
    
    Request body:
    {
        "priority": "normal|high"
    }
    
    Response:
    {
        "status": "success",
        "message": "Priority updated successfully"
    }
    """
    try:
        data = request.json
        priority = data.get('priority', 'normal')
        
        if priority not in ['normal', 'high']:
            return jsonify({
                'status': 'error',
                'error': 'Invalid priority value. Must be "normal" or "high"'
            }), 400
        
        # 验证会话是否存在
        session = ChatSession.query.get(session_id)
        if not session:
            return jsonify({
                'status': 'error',
                'error': 'Session not found'
            }), 404
        
        # 更新优先级
        session.priority = priority
        session.update_activity()  # 更新最后活动时间
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': 'Priority updated successfully'
        })
        
    except Exception as e:
        logger.error(f"Error in update_patient_priority endpoint: {str(e)}")
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500
