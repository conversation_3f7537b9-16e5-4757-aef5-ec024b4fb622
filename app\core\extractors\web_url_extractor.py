"""网页URL数据抽取器"""

import requests
from urllib.parse import urljoin, urlparse
from typing import Dict, Any, List, Set
import pandas as pd
import time

try:
    from bs4 import BeautifulSoup
except ImportError:
    BeautifulSoup = None

from .base_extractor import BaseExtractor


class WebUrlExtractor(BaseExtractor):
    """网页URL数据抽取器
    
    支持从网页URL抓取文本内容
    """
    
    def __init__(self, config: Dict[str, Any], metadata: Dict[str, Any]):
        super().__init__(config, metadata)
        self.session = requests.Session()
        
        # 设置默认headers
        default_headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        custom_headers = self.config.get('headers', {})
        default_headers.update(custom_headers)
        self.session.headers.update(default_headers)
    
    def extract(self) -> pd.DataFrame:
        """从网页URLs抽取数据"""
        if BeautifulSoup is None:
            raise ImportError("需要安装 beautifulsoup4 来处理网页内容")
        
        self.validate_config(['urls'])
        
        urls = self.config['urls']
        max_pages = self.config.get('max_pages', 10)
        timeout = self.config.get('timeout', 30)
        delay = self.config.get('delay', 1)  # 请求间延迟（秒）
        
        if not isinstance(urls, list):
            urls = [urls]
        
        data = []
        processed_urls: Set[str] = set()
        
        for base_url in urls:
            try:
                # 抓取单个URL或从起始URL开始爬取多个页面
                if max_pages == 1:
                    pages_data = self._extract_single_page(base_url, timeout)
                    data.extend(pages_data)
                else:
                    pages_data = self._extract_multiple_pages(
                        base_url, max_pages, timeout, delay, processed_urls
                    )
                    data.extend(pages_data)
                    
            except Exception as e:
                print(f"处理URL {base_url} 时出错: {str(e)}")
                continue
        
        print(f"成功抓取 {len(data)} 个页面")
        return self._create_standardized_dataframe(data)
    
    def _extract_single_page(self, url: str, timeout: int) -> List[Dict[str, Any]]:
        """抓取单个页面"""
        try:
            response = self.session.get(url, timeout=timeout)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 提取页面标题
            title = soup.find('title')
            title_text = title.get_text().strip() if title else "无标题"
            
            # 提取主要文本内容
            content = self._extract_text_content(soup)
            
            if content:
                page_metadata = {
                    'url': url,
                    'title': title_text,
                    'content_length': len(content),
                    'status_code': response.status_code,
                    'content_type': response.headers.get('content-type', ''),
                    'language': self._detect_language(soup)
                }
                
                return [{
                    'content': self._clean_content(content),
                    'metadata': page_metadata
                }]
            
        except Exception as e:
            print(f"抓取页面 {url} 失败: {str(e)}")
        
        return []
    
    def _extract_multiple_pages(self, start_url: str, max_pages: int, 
                               timeout: int, delay: int, 
                               processed_urls: Set[str]) -> List[Dict[str, Any]]:
        """从起始URL开始抓取多个页面"""
        urls_to_process = [start_url]
        data = []
        page_count = 0
        
        while urls_to_process and page_count < max_pages:
            current_url = urls_to_process.pop(0)
            
            if current_url in processed_urls:
                continue
                
            processed_urls.add(current_url)
            
            try:
                response = self.session.get(current_url, timeout=timeout)
                response.raise_for_status()
                
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # 提取当前页面内容
                title = soup.find('title')
                title_text = title.get_text().strip() if title else "无标题"
                content = self._extract_text_content(soup)
                
                if content:
                    page_metadata = {
                        'url': current_url,
                        'title': title_text,
                        'content_length': len(content),
                        'status_code': response.status_code,
                        'page_number': page_count + 1
                    }
                    
                    data.append({
                        'content': self._clean_content(content),
                        'metadata': page_metadata
                    })
                    
                    page_count += 1
                
                # 查找页面中的链接（简单实现）
                if page_count < max_pages:
                    links = self._find_internal_links(soup, current_url)
                    for link in links[:5]:  # 限制每页最多添加5个链接
                        if link not in processed_urls:
                            urls_to_process.append(link)
                
                # 延迟避免过于频繁的请求
                if delay > 0:
                    time.sleep(delay)
                    
            except Exception as e:
                print(f"抓取页面 {current_url} 失败: {str(e)}")
                continue
        
        return data
    
    def _extract_text_content(self, soup: BeautifulSoup) -> str:
        """从BeautifulSoup对象中提取文本内容"""
        # 移除脚本和样式元素
        for script in soup(["script", "style", "nav", "header", "footer"]):
            script.decompose()
        
        # 优先提取主要内容区域
        main_content = soup.find('main') or soup.find('article') or soup.find('div', class_='content')
        
        if main_content:
            text = main_content.get_text()
        else:
            # 如果没有找到主要内容区域，提取body中的所有文本
            body = soup.find('body')
            text = body.get_text() if body else soup.get_text()
        
        # 清理文本
        lines = (line.strip() for line in text.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        text = ' '.join(chunk for chunk in chunks if chunk)
        
        return text
    
    def _find_internal_links(self, soup: BeautifulSoup, base_url: str) -> List[str]:
        """查找页面中的内部链接"""
        links = []
        base_domain = urlparse(base_url).netloc
        
        for link in soup.find_all('a', href=True):
            href = link['href']
            full_url = urljoin(base_url, href)
            
            # 只添加同域名的链接
            if urlparse(full_url).netloc == base_domain:
                links.append(full_url)
        
        return list(set(links))  # 去重
    
    def _detect_language(self, soup: BeautifulSoup) -> str:
        """检测页面语言"""
        # 尝试从html标签的lang属性获取
        html_tag = soup.find('html')
        if html_tag and html_tag.get('lang'):
            return html_tag.get('lang')
        
        # 尝试从meta标签获取
        meta_lang = soup.find('meta', attrs={'http-equiv': 'content-language'})
        if meta_lang and meta_lang.get('content'):
            return meta_lang.get('content')
        
        return 'unknown'
        
    def __del__(self):
        """清理资源"""
        if hasattr(self, 'session'):
            self.session.close() 