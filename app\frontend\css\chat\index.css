/* 聊天界面样式主入口文件 */

/* 1. CSS变量定义 - 必须最先加载 */
@import './variables.css';

/* 2. 基础样式 - 全局重置和基础样式 */
@import './base.css';

/* 3. 布局样式 - 容器和布局相关 */
@import './layout.css';

/* 4. 头部样式 - 聊天头部组件 */
@import './header.css';

/* 5. 消息样式 - 消息气泡和相关样式 */
@import './messages.css';

/* 6. 输入样式 - 输入区域组件 */
@import './input.css';

/* 7. 其他组件 - 加载器、空状态等 */
@import './components.css';

/* 8. 响应式样式 - 媒体查询，最后加载 */
@import './responsive.css'; 