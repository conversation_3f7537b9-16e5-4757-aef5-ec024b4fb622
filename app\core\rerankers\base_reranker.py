"""基础重排器抽象类

定义重排器的统一接口和基础功能
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Tuple
from langchain_core.documents import Document


class BaseReranker(ABC):
    """重排器基类
    
    定义了重排器的统一接口，所有重排器实现都应该继承此类
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化重排器
        
        Args:
            config: 重排器配置
        """
        self.config = config or {}
        self.model_name = self.config.get('model_name', 'default')
        self.top_n = self.config.get('top_n', 20)
        self.batch_size = self.config.get('batch_size', 32)
        self.device = self.config.get('device', 'cpu')
        
    @abstractmethod
    def rerank(
        self, 
        query: str, 
        documents: List[Document], 
        top_n: Optional[int] = None
    ) -> List[Document]:
        """重排文档
        
        Args:
            query: 查询文本
            documents: 待重排的文档列表
            top_n: 返回的文档数量，如果为None则使用配置中的值
            
        Returns:
            重排后的文档列表，按相关性分数降序排列
        """
        pass
    
    @abstractmethod
    def compute_scores(
        self, 
        query: str, 
        documents: List[Document]
    ) -> List[float]:
        """计算查询与文档的相关性分数
        
        Args:
            query: 查询文本
            documents: 文档列表
            
        Returns:
            相关性分数列表，与文档列表一一对应
        """
        pass
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息
        
        Returns:
            模型信息字典
        """
        return {
            'model_name': self.model_name,
            'reranker_type': self.__class__.__name__,
            'top_n': self.top_n,
            'batch_size': self.batch_size,
            'device': self.device,
            'config': self.config
        }
    
    def _prepare_documents_for_scoring(
        self, 
        query: str, 
        documents: List[Document]
    ) -> List[Tuple[str, str]]:
        """准备用于评分的查询-文档对
        
        Args:
            query: 查询文本
            documents: 文档列表
            
        Returns:
            查询-文档对列表
        """
        pairs = []
        for doc in documents:
            content = doc.page_content
            # 如果文档内容过长，进行截断
            max_length = self.config.get('max_content_length', 512)
            if len(content) > max_length:
                content = content[:max_length] + "..."
            pairs.append((query, content))
        return pairs
    
    def _add_rerank_scores_to_metadata(
        self, 
        documents: List[Document], 
        scores: List[float]
    ) -> List[Document]:
        """将重排分数添加到文档元数据中
        
        Args:
            documents: 文档列表
            scores: 分数列表
            
        Returns:
            更新了元数据的文档列表
        """
        for doc, score in zip(documents, scores):
            doc.metadata['rerank_score'] = score
            doc.metadata['reranker_model'] = self.model_name
        return documents
    
    def validate_inputs(self, query: str, documents: List[Document]) -> bool:
        """验证输入参数
        
        Args:
            query: 查询文本
            documents: 文档列表
            
        Returns:
            验证是否通过
        """
        if not query or not query.strip():
            raise ValueError("查询文本不能为空")
        
        if not documents:
            raise ValueError("文档列表不能为空")
        
        if not all(isinstance(doc, Document) for doc in documents):
            raise ValueError("所有元素都必须是Document对象")
        
        return True
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息
        
        Returns:
            性能统计字典
        """
        return {
            'total_rerank_calls': getattr(self, '_total_calls', 0),
            'total_documents_processed': getattr(self, '_total_docs', 0),
            'average_batch_size': getattr(self, '_avg_batch_size', 0),
            'model_info': self.get_model_info()
        }
    
    def _update_stats(self, num_documents: int):
        """更新统计信息
        
        Args:
            num_documents: 处理的文档数量
        """
        if not hasattr(self, '_total_calls'):
            self._total_calls = 0
            self._total_docs = 0
        
        self._total_calls += 1
        self._total_docs += num_documents
        self._avg_batch_size = self._total_docs / self._total_calls
