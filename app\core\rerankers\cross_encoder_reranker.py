"""Cross-encoder重排器实现

实现SRH-002可插拔结果重排功能，使用Cross-encoder模型对搜索结果进行精排
"""

import torch
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from langchain_core.documents import Document

from .base_reranker import BaseReranker


class CrossEncoderReranker(BaseReranker):
    """Cross-encoder重排器
    
    使用Cross-encoder模型（如bge-reranker）对搜索结果进行精排，
    实现SRH-002功能要求。
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化Cross-encoder重排器
        
        Args:
            config: 重排器配置，包含模型名称、设备等信息
        """
        super().__init__(config)
        
        # 默认配置
        self.model_name = self.config.get('model_name', 'BAAI/bge-reranker-base')
        self.max_length = self.config.get('max_length', 512)
        self.normalize_scores = self.config.get('normalize_scores', True)
        
        # 初始化模型
        self.model = None
        self.tokenizer = None
        self._load_model()
        
        print(f"✅ CrossEncoderReranker初始化完成")
        print(f"   - 模型: {self.model_name}")
        print(f"   - 设备: {self.device}")
        print(f"   - Top-N: {self.top_n}")
        print(f"   - 批处理大小: {self.batch_size}")
    
    def _load_model(self):
        """加载Cross-encoder模型"""
        try:
            # 尝试使用sentence-transformers
            try:
                from sentence_transformers import CrossEncoder
                self.model = CrossEncoder(
                    self.model_name,
                    max_length=self.max_length,
                    device=self.device
                )
                self._model_type = 'sentence_transformers'
                print(f"✅ 使用sentence-transformers加载模型: {self.model_name}")
                return
            except ImportError:
                print("⚠️  sentence-transformers未安装，尝试使用transformers")
            
            # 回退到transformers
            try:
                from transformers import AutoTokenizer, AutoModelForSequenceClassification
                self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
                self.model = AutoModelForSequenceClassification.from_pretrained(self.model_name)
                
                # 移动模型到指定设备
                if torch.cuda.is_available() and self.device == 'cuda':
                    self.model = self.model.cuda()
                elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available() and self.device == 'mps':
                    self.model = self.model.to('mps')
                
                self.model.eval()
                self._model_type = 'transformers'
                print(f"✅ 使用transformers加载模型: {self.model_name}")
                return
            except Exception as e:
                print(f"⚠️  transformers加载失败: {e}")
            
            # 最后的回退方案
            print("⚠️  无法加载Cross-encoder模型，使用简化版本")
            self._model_type = 'fallback'
            
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            self._model_type = 'fallback'
    
    def rerank(
        self, 
        query: str, 
        documents: List[Document], 
        top_n: Optional[int] = None
    ) -> List[Document]:
        """重排文档
        
        Args:
            query: 查询文本
            documents: 待重排的文档列表
            top_n: 返回的文档数量
            
        Returns:
            重排后的文档列表
        """
        # 验证输入
        self.validate_inputs(query, documents)
        
        # 确定返回数量
        if top_n is None:
            top_n = self.top_n
        
        # 如果文档数量少于等于top_n，直接返回
        if len(documents) <= top_n:
            top_n = len(documents)
        
        print(f"🔄 开始重排 {len(documents)} 个文档，返回Top-{top_n}")
        
        # 计算相关性分数
        scores = self.compute_scores(query, documents)
        
        # 将分数添加到文档元数据
        documents_with_scores = self._add_rerank_scores_to_metadata(documents, scores)
        
        # 按分数排序
        sorted_docs = sorted(
            zip(documents_with_scores, scores),
            key=lambda x: x[1],
            reverse=True
        )
        
        # 返回Top-N结果
        reranked_docs = [doc for doc, _ in sorted_docs[:top_n]]
        
        # 更新统计信息
        self._update_stats(len(documents))
        
        print(f"✅ 重排完成，返回 {len(reranked_docs)} 个文档")
        return reranked_docs
    
    def compute_scores(self, query: str, documents: List[Document]) -> List[float]:
        """计算查询与文档的相关性分数
        
        Args:
            query: 查询文本
            documents: 文档列表
            
        Returns:
            相关性分数列表
        """
        if self._model_type == 'sentence_transformers':
            return self._compute_scores_sentence_transformers(query, documents)
        elif self._model_type == 'transformers':
            return self._compute_scores_transformers(query, documents)
        else:
            return self._compute_scores_fallback(query, documents)
    
    def _compute_scores_sentence_transformers(
        self, 
        query: str, 
        documents: List[Document]
    ) -> List[float]:
        """使用sentence-transformers计算分数"""
        # 准备查询-文档对
        pairs = self._prepare_documents_for_scoring(query, documents)
        
        # 批量计算分数
        scores = []
        for i in range(0, len(pairs), self.batch_size):
            batch_pairs = pairs[i:i + self.batch_size]
            batch_scores = self.model.predict(batch_pairs)
            scores.extend(batch_scores.tolist())
        
        # 标准化分数
        if self.normalize_scores:
            scores = self._normalize_scores(scores)
        
        return scores
    
    def _compute_scores_transformers(
        self, 
        query: str, 
        documents: List[Document]
    ) -> List[float]:
        """使用transformers计算分数"""
        scores = []
        
        with torch.no_grad():
            for i in range(0, len(documents), self.batch_size):
                batch_docs = documents[i:i + self.batch_size]
                batch_pairs = [(query, doc.page_content) for doc in batch_docs]
                
                # 编码输入
                inputs = self.tokenizer(
                    batch_pairs,
                    padding=True,
                    truncation=True,
                    max_length=self.max_length,
                    return_tensors='pt'
                )
                
                # 移动到设备
                if self.device == 'cuda' and torch.cuda.is_available():
                    inputs = {k: v.cuda() for k, v in inputs.items()}
                elif self.device == 'mps' and hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                    inputs = {k: v.to('mps') for k, v in inputs.items()}
                
                # 前向传播
                outputs = self.model(**inputs)
                batch_scores = outputs.logits.squeeze(-1).cpu().numpy()
                
                if batch_scores.ndim == 0:
                    batch_scores = [float(batch_scores)]
                else:
                    batch_scores = batch_scores.tolist()
                
                scores.extend(batch_scores)
        
        # 标准化分数
        if self.normalize_scores:
            scores = self._normalize_scores(scores)
        
        return scores
    
    def _compute_scores_fallback(
        self, 
        query: str, 
        documents: List[Document]
    ) -> List[float]:
        """回退方案：使用简单的文本匹配计算分数"""
        print("⚠️  使用回退方案计算重排分数")
        
        query_terms = set(query.lower().split())
        scores = []
        
        for doc in documents:
            content = doc.page_content.lower()
            content_terms = set(content.split())
            
            # 计算Jaccard相似度
            intersection = len(query_terms & content_terms)
            union = len(query_terms | content_terms)
            
            if union == 0:
                score = 0.0
            else:
                score = intersection / union
            
            # 添加长度惩罚
            length_penalty = min(1.0, len(content) / 1000)
            score *= length_penalty
            
            scores.append(score)
        
        return scores
    
    def _normalize_scores(self, scores: List[float]) -> List[float]:
        """标准化分数到[0, 1]范围"""
        if not scores:
            return scores
        
        scores_array = np.array(scores)
        min_score = scores_array.min()
        max_score = scores_array.max()
        
        if max_score == min_score:
            return [0.5] * len(scores)
        
        normalized = (scores_array - min_score) / (max_score - min_score)
        return normalized.tolist()
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        info = super().get_model_info()
        info.update({
            'model_type': self._model_type,
            'max_length': self.max_length,
            'normalize_scores': self.normalize_scores,
            'model_loaded': self.model is not None
        })
        return info
