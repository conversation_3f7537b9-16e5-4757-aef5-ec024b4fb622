"""基础数据抽取器抽象类"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List
import pandas as pd
from datetime import datetime


class BaseExtractor(ABC):
    """数据抽取器基类
    
    所有具体的数据抽取器都应该继承这个基类，并实现 extract 方法。
    """
    
    def __init__(self, config: Dict[str, Any], metadata: Dict[str, Any]):
        """初始化抽取器
        
        Args:
            config: 数据源配置
            metadata: 元数据信息
        """
        self.config = config
        self.metadata = metadata
        self.extracted_at = datetime.now()
    
    @abstractmethod
    def extract(self) -> pd.DataFrame:
        """抽取数据并返回标准化的DataFrame
        
        Returns:
            包含content和metadata列的标准化DataFrame
        """
        pass
    
    def _create_standardized_dataframe(self, data: List[Dict[str, Any]]) -> pd.DataFrame:
        """创建标准化的DataFrame
        
        Args:
            data: 包含content和metadata的数据列表
            
        Returns:
            标准化的DataFrame
        """
        if not data:
            return pd.DataFrame(columns=['content', 'metadata'])
        
        df = pd.DataFrame(data)
        
        # 确保包含必需的列
        if 'content' not in df.columns:
            raise ValueError("数据必须包含 'content' 列")
        
        if 'metadata' not in df.columns:
            df['metadata'] = [{}] * len(df)
        
        # 添加通用元数据
        for i, row in df.iterrows():
            if isinstance(df.at[i, 'metadata'], dict):
                df.at[i, 'metadata'].update({
                    'extracted_at': self.extracted_at.isoformat(),
                    'extractor_type': self.__class__.__name__,
                    **self.metadata
                })
            else:
                df.at[i, 'metadata'] = {
                    'extracted_at': self.extracted_at.isoformat(),
                    'extractor_type': self.__class__.__name__,
                    **self.metadata
                }
        
        return df[['content', 'metadata']]
    
    def _clean_content(self, content: str) -> str:
        """清理文本内容
        
        Args:
            content: 原始文本内容
            
        Returns:
            清理后的文本内容
        """
        if not content or not isinstance(content, str):
            return ""
        
        # 去除多余的空白字符
        content = ' '.join(content.split())
        
        # 去除特殊字符（可根据需要调整）
        content = content.strip()
        
        return content
    
    def validate_config(self, required_fields: List[str]) -> None:
        """验证配置是否包含必需的字段
        
        Args:
            required_fields: 必需的配置字段列表
            
        Raises:
            ValueError: 当配置缺少必需字段时
        """
        missing_fields = [field for field in required_fields if field not in self.config]
        if missing_fields:
            raise ValueError(f"配置缺少必需字段: {missing_fields}") 