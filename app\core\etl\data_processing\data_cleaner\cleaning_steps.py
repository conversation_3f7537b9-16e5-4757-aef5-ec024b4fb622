"""数据清洗步骤实现

提供各种可配置的数据清洗步骤实现
"""

import re
import importlib
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Callable
import pandas as pd


class CleaningStep(ABC):
    """数据清洗步骤的抽象基类"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化清洗步骤
        
        Args:
            config: 清洗步骤的配置参数
        """
        self.config = config
        self.step_type = config.get('type', 'unknown')
        self.description = config.get('description', f'{self.step_type} cleaning step')
    
    @abstractmethod
    def apply(self, df: pd.DataFrame) -> pd.DataFrame:
        """应用清洗步骤
        
        Args:
            df: 要清洗的DataFrame
            
        Returns:
            清洗后的DataFrame
        """
        pass
    
    def validate_config(self, required_keys: List[str]) -> None:
        """验证配置参数
        
        Args:
            required_keys: 必需的配置键列表
            
        Raises:
            ValueError: 当缺少必需的配置参数时
        """
        missing_keys = [key for key in required_keys if key not in self.config]
        if missing_keys:
            raise ValueError(f"{self.step_type} 清洗步骤缺少必需的配置参数: {missing_keys}")


class RegexCleaningStep(CleaningStep):
    """正则表达式替换清洗步骤
    
    用于去除水印、页眉页脚、特殊字符等
    
    配置示例:
    {
        "type": "regex",
        "pattern": r"页码：\d+",
        "replace_with": "",
        "columns": ["content"],  # 可选，默认对所有文本列应用
        "flags": ["IGNORECASE"]  # 可选，正则表达式标志
    }
    """
    
    def apply(self, df: pd.DataFrame) -> pd.DataFrame:
        """应用正则表达式替换"""
        self.validate_config(['pattern'])
        
        pattern = self.config['pattern']
        replace_with = self.config.get('replace_with', '')
        columns = self.config.get('columns', None)
        flags_list = self.config.get('flags', [])
        
        # 处理正则表达式标志
        flags = 0
        for flag_name in flags_list:
            if hasattr(re, flag_name):
                flags |= getattr(re, flag_name)
        
        df_copy = df.copy()
        
        # 确定要处理的列
        if columns is None:
            # 自动检测文本列
            text_columns = df_copy.select_dtypes(include=['object']).columns.tolist()
        else:
            text_columns = [col for col in columns if col in df_copy.columns]
        
        # 应用正则表达式替换
        for col in text_columns:
            if col in df_copy.columns:
                df_copy[col] = df_copy[col].astype(str).str.replace(
                    pattern, replace_with, regex=True, flags=flags
                )
        
        return df_copy


class DropNaCleaningStep(CleaningStep):
    """空值删除清洗步骤
    
    配置示例:
    {
        "type": "dropna",
        "subset": ["content"],  # 可选，指定检查空值的列
        "how": "any"  # 可选，'any' 或 'all'
    }
    """
    
    def apply(self, df: pd.DataFrame) -> pd.DataFrame:
        """删除包含空值的行"""
        subset = self.config.get('subset', None)
        how = self.config.get('how', 'any')
        
        df_copy = df.copy()
        
        # 验证subset中的列是否存在
        if subset:
            missing_cols = [col for col in subset if col not in df_copy.columns]
            if missing_cols:
                print(f"警告: 列 {missing_cols} 不存在，将被忽略")
                subset = [col for col in subset if col in df_copy.columns]
        
        return df_copy.dropna(subset=subset, how=how)


class FillNaCleaningStep(CleaningStep):
    """空值填充清洗步骤
    
    配置示例:
    {
        "type": "fillna",
        "value": "N/A",  # 填充值
        "columns": ["content"],  # 可选，指定要填充的列
        "method": "value"  # 可选，'value', 'forward', 'backward'
    }
    """
    
    def apply(self, df: pd.DataFrame) -> pd.DataFrame:
        """填充空值"""
        method = self.config.get('method', 'value')
        columns = self.config.get('columns', None)
        
        df_copy = df.copy()
        
        if method == 'value':
            self.validate_config(['value'])
            fill_value = self.config['value']
            
            if columns:
                # 只填充指定列
                for col in columns:
                    if col in df_copy.columns:
                        df_copy[col] = df_copy[col].fillna(fill_value)
            else:
                # 填充所有列
                df_copy = df_copy.fillna(fill_value)
                
        elif method == 'forward':
            if columns:
                for col in columns:
                    if col in df_copy.columns:
                        df_copy[col] = df_copy[col].fillna(method='ffill')
            else:
                df_copy = df_copy.fillna(method='ffill')
                
        elif method == 'backward':
            if columns:
                for col in columns:
                    if col in df_copy.columns:
                        df_copy[col] = df_copy[col].fillna(method='bfill')
            else:
                df_copy = df_copy.fillna(method='bfill')
        
        return df_copy


class DropDuplicatesCleaningStep(CleaningStep):
    """数据去重清洗步骤
    
    配置示例:
    {
        "type": "drop_duplicates",
        "subset": ["content"],  # 可选，指定用于判断重复的列
        "keep": "first"  # 可选，'first', 'last', False
    }
    """
    
    def apply(self, df: pd.DataFrame) -> pd.DataFrame:
        """删除重复行"""
        subset = self.config.get('subset', None)
        keep = self.config.get('keep', 'first')
        
        df_copy = df.copy()
        
        # 验证subset中的列是否存在
        if subset:
            missing_cols = [col for col in subset if col not in df_copy.columns]
            if missing_cols:
                print(f"警告: 列 {missing_cols} 不存在，将被忽略")
                subset = [col for col in subset if col in df_copy.columns]
                if not subset:
                    subset = None
        
        return df_copy.drop_duplicates(subset=subset, keep=keep)


class ApplyFunctionCleaningStep(CleaningStep):
    """自定义函数应用清洗步骤
    
    允许开发者应用自定义的复杂清洗逻辑
    
    配置示例:
    {
        "type": "apply",
        "function_path": "scripts.custom_cleaners.remove_medical_codes",
        "columns": ["content"],  # 可选，指定要应用函数的列
        "function_args": {},  # 可选，传递给函数的额外参数
        "apply_to": "column"  # 可选，'column' 或 'dataframe'
    }
    """
    
    def apply(self, df: pd.DataFrame) -> pd.DataFrame:
        """应用自定义函数"""
        self.validate_config(['function_path'])
        
        function_path = self.config['function_path']
        columns = self.config.get('columns', None)
        function_args = self.config.get('function_args', {})
        apply_to = self.config.get('apply_to', 'column')
        
        # 动态导入函数
        try:
            module_path, function_name = function_path.rsplit('.', 1)
            module = importlib.import_module(module_path)
            func = getattr(module, function_name)
        except (ImportError, AttributeError) as e:
            raise ValueError(f"无法导入函数 {function_path}: {e}")
        
        df_copy = df.copy()
        
        if apply_to == 'dataframe':
            # 对整个DataFrame应用函数
            return func(df_copy, **function_args)
        else:
            # 对指定列应用函数
            if columns is None:
                # 如果没有指定列，对所有文本列应用
                columns = df_copy.select_dtypes(include=['object']).columns.tolist()
            
            for col in columns:
                if col in df_copy.columns:
                    df_copy[col] = df_copy[col].apply(lambda x: func(x, **function_args))
        
        return df_copy
