/* 组件样式 */

/* 按钮基础样式 */
.btn {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    font-size: 0.875rem;
    font-weight: 600;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--duration-normal) ease;
    border: none;
    line-height: 1;
    white-space: nowrap;
    text-decoration: none;
}

.btn .btn-icon {
    font-size: 1rem;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.btn .btn-icon svg {
    width: 1em;
    height: 1em;
    fill: currentColor;
    display: block;
}

.login-btn {
    background: var(--white);
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.login-btn:hover {
    background: var(--gray-100);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.register-btn {
    background: var(--primary-color);
    color: var(--white);
}

.register-btn:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    align-items: center;
    justify-content: center;
    animation: modalFadeIn var(--duration-normal) ease;
}

.modal.show {
    display: flex;
}

@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: var(--white);
    padding: 2.5rem;
    border-radius: var(--radius-lg);
    width: 100%;
    max-width: 420px;
    position: relative;
    box-shadow: var(--shadow-xl);
    animation: modalSlideIn var(--duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.close-btn {
    position: absolute;
    right: 1rem;
    top: 1rem;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--gray-500);
    transition: color var(--duration-fast) ease;
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: var(--gray-100);
}

.close-btn:hover {
    color: var(--gray-900);
    background: var(--gray-200);
}

.modal h2 {
    margin-bottom: 2rem;
    color: var(--gray-900);
    text-align: center;
    font-size: 1.75rem;
    font-weight: 700;
}

/* 表单组件 */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--gray-800);
    font-weight: 600;
    font-size: 0.875rem;
}

.form-group input {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: 1rem;
    background: var(--white);
    color: var(--gray-800);
    transition: all var(--duration-normal) ease;
}

.form-group input::placeholder {
    color: var(--gray-400);
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.2);
}

.submit-btn {
    width: 100%;
    background: var(--primary-color);
    color: white;
    padding: 1rem;
    font-size: 1.125rem;
    font-weight: 700;
    margin-top: 1.5rem;
    box-shadow: var(--shadow-md);
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: var(--secondary-color);
}

.form-switch {
    margin-top: 1.5rem;
    text-align: center;
    font-size: 0.875rem;
    color: var(--gray-600);
}

.form-switch a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: color var(--duration-fast) ease;
}

.form-switch a:hover {
    text-decoration: underline;
}

.role-selection {
    display: flex;
    gap: 1.5rem;
    margin: 1rem 0;
}

.role-option {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-sm);
    transition: background-color var(--duration-fast) ease;
}

.role-option:hover {
    background: var(--gray-100);
}

.role-option input[type="radio"] {
    margin-right: 0.5rem;
    accent-color: var(--primary-color);
}

.role-label {
    font-size: 0.875rem;
    color: var(--gray-700);
    font-weight: 500;
}

.form-hint {
    color: var(--gray-500);
    font-size: 0.75rem;
    margin-top: 0.25rem;
    display: block;
    font-style: italic;
} 