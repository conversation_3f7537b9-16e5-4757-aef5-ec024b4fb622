/* 聊天消息样式 */

/* 消息行容器 */
.message-row {
    width: 100%;
    display: flex;
    flex-direction: column;
    margin-bottom: 0.5rem;
}

.user-row {
    align-items: flex-end;
}

.bot-row {
    align-items: flex-start;
}

/* 消息气泡基础样式 */
.message {
    display: flex;
    max-width: 70%;
    animation: messageSlideIn var(--duration-normal) ease;
    position: relative;
    align-items: flex-start;
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 用户消息 */
.user-message {
    flex-direction: row-reverse;
    margin-left: auto;
}

/* 机器人消息 */
.bot-message {
    flex-direction: row;
    margin-right: auto;
}

/* 头像样式 */
.message-avatar {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: var(--radius-full);
    overflow: hidden;
    flex-shrink: 0;
    box-shadow: var(--shadow-sm);
    transition: transform var(--duration-normal) ease;
    position: relative;
}

.message-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-message .message-avatar {
    margin-left: 0.75rem;
}

.bot-message .message-avatar {
    margin-right: 0.75rem;
}

/* 消息内容容器 */
.message-content {
    position: relative;
    padding: 1rem 1.25rem;
    border-radius: var(--radius-lg);
    max-width: none;
    min-width: 3rem;
    flex: 1;
    transition: all var(--duration-normal) ease;
    word-wrap: break-word;
    overflow-wrap: break-word;
    box-shadow: var(--shadow-sm);
}

/* 用户消息样式 */
.user-message .message-content {
    background: var(--user-message-bg);
    color: var(--user-message-text);
    border-bottom-right-radius: var(--radius-sm);
}

/* 机器人消息样式 */
.bot-message .message-content {
    background: var(--bot-message-bg);
    color: var(--bot-message-text);
    border-bottom-left-radius: var(--radius-sm);
    border: 1px solid var(--gray-200);
}

/* 消息文本 */
.message-text {
    margin-bottom: 0.25rem;
    line-height: 1.6;
    white-space: normal;
    word-break: keep-all;
    overflow-wrap: break-word;
    word-spacing: normal;
    font-size: 0.95rem;
    text-align: left;
}

/* 时间戳 */
.message-time {
    font-size: 0.7rem;
    color: var(--gray-500);
    text-align: right;
    margin-top: 0.25rem;
    font-weight: 500;
}

.user-message .message-time {
    color: rgba(255, 255, 255, 0.8);
}

/* 消息日期分割线 */
.message-date {
    text-align: center;
    margin: 1.5rem 0 1rem;
    position: relative;
}

.message-date span {
    background: rgba(255, 255, 255, 0.9);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-full);
    font-size: 0.75rem;
    color: var(--gray-600);
    font-weight: 600;
    box-shadow: var(--shadow-sm);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

/* Hover效果增强 */
.message-content:hover {
    transform: translateY(-1px);
}

.user-message .message-content:hover,
.doctor-message .message-content:hover {
    box-shadow: var(--shadow-xl);
}

.bot-message .message-content:hover {
    box-shadow: var(--shadow-lg);
} 