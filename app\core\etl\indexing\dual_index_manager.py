"""双重索引管理器

管理向量数据库和关键词搜索引擎的双重索引，实现ETL-005功能
"""

from typing import Dict, Any, List, Optional, Tuple
import pandas as pd
from datetime import datetime
from enum import Enum

from ..data_processing.data_standardizer import DataStandardizer
from .metadata_validator import MetadataValidator
from ...vector_store import get_vector_store
from ...search_engines import BaseSearchEngine, ElasticsearchEngine, SimpleSearchEngine


class IndexMode(Enum):
    """索引模式枚举"""
    BOTH = "both"           # 同时索引到向量数据库和搜索引擎
    VECTOR_ONLY = "vector_only"    # 仅索引到向量数据库
    KEYWORD_ONLY = "keyword_only"  # 仅索引到搜索引擎


class DualIndexManager:
    """双重索引管理器
    
    负责将处理好的文本块及其元数据原子化地加载到向量数据库和关键词搜索引擎中
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """初始化双重索引管理器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        
        # 初始化组件
        self.metadata_validator = MetadataValidator()
        self.data_standardizer = DataStandardizer()
        
        # 向量存储配置
        self.vector_store = None
        self.vector_collection_name = self.config.get('vector_collection_name', 'medical_documents')
        
        # 搜索引擎配置
        self.search_engine = None
        self.search_index_name = self.config.get('search_index_name', 'medical_documents')
        
        # 初始化存储组件
        self._init_vector_store()
        self._init_search_engine()
        
        # 索引统计
        self.indexing_stats = {
            'total_processed': 0,
            'vector_indexed': 0,
            'keyword_indexed': 0,
            'failed_records': 0,
            'last_indexing_time': None
        }
    
    def _init_vector_store(self):
        """初始化向量存储"""
        try:
            self.vector_store = get_vector_store()
            print("✅ 向量存储初始化成功")
        except Exception as e:
            print(f"⚠️  向量存储初始化失败: {e}")
            self.vector_store = None
    
    def _init_search_engine(self):
        """初始化搜索引擎"""
        engine_type = self.config.get('search_engine_type', 'simple')
        engine_config = self.config.get('search_engine_config', {})
        
        try:
            if engine_type == 'elasticsearch':
                self.search_engine = ElasticsearchEngine(engine_config)
            else:
                self.search_engine = SimpleSearchEngine(engine_config)
            
            print(f"✅ 搜索引擎初始化成功: {engine_type}")
            
        except Exception as e:
            print(f"⚠️  搜索引擎初始化失败，使用简化版本: {e}")
            self.search_engine = SimpleSearchEngine(engine_config)
    
    def index_dataframe(self, df: pd.DataFrame, mode: IndexMode = IndexMode.BOTH,
                       content_column: str = 'content', metadata_column: str = 'metadata',
                       source_name: str = "Unknown") -> Dict[str, Any]:
        """索引DataFrame到双重索引
        
        Args:
            df: 输入DataFrame
            mode: 索引模式
            content_column: 内容列名
            metadata_column: 元数据列名
            source_name: 数据源名称
            
        Returns:
            索引结果统计
        """
        if df.empty:
            print(f"数据源 {source_name}: 输入DataFrame为空，跳过索引")
            return self._create_empty_result()
        
        print(f"\n开始双重索引: {source_name}")
        print(f"索引模式: {mode.value}")
        print(f"输入数据量: {len(df)} 条记录")
        
        start_time = datetime.now()
        
        try:
            # 1. 验证和丰富元数据
            validated_df = self._validate_and_enrich_metadata(df, metadata_column, source_name)
            
            # 2. 根据模式执行索引
            vector_result = {}
            keyword_result = {}
            
            if mode in [IndexMode.BOTH, IndexMode.VECTOR_ONLY]:
                vector_result = self._index_to_vector_store(
                    validated_df, content_column, metadata_column, source_name
                )
            
            if mode in [IndexMode.BOTH, IndexMode.KEYWORD_ONLY]:
                keyword_result = self._index_to_search_engine(
                    validated_df, content_column, metadata_column, source_name
                )
            
            # 3. 统计结果
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            
            result = {
                'source_name': source_name,
                'mode': mode.value,
                'input_records': len(df),
                'processed_records': len(validated_df),
                'vector_result': vector_result,
                'keyword_result': keyword_result,
                'processing_time': processing_time,
                'success': True,
                'timestamp': end_time.isoformat()
            }
            
            # 更新统计信息
            self._update_stats(result)
            
            print(f"双重索引完成:")
            print(f"  处理记录数: {len(validated_df)}")
            print(f"  向量索引: {'✅' if vector_result.get('success', False) else '❌'}")
            print(f"  关键词索引: {'✅' if keyword_result.get('success', False) else '❌'}")
            print(f"  处理时间: {processing_time:.3f} 秒")
            
            return result
            
        except Exception as e:
            print(f"❌ 双重索引失败: {e}")
            return {
                'source_name': source_name,
                'mode': mode.value,
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def _validate_and_enrich_metadata(self, df: pd.DataFrame, metadata_column: str, 
                                    source_name: str) -> pd.DataFrame:
        """验证和丰富元数据"""
        print("  验证和丰富元数据...")
        
        # 验证元数据
        try:
            validated_df = self.metadata_validator.validate_dataframe_metadata(df, metadata_column)
        except Exception as e:
            print(f"  ⚠️  元数据验证失败，尝试修复: {e}")
            validated_df = self._repair_metadata(df, metadata_column, source_name)
        
        # 丰富元数据
        for index, row in validated_df.iterrows():
            metadata = row[metadata_column]
            if isinstance(metadata, dict):
                enriched_metadata = self.metadata_validator.enrich_metadata(
                    metadata, 
                    {'indexing_source': source_name, 'indexing_timestamp': datetime.now().isoformat()}
                )
                validated_df.at[index, metadata_column] = enriched_metadata
        
        return validated_df

    def _repair_metadata(self, df: pd.DataFrame, metadata_column: str,
                        source_name: str) -> pd.DataFrame:
        """修复缺失的元数据"""
        repaired_df = df.copy()

        for index, row in df.iterrows():
            metadata = row.get(metadata_column, {})
            if not isinstance(metadata, dict):
                metadata = {}

            # 补充必需字段
            if 'chunk_id' not in metadata:
                metadata['chunk_id'] = f"{source_name}_{index}"

            if 'document_id' not in metadata:
                metadata['document_id'] = f"{source_name}_{index // 10}"  # 假设每10个chunk为一个文档

            if 'source_type' not in metadata:
                metadata['source_type'] = 'unknown'

            if 'source_location' not in metadata:
                metadata['source_location'] = source_name

            repaired_df.at[index, metadata_column] = metadata

        return repaired_df

    def _index_to_vector_store(self, df: pd.DataFrame, content_column: str,
                              metadata_column: str, source_name: str) -> Dict[str, Any]:
        """索引到向量存储"""
        if not self.vector_store:
            return {'success': False, 'error': '向量存储未初始化'}

        print("  索引到向量存储...")

        try:
            # 准备文档数据
            documents = []
            metadatas = []
            ids = []

            for _, row in df.iterrows():
                content = str(row[content_column])
                metadata = row[metadata_column]

                if isinstance(metadata, dict):
                    chunk_id = metadata.get('chunk_id', f"chunk_{len(documents)}")

                    # 过滤复杂的元数据，只保留基本类型
                    filtered_metadata = {}
                    for key, value in metadata.items():
                        if isinstance(value, (str, int, float, bool)) or value is None:
                            filtered_metadata[key] = value
                        elif isinstance(value, dict):
                            # 将嵌套字典转换为字符串
                            filtered_metadata[f"{key}_json"] = str(value)
                        elif isinstance(value, list):
                            # 将列表转换为字符串
                            filtered_metadata[f"{key}_list"] = str(value)
                        else:
                            # 其他类型转换为字符串
                            filtered_metadata[f"{key}_str"] = str(value)

                    documents.append(content)
                    metadatas.append(filtered_metadata)
                    ids.append(chunk_id)

            # 添加到向量存储
            if documents:
                self.vector_store.add_texts(
                    texts=documents,
                    metadatas=metadatas,
                    ids=ids
                )

                return {
                    'success': True,
                    'indexed_count': len(documents),
                    'collection_name': self.vector_collection_name
                }
            else:
                return {'success': False, 'error': '没有有效文档可索引'}

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _index_to_search_engine(self, df: pd.DataFrame, content_column: str,
                               metadata_column: str, source_name: str) -> Dict[str, Any]:
        """索引到搜索引擎"""
        if not self.search_engine:
            return {'success': False, 'error': '搜索引擎未初始化'}

        print("  索引到搜索引擎...")

        try:
            # 使用搜索引擎的add_dataframe方法
            success = self.search_engine.add_dataframe(
                df, content_column, metadata_column, self.search_index_name
            )

            if success:
                doc_count = self.search_engine.get_document_count(self.search_index_name)
                return {
                    'success': True,
                    'indexed_count': len(df),
                    'total_documents': doc_count,
                    'index_name': self.search_index_name
                }
            else:
                return {'success': False, 'error': '搜索引擎索引失败'}

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _create_empty_result(self) -> Dict[str, Any]:
        """创建空结果"""
        return {
            'success': True,
            'input_records': 0,
            'processed_records': 0,
            'vector_result': {'success': True, 'indexed_count': 0},
            'keyword_result': {'success': True, 'indexed_count': 0},
            'processing_time': 0,
            'timestamp': datetime.now().isoformat()
        }

    def _update_stats(self, result: Dict[str, Any]):
        """更新统计信息"""
        self.indexing_stats['total_processed'] += result.get('processed_records', 0)

        vector_result = result.get('vector_result', {})
        if vector_result.get('success', False):
            self.indexing_stats['vector_indexed'] += vector_result.get('indexed_count', 0)

        keyword_result = result.get('keyword_result', {})
        if keyword_result.get('success', False):
            self.indexing_stats['keyword_indexed'] += keyword_result.get('indexed_count', 0)

        if not result.get('success', True):
            self.indexing_stats['failed_records'] += result.get('input_records', 0)

        self.indexing_stats['last_indexing_time'] = result.get('timestamp')

    def clear_indices(self, mode: IndexMode = IndexMode.BOTH) -> Dict[str, Any]:
        """清空索引

        Args:
            mode: 清空模式

        Returns:
            清空结果
        """
        print(f"\n清空索引 (模式: {mode.value})")

        results = {}

        if mode in [IndexMode.BOTH, IndexMode.VECTOR_ONLY]:
            if self.vector_store:
                try:
                    # ChromaDB的清空方法（需要根据实际实现调整）
                    # self.vector_store.delete_collection()
                    results['vector_cleared'] = True
                    print("✅ 向量存储已清空")
                except Exception as e:
                    results['vector_cleared'] = False
                    results['vector_error'] = str(e)
                    print(f"❌ 向量存储清空失败: {e}")

        if mode in [IndexMode.BOTH, IndexMode.KEYWORD_ONLY]:
            if self.search_engine:
                try:
                    success = self.search_engine.clear_index(self.search_index_name)
                    results['keyword_cleared'] = success
                    if success:
                        print("✅ 搜索引擎索引已清空")
                    else:
                        print("❌ 搜索引擎索引清空失败")
                except Exception as e:
                    results['keyword_cleared'] = False
                    results['keyword_error'] = str(e)
                    print(f"❌ 搜索引擎清空失败: {e}")

        return results

    def get_index_stats(self) -> Dict[str, Any]:
        """获取索引统计信息"""
        stats = self.indexing_stats.copy()

        # 添加当前索引状态
        if self.vector_store:
            try:
                # 获取向量存储统计（需要根据实际实现调整）
                stats['vector_store_status'] = 'available'
            except:
                stats['vector_store_status'] = 'error'
        else:
            stats['vector_store_status'] = 'unavailable'

        if self.search_engine:
            try:
                doc_count = self.search_engine.get_document_count(self.search_index_name)
                stats['search_engine_documents'] = doc_count
                stats['search_engine_status'] = 'available'
            except:
                stats['search_engine_status'] = 'error'
        else:
            stats['search_engine_status'] = 'unavailable'

        return stats
