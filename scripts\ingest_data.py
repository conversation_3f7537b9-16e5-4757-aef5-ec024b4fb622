import argparse
from app.services.knowledge_service import load_and_ingest


def main():
    parser = argparse.ArgumentParser(description="Ingest documents into the vector store.")
    parser.add_argument(
        "--source",
        type=str,
        required=True,
        help="The path to the directory containing documents to ingest."
    )
    args = parser.parse_args()

    load_and_ingest(data_path=args.source)


if __name__ == "__main__":
    main()