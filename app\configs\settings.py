from typing import Optional

import yaml
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings, SettingsConfigDict
from pathlib import Path

# Project Directories
ROOT = Path(__file__).resolve().parent.parent.parent


class ModelConfig(BaseModel):
    provider: str
    name: str
    temperature: float


class EmbeddingConfig(BaseModel):
    provider: str
    name: str


class VectorStoreConfig(BaseModel):
    persist_directory: str
    collection_name: str


class TextSplitterConfig(BaseModel):
    chunk_size: int
    chunk_overlap: int


class HybridSearchWeightsConfig(BaseModel):
    vector: float = 0.5
    keyword: float = 0.5


class HybridSearchRRFConfig(BaseModel):
    k: int = 60


class HybridSearchResultsConfig(BaseModel):
    top_k: int = 10


class SearchEngineSimpleConfig(BaseModel):
    index_name: str = "medical_knowledge"
    persist_directory: str = "data/search_index"


class SearchEngineElasticsearchConfig(BaseModel):
    host: str = "localhost"
    port: int = 9200
    index_name: str = "medical_knowledge"
    username: Optional[str] = None
    password: Optional[str] = None


class SearchEngineConfig(BaseModel):
    type: str = "simple"
    simple: SearchEngineSimpleConfig = SearchEngineSimpleConfig()
    elasticsearch: SearchEngineElasticsearchConfig = SearchEngineElasticsearchConfig()


class HybridSearchConfig(BaseModel):
    enabled: bool = True
    weights: HybridSearchWeightsConfig = HybridSearchWeightsConfig()
    rrf: HybridSearchRRFConfig = HybridSearchRRFConfig()
    results: HybridSearchResultsConfig = HybridSearchResultsConfig()
    search_engine: SearchEngineConfig = SearchEngineConfig()


class RerankerModelConfig(BaseModel):
    name: str = "BAAI/bge-reranker-base"
    device: str = "cpu"
    max_length: int = 512
    normalize_scores: bool = True


class RerankerParametersConfig(BaseModel):
    top_n: int = 20
    batch_size: int = 32
    max_content_length: int = 512


class RerankerPerformanceConfig(BaseModel):
    use_cache: bool = True
    enable_batching: bool = True
    timeout: int = 30


class RerankerConfig(BaseModel):
    enabled: bool = True
    type: str = "bge_reranker"
    model: RerankerModelConfig = RerankerModelConfig()
    parameters: RerankerParametersConfig = RerankerParametersConfig()
    performance: RerankerPerformanceConfig = RerankerPerformanceConfig()


class RetrievalConfig(BaseModel):
    reranker: RerankerConfig = RerankerConfig()


class Config(BaseModel):
    llm: ModelConfig
    embedding: EmbeddingConfig
    vector_store: VectorStoreConfig
    text_splitter: TextSplitterConfig
    hybrid_search: HybridSearchConfig = HybridSearchConfig()
    retrieval: RetrievalConfig = RetrievalConfig()


def load_config(config_path: Path = ROOT / "app/configs/model_config.yaml") -> Config:
    with open(config_path, "r", encoding="utf-8") as f:
        config_data = yaml.safe_load(f)
    return Config(**config_data)


class APISettings(BaseSettings):
    # Load from .env file
    model_config = SettingsConfigDict(
        env_file=str(ROOT / ".env"),
        env_file_encoding="utf-8",
        extra="ignore"  # 忽略额外的字段
    )

    # API Keys
    OPENAI_API_KEY: str = Field(default="your-api-key-here")

    # API Base URL
    OPENAI_BASE_URL: Optional[str] = Field(default=None, alias="OPENAI_BASE_URL")


# Create a single instance of settings and config to be used across the application
api_settings = APISettings()
config = load_config()

# Set the API key in the environment for Langchain to pick it up
import os

os.environ["OPENAI_API_KEY"] = api_settings.OPENAI_API_KEY

if api_settings.OPENAI_BASE_URL:
    os.environ["OPENAI_BASE_URL"] = api_settings.OPENAI_BASE_URL