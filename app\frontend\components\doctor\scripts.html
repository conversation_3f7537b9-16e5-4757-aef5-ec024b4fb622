<!-- 医生工作台脚本 -->
<script>
    console.log('🔥 scripts.html 已被加载！');
    console.log('🔍 正在尝试加载 doctor.js...');
</script>
<script src="/js/doctor.js"></script>
<script>
    // 医生工作台初始化代码 - 立即执行
    console.log('✅ 医生工作台脚本已加载');

    // 等待一小段时间确保DOM元素已加载
    setTimeout(() => {
        console.log('🚀 开始初始化医生工作台功能');

        // 检查关键DOM元素是否存在
        const patientList = document.getElementById('patientList');
        const chatMessages = document.getElementById('chatMessages');
        console.log('🔍 检查关键DOM元素:', {
            patientList: patientList ? '存在' : '不存在',
            chatMessages: chatMessages ? '存在' : '不存在'
        });

        // 确保医生工作台功能正确初始化
        if (typeof initializeMessageInput === 'function') {
            console.log('⌨️ 初始化消息输入功能');
            initializeMessageInput();
        } else {
            console.log('⚠️ initializeMessageInput 函数不存在');
        }

        if (typeof initializeJoinSessionModal === 'function') {
            console.log('🔗 初始化加入会话模态框');
            initializeJoinSessionModal();
        } else {
            console.log('⚠️ initializeJoinSessionModal 函数不存在');
        }

        if (typeof loadPatientList === 'function') {
            console.log('📋 开始加载患者列表');
            loadPatientList();
        } else {
            console.error('❌ loadPatientList 函数未找到');
        }

        // 定期刷新患者列表
        console.log('⏰ 设置定时刷新患者列表 (30秒间隔)');
        setInterval(() => {
            if (typeof loadPatientList === 'function') {
                console.log('🔄 定时刷新患者列表');
                loadPatientList();
            }
        }, 30000);
    }, 500);
</script>
