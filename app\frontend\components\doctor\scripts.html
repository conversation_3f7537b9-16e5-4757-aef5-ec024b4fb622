<!-- 医生工作台脚本 -->
<script src="/js/doctor.js"></script>
<script>
    // 医生工作台初始化代码 - 立即执行
    console.log('医生工作台脚本已加载');

    // 等待一小段时间确保DOM元素已加载
    setTimeout(() => {
        console.log('开始初始化医生工作台功能');

        // 确保医生工作台功能正确初始化
        if (typeof initializeMessageInput === 'function') {
            console.log('初始化消息输入功能');
            initializeMessageInput();
        }

        if (typeof initializeJoinSessionModal === 'function') {
            console.log('初始化加入会话模态框');
            initializeJoinSessionModal();
        }

        if (typeof loadPatientList === 'function') {
            console.log('加载患者列表');
            loadPatientList();
        } else {
            console.error('loadPatientList 函数未找到');
        }

        // 定期刷新患者列表
        setInterval(() => {
            if (typeof loadPatientList === 'function') {
                loadPatientList();
            }
        }, 30000);
    }, 500);
</script>
