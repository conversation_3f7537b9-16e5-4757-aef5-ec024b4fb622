"""重排器功能测试

测试SRH-002可插拔结果重排功能，验证Cross-encoder模型的精排效果
"""

import sys
import os
from typing import List, Dict, Any
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from langchain_core.documents import Document
from app.core.rerankers import (
    BaseReranker, 
    CrossEncoderReranker, 
    create_reranker, 
    RerankerFactory,
    get_reranker
)
from app.core.retrievers.ensemble_retriever import create_ensemble_retriever
from app.core.chains import get_rag_chain


class TestCrossEncoderReranker:
    """测试Cross-encoder重排器"""
    
    def test_reranker_creation(self):
        """测试重排器创建"""
        # 测试默认配置
        reranker = CrossEncoderReranker()
        assert isinstance(reranker, BaseReranker)
        assert reranker.model_name == 'BAAI/bge-reranker-base'
        assert reranker.top_n == 20
        print("✅ 默认重排器创建测试通过")
        
        # 测试自定义配置
        config = {
            'model_name': 'cross-encoder/ms-marco-MiniLM-L-6-v2',
            'top_n': 10,
            'batch_size': 16,
            'device': 'cpu',
            'max_length': 256
        }
        custom_reranker = CrossEncoderReranker(config)
        assert custom_reranker.model_name == config['model_name']
        assert custom_reranker.top_n == config['top_n']
        assert custom_reranker.batch_size == config['batch_size']
        print("✅ 自定义重排器创建测试通过")
    
    def test_reranker_basic_functionality(self):
        """测试重排器基本功能"""
        reranker = CrossEncoderReranker({
            'top_n': 5,
            'batch_size': 8
        })
        
        # 创建测试文档
        query = "高血压的症状和治疗方法"
        documents = [
            Document(
                page_content="高血压是一种常见的心血管疾病，主要症状包括头痛、头晕、心悸等。治疗方法包括药物治疗和生活方式调整。",
                metadata={"source": "medical_book", "chunk_id": "1"}
            ),
            Document(
                page_content="糖尿病是一种代谢性疾病，需要控制血糖水平。",
                metadata={"source": "medical_book", "chunk_id": "2"}
            ),
            Document(
                page_content="高血压患者应该定期监测血压，遵医嘱服药。",
                metadata={"source": "medical_guide", "chunk_id": "3"}
            ),
            Document(
                page_content="心脏病的预防包括健康饮食和适量运动。",
                metadata={"source": "health_tips", "chunk_id": "4"}
            ),
            Document(
                page_content="高血压的药物治疗包括ACE抑制剂、利尿剂等。",
                metadata={"source": "drug_guide", "chunk_id": "5"}
            )
        ]
        
        # 测试重排功能
        start_time = time.time()
        reranked_docs = reranker.rerank(query, documents, top_n=3)
        end_time = time.time()
        
        # 验证结果
        assert len(reranked_docs) == 3
        assert all(isinstance(doc, Document) for doc in reranked_docs)
        assert all('rerank_score' in doc.metadata for doc in reranked_docs)
        assert all('reranker_model' in doc.metadata for doc in reranked_docs)
        
        # 验证分数排序
        scores = [doc.metadata['rerank_score'] for doc in reranked_docs]
        assert scores == sorted(scores, reverse=True)
        
        print(f"✅ 重排器基本功能测试通过")
        print(f"   - 处理时间: {end_time - start_time:.3f}秒")
        print(f"   - 重排结果数量: {len(reranked_docs)}")
        
        # 显示重排结果
        for i, doc in enumerate(reranked_docs, 1):
            score = doc.metadata['rerank_score']
            content = doc.page_content[:50] + "..."
            print(f"   {i}. 分数: {score:.4f} - {content}")

        # 验证相关性提升
        # 第一个结果应该是最相关的（包含"高血压"和"症状"/"治疗"）
        top_doc = reranked_docs[0]
        assert "高血压" in top_doc.page_content
        print("   ✅ 重排器成功提升了相关性")
    
    def test_compute_scores(self):
        """测试分数计算功能"""
        reranker = CrossEncoderReranker()
        
        query = "心肌梗死的诊断方法"
        documents = [
            Document(page_content="心电图是诊断心肌梗死的重要方法"),
            Document(page_content="血液检查可以检测心肌酶的升高"),
            Document(page_content="糖尿病需要控制血糖水平")
        ]
        
        scores = reranker.compute_scores(query, documents)
        
        # 验证分数
        assert len(scores) == len(documents)
        assert all(isinstance(score, (int, float)) for score in scores)
        
        print("✅ 分数计算功能测试通过")
        print(f"   - 查询: {query}")
        for i, (doc, score) in enumerate(zip(documents, scores)):
            content = doc.page_content[:30] + "..."
            print(f"   {i+1}. 分数: {score:.4f} - {content}")


class TestRerankerFactory:
    """测试重排器工厂"""
    
    def test_factory_creation(self):
        """测试工厂创建重排器"""
        # 测试BGE重排器
        bge_reranker = RerankerFactory.create_bge_reranker()
        assert isinstance(bge_reranker, BaseReranker)
        assert 'bge-reranker' in bge_reranker.model_name.lower()
        print("✅ BGE重排器工厂创建测试通过")
        
        # 测试轻量级重排器
        lightweight_reranker = RerankerFactory.create_lightweight_reranker()
        assert isinstance(lightweight_reranker, BaseReranker)
        assert 'minilm' in lightweight_reranker.model_name.lower()
        print("✅ 轻量级重排器工厂创建测试通过")
        
        # 测试通用工厂方法
        custom_reranker = create_reranker(
            'cross_encoder',
            {'model_name': 'test-model', 'top_n': 15}
        )
        assert custom_reranker.model_name == 'test-model'
        assert custom_reranker.top_n == 15
        print("✅ 通用工厂方法测试通过")
    
    def test_available_rerankers(self):
        """测试可用重排器列表"""
        available = RerankerFactory.get_available_rerankers()
        
        assert isinstance(available, dict)
        assert len(available) > 0
        assert 'bge_reranker' in available
        assert 'cross_encoder' in available
        
        print("✅ 可用重排器列表测试通过")
        print(f"   - 可用重排器数量: {len(available)}")
        for name, info in available.items():
            print(f"   - {name}: {info['class']}")


class TestEnsembleRetrieverWithReranker:
    """测试集成重排器的混合检索器"""
    
    def test_ensemble_retriever_with_reranker(self):
        """测试启用重排器的混合检索器"""
        # 创建启用重排器的混合检索器
        reranker_config = {
            'type': 'bge_reranker',
            'model': {
                'name': 'BAAI/bge-reranker-base',
                'device': 'cpu',
                'max_length': 512
            },
            'parameters': {
                'top_n': 10,
                'batch_size': 16
            }
        }
        
        retriever = create_ensemble_retriever(
            vector_weight=0.6,
            keyword_weight=0.4,
            rrf_k=60,
            top_k=5,
            reranker_config=reranker_config,
            enable_reranking=True
        )
        
        # 验证重排器已启用
        assert retriever.enable_reranking == True
        assert retriever.reranker is not None
        
        print("✅ 启用重排器的混合检索器创建测试通过")
        print(f"   - 重排器类型: {type(retriever.reranker).__name__}")
        print(f"   - 重排器模型: {retriever.reranker.model_name}")
    
    def test_retriever_stats_with_reranker(self):
        """测试包含重排器信息的统计"""
        retriever = create_ensemble_retriever(
            enable_reranking=True,
            reranker_config={'type': 'bge_reranker'}
        )
        
        stats = retriever.get_search_stats()
        
        # 验证统计信息包含重排器信息
        assert 'reranking_enabled' in stats
        assert stats['reranking_enabled'] == True
        assert 'reranker_info' in stats
        
        print("✅ 重排器统计信息测试通过")
        print(f"   - 重排器启用: {stats['reranking_enabled']}")
        if 'reranker_info' in stats:
            reranker_info = stats['reranker_info']
            print(f"   - 重排器模型: {reranker_info.get('model_name', 'Unknown')}")
            print(f"   - 重排器类型: {reranker_info.get('reranker_type', 'Unknown')}")


class TestRerankerConfiguration:
    """测试重排器配置"""
    
    def test_config_loading(self):
        """测试配置加载"""
        from app.configs.settings import config
        
        # 验证重排器配置存在
        assert hasattr(config, 'retrieval')
        assert hasattr(config.retrieval, 'reranker')
        
        reranker_config = config.retrieval.reranker
        assert hasattr(reranker_config, 'enabled')
        assert hasattr(reranker_config, 'type')
        assert hasattr(reranker_config, 'model')
        assert hasattr(reranker_config, 'parameters')
        
        print("✅ 重排器配置加载测试通过")
        print(f"   - 重排器启用: {reranker_config.enabled}")
        print(f"   - 重排器类型: {reranker_config.type}")
        print(f"   - 模型名称: {reranker_config.model.name}")
        print(f"   - Top-N: {reranker_config.parameters.top_n}")
    
    def test_rag_chain_with_reranker(self):
        """测试RAG链的重排器集成"""
        try:
            # 测试创建启用重排器的RAG链
            rag_chain = get_rag_chain()
            assert rag_chain is not None
            print("✅ 启用重排器的RAG链创建测试通过")
            
        except Exception as e:
            print(f"⚠️  RAG链重排器测试跳过（可能缺少依赖）: {e}")


def run_reranker_tests():
    """运行所有重排器测试"""
    print("🧪 开始重排器功能测试")
    print("=" * 50)
    
    # 测试Cross-encoder重排器
    print("\n📋 测试Cross-encoder重排器")
    reranker_test = TestCrossEncoderReranker()
    reranker_test.test_reranker_creation()
    reranker_test.test_reranker_basic_functionality()
    reranker_test.test_compute_scores()
    
    # 测试重排器工厂
    print("\n📋 测试重排器工厂")
    factory_test = TestRerankerFactory()
    factory_test.test_factory_creation()
    factory_test.test_available_rerankers()
    
    # 测试集成重排器的混合检索器
    print("\n📋 测试集成重排器的混合检索器")
    ensemble_test = TestEnsembleRetrieverWithReranker()
    ensemble_test.test_ensemble_retriever_with_reranker()
    ensemble_test.test_retriever_stats_with_reranker()
    
    # 测试重排器配置
    print("\n📋 测试重排器配置")
    config_test = TestRerankerConfiguration()
    config_test.test_config_loading()
    config_test.test_rag_chain_with_reranker()
    
    print("\n" + "=" * 50)
    print("🎉 重排器功能测试完成！")


if __name__ == "__main__":
    run_reranker_tests()
