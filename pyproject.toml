[tool.poetry]
name = "llm-app-scaffold"
version = "0.1.0"
description = "A professional, beginner-friendly scaffold for building commercial LangChain applications."
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
package-mode = false
packages = [{ include = "app" }]

[[tool.poetry.source]]
name = "pytorch"
url = "https://download.pytorch.org/whl/cu128" # 这里替换成自己的版本
priority = "explicit"

[tool.poetry.dependencies]
python = "^3.10"

# ✅ LangChain 0.3.24 及其相关依赖
langchain = "^0.3.26"
langchain-core = "^0.3.68"
langchain-community = "^0.3.27"
langchain-openai = "^0.3.27"

# Embedding
sentence-transformers = "^4.1.0"
torch = { source = "pytorch" }
torchvision = { source = "pytorch" }
torchaudio = { source = "pytorch" }

# ⚙️ API Framework
fastapi = "^0.111.0"
uvicorn = { extras = ["standard"], version = "^0.29.0" }

# ⚙️ Config Management
pydantic = "^2.11.7"

# 📚 Vector Store & Data Loading
chromadb = "^0.5.0"
unstructured = "^0.18.5"

# 🔧 Utilities
python-dotenv = "^1.0.1"
tiktoken = "^0.7.0"
pypdf = "^4.2.0"

[tool.poetry.group.dev.dependencies]
poethepoet = "^0.36.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poe.tasks]
start = "uvicorn app.api.main:app --host 0.0.0.0 --port 12345 --reload"

[tool.poetry.scripts]
ingest = "scripts.ingest_data:main"