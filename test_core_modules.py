#!/usr/bin/env python3
"""
综合测试脚本 - 测试 app/core 目录下的核心功能模块
包含：向量存储、混合检索、RRF融合、搜索引擎、重排器、RAG链等
"""

import os
import sys
import traceback
from typing import List, Dict, Any
import time

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from langchain_core.documents import Document

# 测试结果统计
test_results = {
    'total': 0,
    'passed': 0,
    'failed': 0,
    'skipped': 0,
    'details': []
}

def log_test_result(test_name: str, status: str, details: str = ""):
    """记录测试结果"""
    test_results['total'] += 1
    test_results[status] += 1
    test_results['details'].append({
        'test': test_name,
        'status': status,
        'details': details
    })
    
    status_emoji = {
        'passed': '✅',
        'failed': '❌',
        'skipped': '⏭️'
    }
    print(f"{status_emoji.get(status, '❓')} {test_name}: {status.upper()}")
    if details and status == 'failed':
        print(f"   错误详情: {details}")

def test_vector_store():
    """测试向量存储功能"""
    test_name = "向量存储测试"
    try:
        from app.core.vector_store import get_vector_store, get_embedding_model
        
        # 测试嵌入模型
        embeddings = get_embedding_model()
        print(f"   嵌入模型类型: {type(embeddings).__name__}")
        
        # 测试向量存储
        vector_store = get_vector_store()
        print(f"   向量存储类型: {type(vector_store).__name__}")
        
        # 测试文档数量
        try:
            collection = vector_store._collection
            count = collection.count()
            print(f"   现有文档数量: {count}")
        except:
            print("   无法获取文档数量（可能是空集合）")
        
        log_test_result(test_name, 'passed', f"向量存储初始化成功")
        
    except Exception as e:
        log_test_result(test_name, 'failed', f"向量存储测试失败: {str(e)}")

def test_prompts():
    """测试提示词模板"""
    test_name = "提示词模板测试"
    try:
        from app.core.prompts import (
            rephrase_question_prompt,
            rag_answer_prompt,
            agent_prompt,
            SIMPLE_CHAT_PROMPT
        )
        
        prompts_count = 0
        
        # 测试重构问题提示词
        if rephrase_question_prompt:
            prompts_count += 1
            print("   ✓ 重构问题提示词")
        
        # 测试RAG回答提示词
        if rag_answer_prompt:
            prompts_count += 1
            print("   ✓ RAG回答提示词")
        
        # 测试代理提示词
        if agent_prompt:
            prompts_count += 1
            print("   ✓ 代理提示词")
        
        # 测试简单聊天提示词
        if SIMPLE_CHAT_PROMPT:
            prompts_count += 1
            print("   ✓ 简单聊天提示词")
        
        log_test_result(test_name, 'passed', f"成功加载 {prompts_count} 个提示词模板")
        
    except Exception as e:
        log_test_result(test_name, 'failed', f"提示词模板测试失败: {str(e)}")

def test_search_engines():
    """测试搜索引擎"""
    test_name = "搜索引擎测试"
    try:
        from app.core.search_engines import BaseSearchEngine, SimpleSearchEngine
        
        # 测试简单搜索引擎
        simple_engine = SimpleSearchEngine({
            'index_name': 'test_index',
            'data_dir': './data/test_search_index',
            'enable_persistence': False
        })
        
        print(f"   简单搜索引擎类型: {type(simple_engine).__name__}")
        
        # 测试基本功能
        test_index = 'test_medical_index'
        
        # 创建索引
        created = simple_engine.create_index(test_index, {})
        if created:
            print("   ✓ 索引创建成功")
        
        # 检查索引存在
        exists = simple_engine.index_exists(test_index)
        print(f"   索引存在: {exists}")
        
        # 添加测试文档
        test_docs = [
            {
                'id': 'doc1',
                'content': '头痛是一种常见的症状，可能由多种原因引起',
                'title': '头痛的原因',
                'category': '症状'
            },
            {
                'id': 'doc2', 
                'content': '发热通常是机体对感染的一种防御反应',
                'title': '发热机制',
                'category': '症状'
            }
        ]
        
        added = simple_engine.add_documents(test_docs, test_index)
        if added:
            print("   ✓ 文档添加成功")
        
        # 测试搜索
        results = simple_engine.search('头痛', size=5, index_name=test_index)
        print(f"   搜索结果数量: {len(results)}")
        
        # 清理测试索引
        simple_engine.delete_index(test_index)
        
        log_test_result(test_name, 'passed', f"搜索引擎功能正常")
        
    except Exception as e:
        log_test_result(test_name, 'failed', f"搜索引擎测试失败: {str(e)}")

def test_rrf_fusion():
    """测试RRF融合算法"""
    test_name = "RRF融合算法测试"
    try:
        from app.core.fusion.rrf_fusion import RRFFusion, fuse_search_results
        
        # 创建测试文档
        doc1 = Document(page_content="头痛是常见症状", metadata={'source': 'doc1'})
        doc2 = Document(page_content="发热需要及时治疗", metadata={'source': 'doc2'})
        doc3 = Document(page_content="头痛可能由压力引起", metadata={'source': 'doc3'})
        
        # 模拟不同搜索源的结果
        search_results = {
            'vector_search': [doc1, doc3, doc2],
            'keyword_search': [doc2, doc1, doc3]
        }
        
        # 测试RRF融合
        rrf_fusion = RRFFusion(k=60)
        fused_results = rrf_fusion.fuse_results(search_results)
        
        print(f"   输入文档数: {sum(len(docs) for docs in search_results.values())}")
        print(f"   融合后文档数: {len(fused_results)}")
        
        # 检查融合质量
        quality_analysis = rrf_fusion.analyze_fusion_quality(search_results, fused_results)
        print(f"   融合效率: {quality_analysis['fusion_efficiency']:.2f}")
        
        # 测试便捷函数
        fused_results2 = fuse_search_results(search_results, k=60)
        print(f"   便捷函数结果数: {len(fused_results2)}")
        
        log_test_result(test_name, 'passed', f"RRF融合算法正常工作")
        
    except Exception as e:
        log_test_result(test_name, 'failed', f"RRF融合算法测试失败: {str(e)}")

def test_rerankers():
    """测试重排器功能"""
    test_name = "重排器测试"
    try:
        from app.core.rerankers import BaseReranker, get_reranker
        
        # 尝试创建重排器（可能会因为模型依赖而失败）
        try:
            reranker_config = {
                'type': 'bge_reranker',
                'model': {
                    'name': 'BAAI/bge-reranker-base',
                    'device': 'cpu',
                    'max_length': 512
                },
                'parameters': {
                    'top_n': 10,
                    'batch_size': 16
                }
            }
            
            reranker = get_reranker(reranker_config)
            print(f"   重排器类型: {type(reranker).__name__}")
            
            # 创建测试文档
            query = "头痛的原因"
            documents = [
                Document(page_content="头痛可能由压力、疲劳引起", metadata={'id': 'doc1'}),
                Document(page_content="发热是感染的症状", metadata={'id': 'doc2'}),
                Document(page_content="头痛需要找到根本原因进行治疗", metadata={'id': 'doc3'})
            ]
            
            # 测试重排功能
            reranked_docs = reranker.rerank(query, documents)
            print(f"   重排前文档数: {len(documents)}")
            print(f"   重排后文档数: {len(reranked_docs)}")
            
            # 检查分数
            for i, doc in enumerate(reranked_docs[:2]):
                score = doc.metadata.get('rerank_score', 0)
                print(f"   Top{i+1} 分数: {score:.4f}")
            
            log_test_result(test_name, 'passed', f"重排器功能正常")
            
        except ImportError as ie:
            log_test_result(test_name, 'skipped', f"重排器依赖未安装: {str(ie)}")
        except Exception as re:
            log_test_result(test_name, 'skipped', f"重排器模型加载失败: {str(re)}")
            
    except Exception as e:
        log_test_result(test_name, 'failed', f"重排器测试失败: {str(e)}")

def test_ensemble_retriever():
    """测试混合检索器"""
    test_name = "混合检索器测试"
    try:
        from app.core.retrievers import create_ensemble_retriever
        
        # 创建混合检索器配置
        search_config = {
            'type': 'simple',
            'simple': {
                'index_name': 'test_ensemble',
                'persist_directory': './data/test_ensemble_index'
            }
        }
        
        reranker_config = {
            'type': 'bge_reranker',
            'model': {'name': 'BAAI/bge-reranker-base', 'device': 'cpu'},
            'parameters': {'top_n': 10, 'batch_size': 16}
        }
        
        # 创建检索器（可能因为向量存储为空而有限制）
        try:
            retriever = create_ensemble_retriever(
                vector_weight=0.6,
                keyword_weight=0.4,
                rrf_k=60,
                top_k=5,
                search_engine_config=search_config,
                reranker_config=reranker_config,
                enable_reranking=False  # 暂时禁用重排器避免模型依赖
            )
            
            print(f"   混合检索器类型: {type(retriever).__name__}")
            print(f"   向量权重: 0.6, 关键词权重: 0.4")
            print(f"   RRF参数k: 60")
            
            # 如果向量存储有数据，尝试检索
            try:
                test_query = "头痛的症状和治疗方法"
                results = retriever.get_relevant_documents(test_query)
                print(f"   检索结果数量: {len(results)}")
                
                if results:
                    for i, doc in enumerate(results[:2]):
                        rrf_score = doc.metadata.get('rrf_score', 0)
                        print(f"   结果{i+1} RRF分数: {rrf_score:.4f}")
                        
            except Exception as retrieval_error:
                print(f"   检索测试跳过: {str(retrieval_error)}")
            
            log_test_result(test_name, 'passed', f"混合检索器创建成功")
            
        except Exception as creation_error:
            log_test_result(test_name, 'skipped', f"检索器创建失败: {str(creation_error)}")
            
    except Exception as e:
        log_test_result(test_name, 'failed', f"混合检索器测试失败: {str(e)}")

def test_rag_chains():
    """测试RAG链"""
    test_name = "RAG链测试"
    try:
        from app.core.chains import get_rag_chain, get_hybrid_rag_chain, get_vector_rag_chain
        
        print("   测试RAG链创建...")
        
        # 测试默认RAG链
        try:
            default_chain = get_rag_chain()
            print(f"   ✓ 默认RAG链: {type(default_chain).__name__}")
        except Exception as e:
            print(f"   ✗ 默认RAG链创建失败: {str(e)}")
        
        # 测试向量RAG链
        try:
            vector_chain = get_vector_rag_chain()
            print(f"   ✓ 向量RAG链: {type(vector_chain).__name__}")
        except Exception as e:
            print(f"   ✗ 向量RAG链创建失败: {str(e)}")
        
        # 测试混合RAG链
        try:
            hybrid_config = {
                'vector_weight': 0.7,
                'keyword_weight': 0.3,
                'rrf_k': 50,
                'enable_reranking': False
            }
            hybrid_chain = get_hybrid_rag_chain(hybrid_config)
            print(f"   ✓ 混合RAG链: {type(hybrid_chain).__name__}")
        except Exception as e:
            print(f"   ✗ 混合RAG链创建失败: {str(e)}")
        
        log_test_result(test_name, 'passed', f"RAG链测试完成")
        
    except Exception as e:
        log_test_result(test_name, 'failed', f"RAG链测试失败: {str(e)}")

def test_config_loading():
    """测试配置加载"""
    test_name = "配置加载测试"
    try:
        from app.configs.settings import config
        
        print("   检查配置项...")
        
        # 检查混合搜索配置
        if hasattr(config, 'hybrid_search'):
            print(f"   ✓ 混合搜索启用: {config.hybrid_search.enabled}")
            print(f"   ✓ 向量权重: {config.hybrid_search.weights.vector}")
            print(f"   ✓ 关键词权重: {config.hybrid_search.weights.keyword}")
            print(f"   ✓ RRF参数k: {config.hybrid_search.rrf.k}")
        
        # 检查重排器配置
        if hasattr(config, 'retrieval') and hasattr(config.retrieval, 'reranker'):
            print(f"   ✓ 重排器启用: {config.retrieval.reranker.enabled}")
            print(f"   ✓ 重排器类型: {config.retrieval.reranker.type}")
            print(f"   ✓ 重排器模型: {config.retrieval.reranker.model.name}")
        
        # 检查向量存储配置
        if hasattr(config, 'vector_store'):
            print(f"   ✓ 向量存储目录: {config.vector_store.persist_directory}")
            print(f"   ✓ 集合名称: {config.vector_store.collection_name}")
        
        # 检查嵌入模型配置
        if hasattr(config, 'embedding'):
            print(f"   ✓ 嵌入提供者: {config.embedding.provider}")
            print(f"   ✓ 嵌入模型: {config.embedding.name}")
        
        log_test_result(test_name, 'passed', f"配置加载正常")
        
    except Exception as e:
        log_test_result(test_name, 'failed', f"配置加载测试失败: {str(e)}")

def run_performance_test():
    """运行性能测试"""
    test_name = "性能测试"
    try:
        print("   运行性能基准测试...")
        
        # 测试嵌入模型性能
        start_time = time.time()
        from app.core.vector_store import get_embedding_model
        embeddings = get_embedding_model()
        
        # 测试文本嵌入
        test_text = "头痛是一种常见的医学症状，可能由多种原因引起，包括压力、疲劳、脱水等。"
        embed_result = embeddings.embed_query(test_text)
        embed_time = time.time() - start_time
        
        print(f"   嵌入模型加载+推理时间: {embed_time:.3f}秒")
        print(f"   嵌入向量维度: {len(embed_result)}")
        
        # 测试RRF融合性能
        start_time = time.time()
        from app.core.fusion.rrf_fusion import RRFFusion
        
        # 创建大量测试文档
        docs = [Document(page_content=f"测试文档{i}", metadata={'id': f'doc{i}'}) 
                for i in range(100)]
        
        search_results = {
            'vector': docs[:50],
            'keyword': docs[25:75]
        }
        
        rrf = RRFFusion(k=60)
        fused_results = rrf.fuse_results(search_results)
        fusion_time = time.time() - start_time
        
        print(f"   RRF融合100个文档时间: {fusion_time:.3f}秒")
        print(f"   融合后文档数量: {len(fused_results)}")
        
        log_test_result(test_name, 'passed', f"性能测试完成")
        
    except Exception as e:
        log_test_result(test_name, 'failed', f"性能测试失败: {str(e)}")

def print_test_summary():
    """打印测试总结"""
    print("\n" + "="*60)
    print("📊 测试结果总结")
    print("="*60)
    
    total = test_results['total']
    passed = test_results['passed']
    failed = test_results['failed']
    skipped = test_results['skipped']
    
    print(f"总测试数: {total}")
    print(f"✅ 通过: {passed} ({passed/total*100:.1f}%)" if total > 0 else "✅ 通过: 0")
    print(f"❌ 失败: {failed} ({failed/total*100:.1f}%)" if total > 0 else "❌ 失败: 0")
    print(f"⏭️ 跳过: {skipped} ({skipped/total*100:.1f}%)" if total > 0 else "⏭️ 跳过: 0")
    
    if failed > 0:
        print("\n❌ 失败的测试:")
        for detail in test_results['details']:
            if detail['status'] == 'failed':
                print(f"   - {detail['test']}: {detail['details']}")
    
    if skipped > 0:
        print("\n⏭️ 跳过的测试:")
        for detail in test_results['details']:
            if detail['status'] == 'skipped':
                print(f"   - {detail['test']}: {detail['details']}")
    
    print("\n" + "="*60)
    
    # 返回测试是否全部通过
    return failed == 0

def main():
    """主测试函数"""
    print("🚀 开始测试 app/core 核心模块")
    print("="*60)
    
    # 按顺序运行测试
    test_functions = [
        test_config_loading,      # 配置加载测试
        test_vector_store,        # 向量存储测试
        test_prompts,            # 提示词测试
        test_search_engines,     # 搜索引擎测试
        test_rrf_fusion,         # RRF融合测试
        test_rerankers,          # 重排器测试
        test_ensemble_retriever, # 混合检索器测试
        test_rag_chains,         # RAG链测试
        run_performance_test,    # 性能测试
    ]
    
    for test_func in test_functions:
        try:
            print(f"\n🔍 运行 {test_func.__name__}...")
            test_func()
        except KeyboardInterrupt:
            print("\n⏹️ 测试被用户中断")
            break
        except Exception as e:
            print(f"❌ 测试执行异常: {str(e)}")
            traceback.print_exc()
    
    # 打印测试总结
    success = print_test_summary()
    
    # 退出码
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main() 