"""RRF（倒数排序融合）算法实现

实现Reciprocal Rank Fusion算法，用于合并来自不同搜索引擎的结果。
这是SRH-001混合搜索核心功能的关键组件。
"""

from typing import List, Dict, Any, Tuple, Optional, Union
import math
from dataclasses import dataclass
from langchain_core.documents import Document


@dataclass
class SearchResult:
    """搜索结果数据类"""
    document: Document
    score: float
    rank: int
    source: str  # 搜索来源标识


class RRFFusion:
    """RRF融合算法实现类"""
    
    def __init__(self, k: int = 60):
        """初始化RRF融合器
        
        Args:
            k: RRF算法的k参数，用于平滑排名影响。
               较大的k值会减少高排名文档的优势，较小的k值会增强高排名文档的优势。
               经验值通常在60左右。
        """
        self.k = k
    
    def fuse_results(
        self,
        search_results: Dict[str, List[Document]],
        weights: Optional[Dict[str, float]] = None
    ) -> List[Document]:
        """融合多个搜索结果
        
        Args:
            search_results: 搜索结果字典，键为搜索源名称，值为文档列表
            weights: 各搜索源的权重，如果为None则使用均等权重
        
        Returns:
            融合后的文档列表，按RRF分数降序排列
        """
        if not search_results:
            return []
        
        # 设置默认权重
        if weights is None:
            weights = {source: 1.0 for source in search_results.keys()}
        
        # 标准化权重
        total_weight = sum(weights.values())
        if total_weight > 0:
            weights = {k: v / total_weight for k, v in weights.items()}
        
        print(f"🔄 开始RRF融合，k={self.k}")
        for source, docs in search_results.items():
            weight = weights.get(source, 0)
            print(f"   - {source}: {len(docs)} 个文档, 权重: {weight:.3f}")
        
        # 计算RRF分数
        doc_scores = self._calculate_rrf_scores(search_results, weights)
        
        # 按分数排序
        sorted_results = sorted(doc_scores.items(), key=lambda x: x[1], reverse=True)
        
        # 构建最终结果
        fused_documents = []
        for doc_key, rrf_score in sorted_results:
            doc = self._get_document_by_key(doc_key, search_results)
            if doc:
                # 在元数据中添加融合信息
                doc.metadata['rrf_score'] = rrf_score
                doc.metadata['fusion_sources'] = self._get_document_sources(doc_key, search_results)
                fused_documents.append(doc)
        
        print(f"✅ RRF融合完成，返回 {len(fused_documents)} 个文档")
        return fused_documents
    
    def _calculate_rrf_scores(
        self,
        search_results: Dict[str, List[Document]],
        weights: Dict[str, float]
    ) -> Dict[str, float]:
        """计算RRF分数
        
        RRF公式: score = Σ(weight / (k + rank))
        其中rank是文档在各个搜索结果中的排名（从1开始）
        """
        doc_scores = {}
        
        for source, documents in search_results.items():
            weight = weights.get(source, 0)
            
            for rank, doc in enumerate(documents, 1):
                doc_key = self._get_document_key(doc)
                
                # 计算RRF分数
                rrf_contribution = weight / (self.k + rank)
                doc_scores[doc_key] = doc_scores.get(doc_key, 0) + rrf_contribution
        
        return doc_scores
    
    def _get_document_key(self, doc: Document) -> str:
        """生成文档的唯一标识符"""
        metadata = doc.metadata
        
        # 优先使用chunk_id
        if 'chunk_id' in metadata:
            return f"chunk_{metadata['chunk_id']}"
        
        # 其次使用_id
        if '_id' in metadata:
            return f"id_{metadata['_id']}"
        
        # 使用source和部分内容的组合
        source = metadata.get('source', 'unknown')
        content_preview = doc.page_content[:100].replace('\n', ' ')
        content_hash = hash(content_preview)
        return f"{source}_{content_hash}"
    
    def _get_document_by_key(
        self,
        doc_key: str,
        search_results: Dict[str, List[Document]]
    ) -> Optional[Document]:
        """根据文档键获取文档对象"""
        for documents in search_results.values():
            for doc in documents:
                if self._get_document_key(doc) == doc_key:
                    return doc
        return None
    
    def _get_document_sources(
        self,
        doc_key: str,
        search_results: Dict[str, List[Document]]
    ) -> List[str]:
        """获取文档出现的所有搜索源"""
        sources = []
        for source, documents in search_results.items():
            for doc in documents:
                if self._get_document_key(doc) == doc_key:
                    sources.append(source)
                    break
        return sources
    
    def analyze_fusion_quality(
        self,
        search_results: Dict[str, List[Document]],
        fused_results: List[Document]
    ) -> Dict[str, Any]:
        """分析融合质量
        
        Args:
            search_results: 原始搜索结果
            fused_results: 融合后的结果
        
        Returns:
            融合质量分析报告
        """
        total_unique_docs = len(set(
            self._get_document_key(doc)
            for docs in search_results.values()
            for doc in docs
        ))
        
        # 计算各源的贡献度
        source_contributions = {}
        for doc in fused_results:
            sources = doc.metadata.get('fusion_sources', [])
            for source in sources:
                source_contributions[source] = source_contributions.get(source, 0) + 1
        
        # 计算重叠度
        overlaps = {}
        sources = list(search_results.keys())
        for i, source1 in enumerate(sources):
            for source2 in sources[i+1:]:
                docs1_keys = {self._get_document_key(doc) for doc in search_results[source1]}
                docs2_keys = {self._get_document_key(doc) for doc in search_results[source2]}
                overlap = len(docs1_keys & docs2_keys)
                overlaps[f"{source1}_vs_{source2}"] = {
                    'overlap_count': overlap,
                    'overlap_ratio': overlap / max(len(docs1_keys), len(docs2_keys)) if max(len(docs1_keys), len(docs2_keys)) > 0 else 0
                }
        
        return {
            'total_input_documents': sum(len(docs) for docs in search_results.values()),
            'unique_input_documents': total_unique_docs,
            'fused_documents_count': len(fused_results),
            'source_contributions': source_contributions,
            'source_overlaps': overlaps,
            'fusion_efficiency': len(fused_results) / total_unique_docs if total_unique_docs > 0 else 0
        }


def create_rrf_fusion(k: int = 60) -> RRFFusion:
    """创建RRF融合器的工厂函数
    
    Args:
        k: RRF算法的k参数
    
    Returns:
        配置好的RRFFusion实例
    """
    return RRFFusion(k=k)


# 便捷函数
def fuse_search_results(
    search_results: Dict[str, List[Document]],
    weights: Optional[Dict[str, float]] = None,
    k: int = 60
) -> List[Document]:
    """便捷的RRF融合函数
    
    Args:
        search_results: 搜索结果字典
        weights: 各搜索源的权重
        k: RRF算法的k参数
    
    Returns:
        融合后的文档列表
    """
    fusion = RRFFusion(k=k)
    return fusion.fuse_results(search_results, weights)
