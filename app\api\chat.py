from flask import Blueprint, request, jsonify, current_app, Response
import time
import logging
import random
import uuid
import json
from ..services.llm_service import LLMService
from ..models.database import db
from ..models.chat_session import ChatSession, ChatMessage
from ..models.user import User
from ..models.token import Token

# Setup logger
logger = logging.getLogger(__name__)

# Initialize LLM service
def get_llm_service():
    """Get or create LLM service instance"""
    return LLMService()

chat_bp = Blueprint('chat', __name__)

# 医疗关键词列表，用于识别可能需要医生介入的会话
MEDICAL_KEYWORDS = [
    '头痛', '头疼', '偏头痛', '脑袋痛', '血压', '胸痛', '心脏', '呼吸困难', 
    '呕吐', '恶心', '发烧', '高热', '腹痛', '腹泻', '关节痛', '骨折',
    '癌症', '肿瘤', '糖尿病', '中风', '抑郁', '焦虑', '自杀', '药物过敏'
]

@chat_bp.route('/api/chat', methods=['POST'])
def chat():
    """
    Chat API endpoint for handling messages with the LLM
    
    Request body:
    {
        "message": "User's message here",
        "session_id": "optional_session_id",
        "username": "optional_username"
    }
    
    Response:
    {
        "reply": "Bot's response here",
        "status": "success",
        "session_id": "session_id"
    }
    """
    try:
        data = request.json
        user_message = data.get('message', '')
        session_id = data.get('session_id')
        username = data.get('username', '患者')
        
        if not user_message:
            return jsonify({
                'reply': '请输入您的问题',
                'status': 'error',
                'error': 'Empty message'
            }), 400
        
        # Log incoming message
        logger.info(f"Received message from session {session_id}: {user_message[:50]}...")
        
        # Find the user by username
        user = User.query.filter_by(username=username).first()
        user_id = user.id if user else None

        # Try to get user from Authorization header if no username provided or user not found
        if not user_id:
            auth_header = request.headers.get('Authorization')
            if auth_header and auth_header.startswith('Bearer '):
                token_str = auth_header.split(' ')[1]
                user_id = Token.get_user_id_by_token(token_str)

        # Check if we have a valid user_id
        if not user_id:
            return jsonify({
                'reply': '请先登录后再使用聊天功能',
                'status': 'error',
                'error': 'User authentication required'
            }), 401

        # Create a new session if needed
        session = None
        if not session_id:
            session_id = str(uuid.uuid4())
            session = ChatSession(
                session_id=session_id,
                user_id=user_id,
                title=user_message[:50]  # Use first message as title
            )
            db.session.add(session)
            db.session.commit()
        else:
            session = ChatSession.query.get(session_id)
            if not session:
                # Session ID provided but not found, create a new one
                session = ChatSession(
                    session_id=session_id,
                    user_id=user_id,
                    title=user_message[:50]
                )
                db.session.add(session)
                db.session.commit()
            else:
                # Verify that the session belongs to the current user
                if session.user_id != user_id:
                    return jsonify({
                        'reply': '无权访问此会话',
                        'status': 'error',
                        'error': 'Session access denied'
                    }), 403
                # Update session activity
                session.update_activity()
                db.session.commit()
        
        # Check if message contains medical keywords to set priority
        for keyword in MEDICAL_KEYWORDS:
            if keyword in user_message:
                session.priority = 'high'
                db.session.commit()
                break
        
        # Add user message to database
        user_msg = ChatMessage(
            session_id=session_id,
            role='user',
            content=user_message
        )
        db.session.add(user_msg)
        db.session.commit()
        
        # Initialize LLM service if needed
        global llm_service
        if llm_service is None:
            llm_service = LLMService()
            
        # Get conversation history from database
        history_query = ChatMessage.query.filter_by(session_id=session_id).order_by(ChatMessage.timestamp).all()
        history = [msg.to_dict() for msg in history_query]
        
        # Generate response using LLM service
        try:
            llm_service = get_llm_service()
            response = llm_service.generate_response(
                user_message,
                history=history[:-1]  # Exclude the message we just added
            )
        except Exception as e:
            logger.error(f"Error generating response: {str(e)}")
            response = "抱歉，我暂时无法回答您的问题，请稍后再试。"
        
        # Add bot response to database
        bot_msg = ChatMessage(
            session_id=session_id,
            role='bot',
            content=response
        )
        db.session.add(bot_msg)
        
        # Update session's last message
        session.title = user_message[:50]  # Update title with the latest message
        db.session.commit()
        
        return jsonify({
            'reply': response,
            'status': 'success',
            'session_id': session_id
        })
        
    except Exception as e:
        logger.error(f"Error in chat endpoint: {str(e)}")
        return jsonify({
            'reply': '处理您的请求时出现错误，请稍后再试',
            'status': 'error',
            'error': str(e)
        }), 500

@chat_bp.route('/api/chat/stream', methods=['POST'])
def chat_stream():
    """
    流式聊天API端点

    Request body:
    {
        "message": "User's message here",
        "session_id": "optional_session_id",
        "username": "optional_username"
    }

    Response: Server-Sent Events (SSE) stream
    """
    try:
        data = request.json
        user_message = data.get('message', '')
        session_id = data.get('session_id')
        username = data.get('username', '患者')

        if not user_message:
            return jsonify({
                'reply': '请输入您的问题',
                'status': 'error',
                'error': 'Empty message'
            }), 400

        # Find the user by username
        user = User.query.filter_by(username=username).first()
        user_id = user.id if user else None

        # Try to get user from Authorization header if no username provided or user not found
        if not user_id:
            auth_header = request.headers.get('Authorization')
            if auth_header and auth_header.startswith('Bearer '):
                token_str = auth_header.split(' ')[1]
                user_id = Token.get_user_id_by_token(token_str)

        # Check if we have a valid user_id
        if not user_id:
            return jsonify({
                'reply': '请先登录后再使用聊天功能',
                'status': 'error',
                'error': 'User authentication required'
            }), 401

        # Generate session ID if not provided
        if not session_id:
            session_id = str(uuid.uuid4())

        # Get or create session
        session = ChatSession.query.get(session_id)
        if not session:
            session = ChatSession(session_id=session_id, user_id=user_id, title=user_message[:50])
            db.session.add(session)
        else:
            # Verify that the session belongs to the current user
            if session.user_id != user_id:
                return jsonify({
                    'reply': '无权访问此会话',
                    'status': 'error',
                    'error': 'Session access denied'
                }), 403

        # Update session activity
        session.update_activity()

        # Add user message to database
        user_msg = ChatMessage(
            session_id=session_id,
            role='user',
            content=user_message
        )
        db.session.add(user_msg)
        db.session.commit()

        # Get conversation history from database
        history_query = ChatMessage.query.filter_by(session_id=session_id).order_by(ChatMessage.timestamp).all()
        history = [msg.to_dict() for msg in history_query]

        # 在生成器外部获取应用实例
        app = current_app._get_current_object()

        def generate_stream():
            """生成流式响应"""

            try:
                # 发送开始事件
                yield f"data: {json.dumps({'type': 'start', 'session_id': session_id})}\n\n"

                # 获取LLM服务实例
                llm_service = get_llm_service()

                # 生成流式回复
                full_response = ""
                for chunk in llm_service.generate_response_stream(
                    user_message,
                    history=history[:-1]  # Exclude the message we just added
                ):
                    full_response += chunk
                    yield f"data: {json.dumps({'type': 'chunk', 'content': chunk})}\n\n"

                # 在应用上下文中保存完整回复到数据库
                with app.app_context():
                    # 重新获取session对象，因为在新的上下文中
                    session_obj = ChatSession.query.get(session_id)
                    if session_obj:
                        bot_msg = ChatMessage(
                            session_id=session_id,
                            role='bot',
                            content=full_response
                        )
                        db.session.add(bot_msg)
                        session_obj.update_activity()
                        db.session.commit()

                # 发送结束事件
                yield f"data: {json.dumps({'type': 'end', 'full_response': full_response})}\n\n"

            except Exception as e:
                logger.error(f"Error in stream generation: {str(e)}")
                error_response = "抱歉，我暂时无法回答您的问题，请稍后再试。"

                # 在应用上下文中保存错误回复到数据库
                try:
                    with app.app_context():
                        # 重新获取session对象
                        session_obj = ChatSession.query.get(session_id)
                        if session_obj:
                            bot_msg = ChatMessage(
                                session_id=session_id,
                                role='bot',
                                content=error_response
                            )
                            db.session.add(bot_msg)
                            session_obj.update_activity()
                            db.session.commit()
                except Exception as db_error:
                    logger.error(f"Error saving error message to database: {str(db_error)}")

                yield f"data: {json.dumps({'type': 'error', 'content': error_response})}\n\n"

        return Response(
            generate_stream(),
            mimetype='text/event-stream',
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type'
            }
        )

    except Exception as e:
        logger.error(f"Error in chat_stream endpoint: {str(e)}")
        return jsonify({
            'reply': '抱歉，我暂时无法回答您的问题，请稍后再试。',
            'status': 'error',
            'error': str(e)
        }), 500

@chat_bp.route('/api/chat/history', methods=['GET'])
def get_chat_history():
    """
    Get chat history for a specific session
    
    Query parameters:
    - session_id: The session ID to retrieve history for
    
    Response:
    {
        "history": [
            {"role": "user", "content": "...", "timestamp": 1234567890},
            {"role": "bot", "content": "...", "timestamp": 1234567890},
            {"role": "doctor", "content": "...", "timestamp": 1234567890}
        ],
        "status": "success"
    }
    """
    try:
        session_id = request.args.get('session_id')
        
        if not session_id:
            return jsonify({
                'status': 'error',
                'error': 'Missing session_id parameter'
            }), 400
            
        # Get current user
        user_id = None
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            token_str = auth_header.split(' ')[1]
            user_id = Token.get_user_id_by_token(token_str)

        if not user_id:
            return jsonify({
                'status': 'error',
                'error': 'User authentication required'
            }), 401

        # Check if session exists
        session = ChatSession.query.get(session_id)
        if not session:
            return jsonify({
                'status': 'error',
                'error': 'Session not found'
            }), 404

        # Verify that the session belongs to the current user
        if session.user_id != user_id:
            return jsonify({
                'status': 'error',
                'error': 'Session access denied'
            }), 403

        # Get messages for this session
        messages = ChatMessage.query.filter_by(session_id=session_id).order_by(ChatMessage.timestamp).all()
        history = [msg.to_dict() for msg in messages]

        return jsonify({
            'history': history,
            'status': 'success'
        })
        
    except Exception as e:
        logger.error(f"Error in get_chat_history endpoint: {str(e)}")
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500
        
@chat_bp.route('/api/chat/session', methods=['DELETE'])
def clear_chat_session():
    """
    Clear a chat session
    
    Query parameters:
    - session_id: The session ID to clear
    
    Response:
    {
        "status": "success"
    }
    """
    try:
        session_id = request.args.get('session_id')
        
        if not session_id:
            return jsonify({
                'status': 'error',
                'error': 'Missing session_id parameter'
            }), 400
            
        # Find and delete the session (cascade will delete all messages)
        session = ChatSession.query.get(session_id)
        if session:
            db.session.delete(session)
            db.session.commit()
            
        return jsonify({
            'status': 'success'
        })
        
    except Exception as e:
        logger.error(f"Error in clear_chat_session endpoint: {str(e)}")
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@chat_bp.route('/api/chat/doctor', methods=['POST'])
def send_doctor_message():
    """
    医生向特定会话发送消息
    
    Request body:
    {
        "session_id": "session_id",
        "message": "Doctor's message here",
        "doctor_name": "医生姓名"
    }
    
    Response:
    {
        "status": "success"
    }
    """
    try:
        data = request.json
        session_id = data.get('session_id')
        message = data.get('message')
        doctor_name = data.get('doctor_name', '医生')
        
        if not session_id or not message:
            return jsonify({
                'status': 'error',
                'error': 'Missing required parameters'
            }), 400
            
        # Check if session exists
        session = ChatSession.query.get(session_id)
        if not session:
            return jsonify({
                'status': 'error',
                'error': 'Session not found'
            }), 404
        
        # Add doctor message to database
        doctor_msg = ChatMessage(
            session_id=session_id,
            role='doctor',
            content=message,
            doctor_name=doctor_name
        )
        db.session.add(doctor_msg)
        
        # Mark that doctor is involved
        session.doctor_involved = True
        session.update_activity()
        db.session.commit()
        
        return jsonify({
            'status': 'success'
        })
        
    except Exception as e:
        logger.error(f"Error in send_doctor_message endpoint: {str(e)}")
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@chat_bp.route('/api/chat/active_sessions', methods=['GET'])
def get_active_sessions():
    """
    获取所有活跃的聊天会话，供医生工作台和用户历史记录使用
    
    Response:
    {
        "sessions": [
            {
                "session_id": "session_id",
                "username": "患者姓名",
                "last_message": "最后一条消息",
                "last_activity": 1234567890,
                "doctor_involved": false,
                "priority": "normal"
            }
        ],
        "status": "success"
    }
    """
    try:
        # Get username from query parameter if provided (for filtering by user)
        username = request.args.get('username')
        user_id = None

        if username:
            user = User.query.filter_by(username=username).first()
            if user:
                user_id = user.id

        # Try to get user from Authorization header if no username provided or user not found
        if not user_id:
            auth_header = request.headers.get('Authorization')
            if auth_header and auth_header.startswith('Bearer '):
                token_str = auth_header.split(' ')[1]
                user_id = Token.get_user_id_by_token(token_str)
        
        # Query sessions, ordered by last activity
        query = ChatSession.query
        
        # Filter by user if username was provided
        if user_id:
            query = query.filter_by(user_id=user_id)
            
        # Only get active sessions
        query = query.filter_by(status='active')
        
        # Order by last activity, newest first
        sessions = query.order_by(ChatSession.last_activity.desc()).all()
        
        result = []
        for session in sessions:
            # Get the last message for each session
            last_message = ChatMessage.query.filter_by(session_id=session.id).order_by(ChatMessage.timestamp.desc()).first()
            
            # Get user's username
            username = '患者'
            if session.user_id:
                user = User.query.get(session.user_id)
                if user:
                    username = user.username
            
            # Build session summary
            session_summary = {
                'session_id': session.id,
                'username': username,
                'last_message': last_message.content if last_message else '',
                'last_activity': session.last_activity.isoformat() if session.last_activity else None,
                'doctor_involved': session.doctor_involved,
                'priority': session.priority
            }
            
            result.append(session_summary)
            
        return jsonify({
            'sessions': result,
            'status': 'success'
        })

    except Exception as e:
        logger.error(f"Error in get_active_sessions endpoint: {str(e)}")
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@chat_bp.route('/api/chat/delete_session', methods=['DELETE'])
def delete_session():
    """
    删除指定的聊天会话及其所有消息

    Request:
    {
        "session_id": "session_id_to_delete"
    }

    Response:
    {
        "status": "success" | "error",
        "message": "删除成功" | "错误信息"
    }
    """
    try:
        data = request.get_json()
        session_id = data.get('session_id')

        if not session_id:
            return jsonify({
                'status': 'error',
                'message': '缺少session_id参数'
            }), 400

        # 查找会话
        session = ChatSession.query.get(session_id)
        if not session:
            return jsonify({
                'status': 'error',
                'message': '会话不存在'
            }), 404

        # 删除会话相关的所有消息（由于外键约束，会自动删除）
        # 但为了确保，我们先手动删除消息
        ChatMessage.query.filter_by(session_id=session_id).delete()

        # 删除会话
        db.session.delete(session)
        db.session.commit()

        logger.info(f"Successfully deleted session: {session_id}")

        return jsonify({
            'status': 'success',
            'message': '会话删除成功'
        })

    except Exception as e:
        logger.error(f"Error deleting session: {str(e)}")
        db.session.rollback()
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500