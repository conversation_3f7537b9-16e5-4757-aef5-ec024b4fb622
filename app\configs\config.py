import os
from dotenv import load_dotenv

# Load environment variables from .env file if present
load_dotenv()

class Config:
    """Base configuration."""
    # Flask configuration
    SECRET_KEY = os.getenv('SECRET_KEY', 'dev-key-please-change-in-production')
    DEBUG = False
    TESTING = False
    
    # Application configuration
    APP_NAME = "慧问医答——智能导诊解答机器人"
    
    # API configuration
    API_PREFIX = '/api'
    
    # LLM configuration
    LLM_API_KEY = os.getenv('LLM_API_KEY', '')
    LLM_API_URL = os.getenv('LLM_API_URL', '')
    LLM_MODEL_NAME = os.getenv('LLM_MODEL_NAME', 'gpt-3.5-turbo')
    
    # Chat configuration
    MAX_HISTORY_LENGTH = int(os.getenv('MAX_HISTORY_LENGTH', '10'))
    RESPONSE_TEMPERATURE = float(os.getenv('RESPONSE_TEMPERATURE', '0.7'))
    MAX_TOKENS = int(os.getenv('MAX_TOKENS', '1024'))
    
    # Database configuration
    DB_USER = os.getenv('DB_USER', 'root123')
    DB_PASSWORD = os.getenv('DB_PASSWORD', '123456')
    DB_HOST = os.getenv('DB_HOST', 'localhost')
    DB_PORT = os.getenv('DB_PORT', '3306')
    DB_NAME = os.getenv('DB_NAME', 'medical_chatbot')
    SQLALCHEMY_DATABASE_URI = f'mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}'
    SQLALCHEMY_TRACK_MODIFICATIONS = False


class DevelopmentConfig(Config):
    """Development configuration."""
    DEBUG = True
    # Use MySQL for development as well
    # SQLALCHEMY_DATABASE_URI = 'sqlite:///medical_chatbot.db'
    

class ProductionConfig(Config):
    """Production configuration."""
    # Production environment should have a proper secret key
    SECRET_KEY = os.getenv('SECRET_KEY')
    

class TestingConfig(Config):
    """Testing configuration."""
    TESTING = True
    # Use an in-memory SQLite database for testing
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    

# Configuration dictionary
config_by_name = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig
}

# Get the active configuration
def get_config():
    env = os.getenv('FLASK_ENV', 'development')
    return config_by_name.get(env, DevelopmentConfig) 