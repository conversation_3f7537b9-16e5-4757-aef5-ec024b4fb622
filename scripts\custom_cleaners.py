"""自定义数据清洗函数

提供一些常用的自定义清洗函数，可以在cleaning_steps中通过function_path引用
"""

import re
import pandas as pd
from typing import Any, Dict


def remove_medical_codes(text: str) -> str:
    """移除医学编码
    
    移除常见的医学编码格式，如ICD-10编码等
    
    Args:
        text: 输入文本
        
    Returns:
        清洗后的文本
    """
    if not isinstance(text, str):
        return str(text)
    
    # 移除ICD-10编码 (如: A00.1, B15.9)
    text = re.sub(r'\b[A-Z]\d{2}\.\d+\b', '', text)
    
    # 移除其他常见医学编码格式
    text = re.sub(r'\b\d{3}\.\d{2}\b', '', text)  # 如: 123.45
    text = re.sub(r'\bCPT:\s*\d+\b', '', text, flags=re.IGNORECASE)  # CPT编码
    
    # 清理多余空格
    text = re.sub(r'\s+', ' ', text).strip()
    
    return text


def normalize_medical_terms(text: str) -> str:
    """标准化医学术语
    
    将常见的医学术语变体统一为标准形式
    
    Args:
        text: 输入文本
        
    Returns:
        标准化后的文本
    """
    if not isinstance(text, str):
        return str(text)
    
    # 医学术语标准化映射
    term_mapping = {
        r'\b高血压病\b': '高血压',
        r'\b糖尿病病\b': '糖尿病',
        r'\b心脏病病\b': '心脏病',
        r'\b冠心病病\b': '冠心病',
        r'\b脑梗死\b': '脑梗塞',
        r'\b脑梗塞\b': '脑梗塞',
        r'\b心肌梗死\b': '心肌梗塞',
        r'\b心肌梗塞\b': '心肌梗塞'
    }
    
    for pattern, replacement in term_mapping.items():
        text = re.sub(pattern, replacement, text, flags=re.IGNORECASE)
    
    return text


def remove_page_headers_footers(text: str) -> str:
    """移除页眉页脚信息
    
    移除常见的页眉页脚格式
    
    Args:
        text: 输入文本
        
    Returns:
        清洗后的文本
    """
    if not isinstance(text, str):
        return str(text)
    
    # 移除页码信息
    text = re.sub(r'页码[：:]\s*\d+', '', text)
    text = re.sub(r'第\s*\d+\s*页', '', text)
    text = re.sub(r'- \d+ -', '', text)
    
    # 移除常见页眉页脚
    text = re.sub(r'医院内部资料', '', text, flags=re.IGNORECASE)
    text = re.sub(r'仅供内部使用', '', text, flags=re.IGNORECASE)
    text = re.sub(r'版权所有', '', text, flags=re.IGNORECASE)
    text = re.sub(r'保密文件', '', text, flags=re.IGNORECASE)
    
    # 移除日期时间戳
    text = re.sub(r'\d{4}[-/]\d{1,2}[-/]\d{1,2}', '', text)
    text = re.sub(r'\d{1,2}:\d{2}(:\d{2})?', '', text)
    
    # 清理多余空格和换行
    text = re.sub(r'\s+', ' ', text).strip()
    
    return text


def extract_key_medical_info(text: str) -> str:
    """提取关键医学信息
    
    保留重要的医学信息，移除无关内容
    
    Args:
        text: 输入文本
        
    Returns:
        提取后的文本
    """
    if not isinstance(text, str):
        return str(text)
    
    # 保留的关键医学信息模式
    important_patterns = [
        r'症状[：:].*?[。\n]',
        r'诊断[：:].*?[。\n]',
        r'治疗[：:].*?[。\n]',
        r'用药[：:].*?[。\n]',
        r'建议[：:].*?[。\n]',
        r'注意事项[：:].*?[。\n]',
        r'病史[：:].*?[。\n]',
        r'检查结果[：:].*?[。\n]'
    ]
    
    extracted_parts = []
    for pattern in important_patterns:
        matches = re.findall(pattern, text, flags=re.IGNORECASE | re.DOTALL)
        extracted_parts.extend(matches)
    
    if extracted_parts:
        return ' '.join(extracted_parts).strip()
    else:
        # 如果没有找到特定模式，返回原文本
        return text


def clean_medical_dataframe(df: pd.DataFrame, **kwargs) -> pd.DataFrame:
    """清洗医学数据DataFrame
    
    对整个DataFrame应用医学数据特定的清洗逻辑
    
    Args:
        df: 输入DataFrame
        **kwargs: 额外参数
        
    Returns:
        清洗后的DataFrame
    """
    df_copy = df.copy()
    
    # 如果有content列，应用文本清洗
    if 'content' in df_copy.columns:
        df_copy['content'] = df_copy['content'].apply(remove_medical_codes)
        df_copy['content'] = df_copy['content'].apply(normalize_medical_terms)
        df_copy['content'] = df_copy['content'].apply(remove_page_headers_footers)
    
    # 移除过短的内容
    min_length = kwargs.get('min_length', 20)
    if 'content' in df_copy.columns:
        df_copy = df_copy[df_copy['content'].str.len() >= min_length]
    
    # 移除包含特定无用词汇的记录
    exclude_keywords = kwargs.get('exclude_keywords', ['测试', '示例', 'test', 'example'])
    if 'content' in df_copy.columns and exclude_keywords:
        for keyword in exclude_keywords:
            df_copy = df_copy[~df_copy['content'].str.contains(keyword, case=False, na=False)]
    
    return df_copy


def standardize_disease_names(text: str) -> str:
    """标准化疾病名称
    
    将疾病名称的各种写法统一为标准形式
    
    Args:
        text: 输入文本
        
    Returns:
        标准化后的文本
    """
    if not isinstance(text, str):
        return str(text)
    
    # 疾病名称标准化
    disease_mapping = {
        r'\b感冒\b': '普通感冒',
        r'\b流感\b': '流行性感冒',
        r'\b高血压\b': '原发性高血压',
        r'\b低血压\b': '低血压症',
        r'\b糖尿病\b': '糖尿病',
        r'\b心脏病\b': '心脏疾病',
        r'\b肺炎\b': '肺部感染',
        r'\b胃炎\b': '胃部炎症'
    }
    
    for pattern, replacement in disease_mapping.items():
        text = re.sub(pattern, replacement, text)
    
    return text


def remove_personal_info(text: str) -> str:
    """移除个人信息
    
    移除可能包含的个人隐私信息
    
    Args:
        text: 输入文本
        
    Returns:
        清洗后的文本
    """
    if not isinstance(text, str):
        return str(text)
    
    # 移除身份证号
    text = re.sub(r'\b\d{17}[\dXx]\b', '[身份证号]', text)
    
    # 移除手机号
    text = re.sub(r'\b1[3-9]\d{9}\b', '[手机号]', text)
    
    # 移除邮箱
    text = re.sub(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', '[邮箱]', text)
    
    # 移除可能的姓名（2-4个中文字符，前后有特定标识）
    text = re.sub(r'患者[：:]?\s*[\u4e00-\u9fff]{2,4}', '患者：[姓名]', text)
    text = re.sub(r'姓名[：:]?\s*[\u4e00-\u9fff]{2,4}', '姓名：[姓名]', text)
    
    return text
