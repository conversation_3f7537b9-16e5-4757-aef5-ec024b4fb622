/* 聊天页面样式 */

/* 医生标签和优先级 */
.doctor-tag {
    display: inline-block;
    font-size: 0.7rem;
    padding: 1px 5px;
    background-color: var(--primary-color);
    color: #fff;
    border-radius: 4px;
    margin-left: 6px;
    vertical-align: middle;
}

.doctor-name {
    font-weight: 600;
    color: var(--primary-color);
    margin-right: 6px;
}

.history-item.high-priority {
    border-left: 3px solid var(--error-color);
}

.message-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-top: 3px;
}

.session-id-preview {
    opacity: 0.6;
    font-size: 0.7rem;
}

/* 侧边栏会话历史样式 */
.history-section-title {
    color: var(--text-muted);
    font-size: 0.8rem;
    margin: 10px 0 8px;
    padding: 0 10px;
}

.history-placeholder {
    color: var(--text-muted);
    font-size: 0.9rem;
    text-align: center;
    padding: 20px 0;
}

/* 修改现有样式 */
.chat-message {
    margin-bottom: 16px;
    display: flex;
}

.doctor-message {
    margin-bottom: 16px;
    display: flex;
}

.doctor-message .message-content {
    background-color: var(--secondary-light);
    border: 1px solid var(--secondary-color);
}

/* 消息时间显示 */
.message-time {
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* 历史会话删除功能样式 */
.history-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    margin-bottom: 8px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.history-content {
    flex: 1;
    min-width: 0; /* 允许内容收缩 */
}

.history-actions {
    flex-shrink: 0;
    margin-left: 8px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.history-item:hover .history-actions {
    opacity: 1;
}

.delete-session-btn {
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.delete-session-btn:hover {
    background-color: #dc3545;
    color: white;
    transform: scale(1.1);
}

.history-item:hover {
    background-color: #f8f9fa;
    transform: translateX(2px);
}