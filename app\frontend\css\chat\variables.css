/* 聊天界面CSS变量定义 */
:root {
    /* 简约高级配色 */
    --primary-color: #2c5282;
    --secondary-color: #4c6ef5;
    --accent-color: #5a67d8;
    --success-color: #38a169;
    --warning-color: #dd6b20;
    --danger-color: #e53e3e;
    
    /* 简约渐变 */
    --primary-gradient: linear-gradient(to right, #2c5282, #3182ce);
    --secondary-gradient: linear-gradient(to right, #4c6ef5, #6b8cff);
    --success-gradient: linear-gradient(to right, #38a169, #48bb78);
    
    /* 消息气泡配色 */
    --user-message-bg: #3182ce;
    --bot-message-bg: #ffffff;
    --doctor-message-bg: #38a169;
    --user-message-text: #ffffff;
    --bot-message-text: #1e293b;
    --doctor-message-text: #ffffff;
    
    /* 中性色 */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* 背景系统 */
    --chat-bg: #f8fafc;
    --header-bg: #2c5282;
    --input-bg: #ffffff;
    
    /* 阴影系统 */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* 边框半径 */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;
    
    /* 动画时长 */
    --duration-fast: 0.15s;
    --duration-normal: 0.3s;
    --duration-slow: 0.5s;
} 